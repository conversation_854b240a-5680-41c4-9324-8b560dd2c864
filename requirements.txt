# 泵组优化程序依赖包列表 (Windows环境)

# 核心数据处理
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# 数据库相关
sqlalchemy>=1.4.0
pymysql>=1.0.0
alembic>=1.8.0

# Redis缓存 (兼容Redis 3.0.504)
redis>=3.5.0,<4.0.0

# 配置文件处理
PyYAML>=6.0
python-dotenv>=0.20.0
configparser>=5.3.0
jsonschema>=4.17.0

# 日志和监控
colorlog>=6.7.0
psutil>=5.9.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2022.1

# 数据验证
cerberus>=1.3.4

# 并发处理
concurrent-futures>=3.1.1

# 网络和API
requests>=2.28.0

# Windows特定依赖
pywin32>=304  # Windows服务支持
wmi>=1.5.1    # Windows系统信息

# 性能优化
lz4>=4.0.0
msgpack>=1.0.4

# 开发工具已移动到 dev-requirements.txt
# 安装开发依赖: pip install -r dev-requirements.txt
