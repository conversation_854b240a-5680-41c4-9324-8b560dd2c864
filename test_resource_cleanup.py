"""
简单的资源清理测试脚本
"""

import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_resource_manager():
    """测试资源管理器"""
    print("=" * 50)
    print("测试资源管理器")
    print("=" * 50)
    
    try:
        from src.utils.resource_manager import ResourceManager
        
        # 创建资源管理器
        rm = ResourceManager()
        print(f"[OK] 资源管理器创建成功")
        
        # 获取初始统计
        stats = rm.get_resource_stats()
        print(f"[OK] 初始资源统计: {stats}")

        # 创建临时文件
        temp_file = rm.create_temp_file(suffix='.test', prefix='resource_test_')
        print(f"[OK] 创建临时文件: {temp_file}")

        # 写入测试数据
        with open(temp_file, 'w') as f:
            f.write("这是一个测试文件\n")
        print(f"[OK] 写入测试数据成功")

        # 创建临时目录
        temp_dir = rm.create_temp_dir(suffix='_test', prefix='resource_test_')
        print(f"[OK] 创建临时目录: {temp_dir}")

        # 在临时目录中创建文件
        test_file = Path(temp_dir) / "test.txt"
        with open(test_file, 'w') as f:
            f.write("临时目录中的文件\n")
        print(f"[OK] 在临时目录中创建文件: {test_file}")

        # 检查文件是否存在
        print(f"[OK] 临时文件存在: {os.path.exists(temp_file)}")
        print(f"[OK] 临时目录存在: {os.path.exists(temp_dir)}")
        print(f"[OK] 目录中文件存在: {os.path.exists(test_file)}")

        # 获取清理前统计
        stats_before = rm.get_resource_stats()
        print(f"[OK] 清理前资源统计: {stats_before}")

        # 清理资源
        print("开始清理资源...")
        rm.cleanup_all_resources()
        print("[OK] 资源清理完成")

        # 检查文件是否被删除
        print(f"[OK] 临时文件已删除: {not os.path.exists(temp_file)}")
        print(f"[OK] 临时目录已删除: {not os.path.exists(temp_dir)}")

        # 获取清理后统计
        stats_after = rm.get_resource_stats()
        print(f"[OK] 清理后资源统计: {stats_after}")

        print("[PASS] 资源管理器测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 资源管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_context_manager():
    """测试上下文管理器"""
    print("\n" + "=" * 50)
    print("测试上下文管理器")
    print("=" * 50)
    
    try:
        from src.utils.resource_manager import ResourceManager
        
        print("测试with语句...")
        with ResourceManager() as rm:
            print("[OK] 进入上下文管理器")

            # 创建一些资源
            temp_file = rm.create_temp_file(suffix='.ctx_test')
            print(f"[OK] 在上下文中创建临时文件: {temp_file}")

            # 写入数据
            with open(temp_file, 'w') as f:
                f.write("上下文管理器测试\n")

            print(f"[OK] 文件存在: {os.path.exists(temp_file)}")

        print("[OK] 退出上下文管理器")
        print(f"[OK] 文件已自动删除: {not os.path.exists(temp_file)}")

        print("[PASS] 上下文管理器测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 上下文管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_manager():
    """测试数据库管理器（不需要实际连接）"""
    print("\n" + "=" * 50)
    print("测试数据库管理器资源清理")
    print("=" * 50)
    
    try:
        from src.core.database_manager import DatabaseManager
        
        # 测试上下文管理器功能（不实际连接数据库）
        print("测试数据库管理器上下文管理器...")
        
        # 创建实例但不初始化连接
        db_manager = DatabaseManager.__new__(DatabaseManager)
        db_manager.logger = None
        db_manager.engine = None
        db_manager.is_connected = False
        
        # 测试清理方法
        if hasattr(db_manager, 'cleanup_resources'):
            print("[OK] 数据库管理器有cleanup_resources方法")
            db_manager.cleanup_resources()
            print("[OK] cleanup_resources方法调用成功")
        else:
            print("[FAIL] 数据库管理器缺少cleanup_resources方法")
            return False

        # 测试上下文管理器方法
        if hasattr(db_manager, '__enter__') and hasattr(db_manager, '__exit__'):
            print("[OK] 数据库管理器支持上下文管理器")
        else:
            print("[FAIL] 数据库管理器不支持上下文管理器")
            return False

        print("[PASS] 数据库管理器资源清理测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 数据库管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始资源清理机制测试")
    print("时间:", __import__('datetime').datetime.now())
    
    results = []
    
    # 测试资源管理器
    results.append(test_resource_manager())
    
    # 测试上下文管理器
    results.append(test_context_manager())
    
    # 测试数据库管理器
    results.append(test_database_manager())
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("[SUCCESS] 所有测试通过！资源清理机制实现成功！")
        return True
    else:
        print("[WARNING] 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
