#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备ID缓存管理器
内存友好的设备ID缓存系统，支持LRU策略和大小限制
"""

import threading
from typing import Dict, Optional, Any, List, Tuple
from collections import OrderedDict
import logging
import gc
import psutil

class DeviceIDCache:
    """
    内存友好的设备ID缓存管理器
    
    特性:
    - LRU (Least Recently Used) 缓存策略
    - 内存大小限制和监控
    - 线程安全
    - 自动清理机制
    - 性能统计
    """
    
    def __init__(self, 
                 max_cache_size: int = 500,
                 memory_limit_mb: float = 50.0,
                 auto_cleanup: bool = True,
                 cleanup_threshold: float = 0.8):
        """
        初始化设备ID缓存管理器
        
        Args:
            max_cache_size: 最大缓存条目数 (默认500个设备)
            memory_limit_mb: 内存限制MB (默认50MB)
            auto_cleanup: 是否启用自动清理 (默认True)
            cleanup_threshold: 清理阈值 (默认80%)
        """
        self.max_cache_size = max_cache_size
        self.memory_limit_mb = memory_limit_mb
        self.auto_cleanup = auto_cleanup
        self.cleanup_threshold = cleanup_threshold
        
        # 缓存存储 - 使用OrderedDict实现LRU
        self._cache: OrderedDict[str, int] = OrderedDict()
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 性能统计
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'cleanups': 0,
            'memory_warnings': 0,
            'total_queries': 0
        }
        
        # 内存监控
        self._process = psutil.Process()
        self._initial_memory = self._get_memory_usage()
        
        # 日志记录器
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"设备ID缓存管理器初始化完成:")
        self.logger.info(f"  最大缓存大小: {max_cache_size}个设备")
        self.logger.info(f"  内存限制: {memory_limit_mb}MB")
        self.logger.info(f"  自动清理: {auto_cleanup}")
        self.logger.info(f"  清理阈值: {cleanup_threshold*100}%")
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量(MB)"""
        try:
            return self._process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def _get_cache_memory_usage(self) -> float:
        """估算缓存内存使用量(MB)"""
        if not self._cache:
            return 0.0
        
        # 估算每个缓存条目的内存使用
        # 设备名称(平均30字符) + 设备ID(4字节) + OrderedDict开销
        avg_device_name_size = 30  # 字符
        device_id_size = 4  # 字节
        orderdict_overhead = 64  # 字节 (估算)
        
        entry_size = (avg_device_name_size * 2) + device_id_size + orderdict_overhead  # UTF-8编码
        total_size = len(self._cache) * entry_size
        
        return total_size / 1024 / 1024  # 转换为MB
    
    def _should_cleanup(self) -> bool:
        """检查是否需要清理缓存"""
        if not self.auto_cleanup:
            return False
        
        # 检查缓存大小
        if len(self._cache) >= self.max_cache_size * self.cleanup_threshold:
            return True
        
        # 检查内存使用
        current_memory = self._get_memory_usage()
        cache_memory = self._get_cache_memory_usage()
        
        if cache_memory >= self.memory_limit_mb * self.cleanup_threshold:
            return True
        
        return False
    
    def _cleanup_cache(self, target_size: Optional[int] = None) -> int:
        """
        清理缓存，移除最少使用的条目
        
        Args:
            target_size: 目标缓存大小，如果为None则清理到阈值以下
            
        Returns:
            清理的条目数
        """
        if not self._cache:
            return 0
        
        if target_size is None:
            target_size = int(self.max_cache_size * 0.7)  # 清理到70%
        
        removed_count = 0
        
        # 移除最少使用的条目 (OrderedDict的开头)
        while len(self._cache) > target_size and self._cache:
            device_name, device_id = self._cache.popitem(last=False)
            removed_count += 1
            self._stats['evictions'] += 1
        
        if removed_count > 0:
            self._stats['cleanups'] += 1
            self.logger.debug(f"缓存清理完成: 移除{removed_count}个条目, 当前大小: {len(self._cache)}")
        
        return removed_count
    
    def get_device_id(self, device_name: str) -> Optional[int]:
        """
        从缓存获取设备ID
        
        Args:
            device_name: 设备名称
            
        Returns:
            设备ID，如果不存在返回None
        """
        if not device_name:
            return None
        
        with self._lock:
            self._stats['total_queries'] += 1
            
            if device_name in self._cache:
                # 缓存命中 - 移动到末尾 (最近使用)
                device_id = self._cache.pop(device_name)
                self._cache[device_name] = device_id
                self._stats['hits'] += 1
                
                self.logger.debug(f"缓存命中: {device_name} -> {device_id}")
                return device_id
            else:
                # 缓存未命中
                self._stats['misses'] += 1
                self.logger.debug(f"缓存未命中: {device_name}")
                return None
    
    def set_device_id(self, device_name: str, device_id: int) -> bool:
        """
        设置设备ID到缓存
        
        Args:
            device_name: 设备名称
            device_id: 设备ID
            
        Returns:
            是否设置成功
        """
        if not device_name or device_id is None:
            return False
        
        with self._lock:
            # 检查是否需要清理
            if self._should_cleanup():
                self._cleanup_cache()
            
            # 检查内存限制
            cache_memory = self._get_cache_memory_usage()
            if cache_memory >= self.memory_limit_mb:
                self._stats['memory_warnings'] += 1
                self.logger.warning(f"缓存内存使用超限: {cache_memory:.2f}MB >= {self.memory_limit_mb}MB")
                
                # 强制清理到50%
                self._cleanup_cache(target_size=int(self.max_cache_size * 0.5))
            
            # 如果设备已存在，更新位置
            if device_name in self._cache:
                self._cache.pop(device_name)
            
            # 添加到末尾 (最近使用)
            self._cache[device_name] = device_id
            
            self.logger.debug(f"缓存设置: {device_name} -> {device_id}")
            return True
    
    def preload_devices(self, device_mapping: Dict[str, int]) -> int:
        """
        预加载设备映射
        
        Args:
            device_mapping: 设备名称到ID的映射
            
        Returns:
            成功加载的设备数
        """
        if not device_mapping:
            return 0
        
        loaded_count = 0
        
        with self._lock:
            for device_name, device_id in device_mapping.items():
                if self.set_device_id(device_name, device_id):
                    loaded_count += 1
        
        self.logger.info(f"预加载设备缓存完成: {loaded_count}/{len(device_mapping)}个设备")
        return loaded_count
    
    def clear_cache(self) -> int:
        """
        清空所有缓存

        Returns:
            清理的条目数
        """
        with self._lock:
            count = len(self._cache)
            self._cache.clear()

            # 重置统计信息
            self._stats['evictions'] += count
            self._stats['cleanups'] += 1

            # 强制垃圾回收
            collected = gc.collect()

            self.logger.info(f"设备ID缓存已清空: 移除{count}个条目, 垃圾回收{collected}个对象")
            return count

    def cleanup_resources(self):
        """清理所有资源"""
        self.logger.info("开始清理DeviceIDCache资源")

        try:
            # 清空缓存
            cleared_count = self.clear_cache()

            # 重置统计信息
            self._stats = {
                'hits': 0,
                'misses': 0,
                'evictions': 0,
                'cleanups': 0,
                'memory_warnings': 0,
                'total_queries': 0
            }

            self.logger.info(f"DeviceIDCache资源清理完成: 清理{cleared_count}个缓存条目")

        except Exception as e:
            self.logger.error(f"DeviceIDCache资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        self.cleanup_resources()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息字典
        """
        with self._lock:
            total_queries = self._stats['total_queries']
            hit_rate = (self._stats['hits'] / total_queries * 100) if total_queries > 0 else 0.0
            
            current_memory = self._get_memory_usage()
            cache_memory = self._get_cache_memory_usage()
            
            return {
                'cache_size': len(self._cache),
                'max_cache_size': self.max_cache_size,
                'hit_rate': hit_rate,
                'total_queries': total_queries,
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'evictions': self._stats['evictions'],
                'cleanups': self._stats['cleanups'],
                'memory_warnings': self._stats['memory_warnings'],
                'cache_memory_mb': cache_memory,
                'memory_limit_mb': self.memory_limit_mb,
                'current_memory_mb': current_memory,
                'memory_usage_percent': (cache_memory / self.memory_limit_mb * 100) if self.memory_limit_mb > 0 else 0.0
            }
    
    def get_cached_devices(self) -> List[Tuple[str, int]]:
        """
        获取所有缓存的设备
        
        Returns:
            设备名称和ID的列表
        """
        with self._lock:
            return list(self._cache.items())
    
    def optimize_memory(self) -> Dict[str, Any]:
        """
        优化内存使用
        
        Returns:
            优化结果统计
        """
        with self._lock:
            before_memory = self._get_memory_usage()
            before_cache_memory = self._get_cache_memory_usage()
            before_size = len(self._cache)
            
            # 清理缓存到50%
            removed_count = self._cleanup_cache(target_size=int(self.max_cache_size * 0.5))
            
            # 强制垃圾回收
            gc.collect()
            
            after_memory = self._get_memory_usage()
            after_cache_memory = self._get_cache_memory_usage()
            after_size = len(self._cache)
            
            result = {
                'removed_entries': removed_count,
                'before_cache_size': before_size,
                'after_cache_size': after_size,
                'before_cache_memory_mb': before_cache_memory,
                'after_cache_memory_mb': after_cache_memory,
                'before_total_memory_mb': before_memory,
                'after_total_memory_mb': after_memory,
                'memory_saved_mb': before_cache_memory - after_cache_memory
            }
            
            self.logger.info(f"内存优化完成: 移除{removed_count}个条目, 节省{result['memory_saved_mb']:.2f}MB")
            return result
    
    def __len__(self) -> int:
        """返回缓存大小"""
        return len(self._cache)
    
    def __contains__(self, device_name: str) -> bool:
        """检查设备是否在缓存中"""
        with self._lock:
            return device_name in self._cache
    
    def __repr__(self) -> str:
        """字符串表示"""
        stats = self.get_cache_stats()
        return (f"DeviceIDCache(size={stats['cache_size']}/{stats['max_cache_size']}, "
                f"hit_rate={stats['hit_rate']:.1f}%, "
                f"memory={stats['cache_memory_mb']:.2f}/{stats['memory_limit_mb']}MB)")


# 全局单例实例
_device_cache_instance: Optional[DeviceIDCache] = None
_cache_lock = threading.Lock()


def get_device_cache(max_cache_size: int = 500,
                    memory_limit_mb: float = 50.0,
                    auto_cleanup: bool = True) -> DeviceIDCache:
    """
    获取设备ID缓存管理器单例实例
    
    Args:
        max_cache_size: 最大缓存大小
        memory_limit_mb: 内存限制MB
        auto_cleanup: 是否启用自动清理
        
    Returns:
        设备ID缓存管理器实例
    """
    global _device_cache_instance
    
    with _cache_lock:
        if _device_cache_instance is None:
            _device_cache_instance = DeviceIDCache(
                max_cache_size=max_cache_size,
                memory_limit_mb=memory_limit_mb,
                auto_cleanup=auto_cleanup
            )
        
        return _device_cache_instance


def reset_device_cache():
    """重置设备ID缓存管理器单例"""
    global _device_cache_instance
    
    with _cache_lock:
        if _device_cache_instance is not None:
            _device_cache_instance.clear_cache()
            _device_cache_instance = None
