-- 为pump_data和main_pipe_data表添加唯一约束，支持跨文件参数合并
-- 执行前请备份数据库

USE pump_optimization;

-- 1. 检查是否存在重复数据
SELECT '检查pump_data表重复数据' as step;
SELECT device_id, data_time, COUNT(*) as count 
FROM pump_data 
GROUP BY device_id, data_time 
HAVING COUNT(*) > 1 
LIMIT 10;

SELECT '检查main_pipe_data表重复数据' as step;
SELECT device_id, data_time, COUNT(*) as count 
FROM main_pipe_data 
GROUP BY device_id, data_time 
HAVING COUNT(*) > 1 
LIMIT 10;

-- 2. 如果存在重复数据，先清理（保留最新的记录）
-- 注意：这会删除重复数据，请确认后执行

-- 清理pump_data重复数据
DELETE p1 FROM pump_data p1
INNER JOIN pump_data p2 
WHERE p1.device_id = p2.device_id 
  AND p1.data_time = p2.data_time 
  AND p1.id < p2.id;

-- 清理main_pipe_data重复数据  
DELETE m1 FROM main_pipe_data m1
INNER JOIN main_pipe_data m2 
WHERE m1.device_id = m2.device_id 
  AND m1.data_time = m2.data_time 
  AND m1.id < m2.id;

-- 3. 添加唯一约束
SELECT '添加pump_data唯一约束' as step;
ALTER TABLE pump_data 
ADD UNIQUE KEY uk_pump_device_time (device_id, data_time);

SELECT '添加main_pipe_data唯一约束' as step;
ALTER TABLE main_pipe_data 
ADD UNIQUE KEY uk_pipe_device_time (device_id, data_time);

-- 4. 验证约束添加成功
SELECT '验证约束' as step;
SHOW INDEX FROM pump_data WHERE Key_name = 'uk_pump_device_time';
SHOW INDEX FROM main_pipe_data WHERE Key_name = 'uk_pipe_device_time';

SELECT '约束添加完成' as result;
