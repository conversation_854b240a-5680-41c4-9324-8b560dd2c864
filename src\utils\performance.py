# -*- coding: utf-8 -*-
"""
性能监控模块
提供函数执行时间监控和系统资源监控功能
"""

import time
import psutil
import threading
from datetime import datetime
from functools import wraps
from typing import Dict, Any, Optional, Callable
# 处理导入问题
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from src.utils.logger import get_logger
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from .logger import get_logger


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.performance_data: Dict[str, Dict] = {}
        self._lock = threading.Lock()

        # 添加数据清理配置
        self.max_records_per_function = 100  # 限制每个函数的记录数
        self.cleanup_interval = 1800  # 30分钟清理一次
        self.last_cleanup = time.time()

        self.logger.info("性能监控器初始化完成（已启用数据清理）")
    
    def monitor_function(self, func_name: str = None):
        """函数性能监控装饰器"""
        def decorator(func: Callable):
            nonlocal func_name
            if func_name is None:
                func_name = f"{func.__module__}.{func.__name__}"
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                return self._execute_with_monitoring(func, func_name, *args, **kwargs)
            
            return wrapper
        return decorator
    
    def _execute_with_monitoring(self, func: Callable, func_name: str, *args, **kwargs):
        """执行函数并监控性能"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        self.logger.debug(f"开始执行函数: {func_name}")

        try:
            # 执行函数
            result = func(*args, **kwargs)

            # 计算性能指标
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

            execution_time = end_time - start_time
            memory_delta = end_memory - start_memory

            # 定期清理性能数据
            current_time = time.time()
            if current_time - self.last_cleanup > self.cleanup_interval:
                self._cleanup_performance_data()
                self.last_cleanup = current_time
            
            # 记录性能数据
            performance_info = {
                'execution_time': execution_time,
                'start_memory': start_memory,
                'end_memory': end_memory,
                'memory_delta': memory_delta,
                'timestamp': datetime.now(),
                'status': 'success'
            }
            
            with self._lock:
                if func_name not in self.performance_data:
                    self.performance_data[func_name] = []
                self.performance_data[func_name].append(performance_info)
            
            # 记录日志
            self.logger.info(f"函数执行完成: {func_name}, 耗时: {execution_time:.3f}秒, 内存变化: {memory_delta:+.2f}MB")
            
            # 如果执行时间过长，记录警告
            if execution_time > 90:  # 超过90秒（调整后的阈值，适合大文件处理）
                self.logger.warning(f"函数执行时间过长: {func_name}, 耗时: {execution_time:.3f}秒")
            
            return result
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 记录失败的性能数据
            performance_info = {
                'execution_time': execution_time,
                'start_memory': start_memory,
                'end_memory': psutil.Process().memory_info().rss / 1024 / 1024,
                'memory_delta': 0,
                'timestamp': datetime.now(),
                'status': 'failed',
                'error': str(e)
            }
            
            with self._lock:
                if func_name not in self.performance_data:
                    self.performance_data[func_name] = []
                self.performance_data[func_name].append(performance_info)
            
            self.logger.error(f"函数执行失败: {func_name}, 耗时: {execution_time:.3f}秒, 错误: {e}")
            raise
    
    def get_performance_summary(self, func_name: str = None) -> Dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            if func_name:
                if func_name not in self.performance_data:
                    return {'error': f'未找到函数 {func_name} 的性能数据'}
                
                data = self.performance_data[func_name]
                return self._calculate_summary(func_name, data)
            else:
                # 返回所有函数的摘要
                summary = {}
                for name, data in self.performance_data.items():
                    summary[name] = self._calculate_summary(name, data)
                return summary
    
    def _calculate_summary(self, func_name: str, data: list) -> Dict[str, Any]:
        """计算性能摘要统计"""
        if not data:
            return {'error': '无性能数据'}
        
        successful_calls = [d for d in data if d['status'] == 'success']
        failed_calls = [d for d in data if d['status'] == 'failed']
        
        if not successful_calls:
            return {
                'function_name': func_name,
                'total_calls': len(data),
                'successful_calls': 0,
                'failed_calls': len(failed_calls),
                'success_rate': 0.0,
                'error': '无成功执行记录'
            }
        
        execution_times = [d['execution_time'] for d in successful_calls]
        memory_deltas = [d['memory_delta'] for d in successful_calls]
        
        return {
            'function_name': func_name,
            'total_calls': len(data),
            'successful_calls': len(successful_calls),
            'failed_calls': len(failed_calls),
            'success_rate': len(successful_calls) / len(data),
            'avg_execution_time': sum(execution_times) / len(execution_times),
            'min_execution_time': min(execution_times),
            'max_execution_time': max(execution_times),
            'avg_memory_delta': sum(memory_deltas) / len(memory_deltas),
            'total_memory_delta': sum(memory_deltas),
            'last_execution': data[-1]['timestamp']
        }
    
    def log_system_resources(self):
        """记录当前系统资源使用情况"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            self.logger.info(f"系统资源使用情况:")
            self.logger.info(f"  CPU使用率: {cpu_percent:.1f}%")
            self.logger.info(f"  内存使用率: {memory.percent:.1f}% ({memory.used/1024/1024/1024:.2f}GB/{memory.total/1024/1024/1024:.2f}GB)")
            self.logger.info(f"  磁盘使用率: {disk.percent:.1f}% ({disk.used/1024/1024/1024:.2f}GB/{disk.total/1024/1024/1024:.2f}GB)")
            
        except Exception as e:
            self.logger.error(f"获取系统资源信息失败: {e}")
    
    def clear_performance_data(self, func_name: str = None):
        """清空性能数据"""
        with self._lock:
            if func_name:
                if func_name in self.performance_data:
                    del self.performance_data[func_name]
                    self.logger.info(f"已清空函数 {func_name} 的性能数据")
            else:
                self.performance_data.clear()
                self.logger.info("已清空所有性能数据")

    def _cleanup_performance_data(self):
        """清理过期的性能数据，防止内存泄漏"""
        with self._lock:
            cleaned_functions = 0
            for func_name, func_data in list(self.performance_data.items()):
                if 'records' in func_data and len(func_data['records']) > self.max_records_per_function:
                    # 保留最新的记录
                    func_data['records'] = func_data['records'][-self.max_records_per_function:]
                    cleaned_functions += 1
                    self.logger.debug(f"清理函数 {func_name} 的性能数据，保留最新 {self.max_records_per_function} 条")

            if cleaned_functions > 0:
                self.logger.info(f"性能数据清理完成，清理了 {cleaned_functions} 个函数的历史数据")


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def performance_monitor_decorator(func_name: str = None):
    """性能监控装饰器的便捷函数"""
    return performance_monitor.monitor_function(func_name)


# 简化的装饰器别名
monitor_performance = performance_monitor_decorator


if __name__ == "__main__":
    # 测试性能监控
    import time
    
    @monitor_performance("test_function")
    def test_function(duration: float):
        """测试函数"""
        logger = get_logger("test_performance")
        logger.info(f"测试函数开始，将运行 {duration} 秒")
        time.sleep(duration)
        logger.info("测试函数完成")
        return f"运行了 {duration} 秒"
    
    @monitor_performance()
    def test_memory_function():
        """测试内存使用函数"""
        logger = get_logger("test_performance")
        logger.info("创建大量数据测试内存使用")
        
        # 创建一些数据
        data = [i for i in range(100000)]
        logger.info(f"创建了 {len(data)} 个数据项")
        
        return len(data)
    
    # 执行测试
    logger = get_logger("performance_test")
    logger.info("性能监控测试开始")
    
    # 测试正常执行
    result1 = test_function(0.5)
    logger.info(f"测试结果1: {result1}")
    
    result2 = test_memory_function()
    logger.info(f"测试结果2: {result2}")
    
    # 测试异常情况
    @monitor_performance("test_error_function")
    def test_error_function():
        raise ValueError("测试异常")
    
    try:
        test_error_function()
    except ValueError:
        logger.info("异常测试完成")
    
    # 记录系统资源
    performance_monitor.log_system_resources()
    
    # 获取性能摘要
    summary = performance_monitor.get_performance_summary()
    logger.info("性能摘要:")
    for func_name, stats in summary.items():
        if 'error' in stats:
            logger.info(f"  {func_name}: {stats['error']}")
        else:
            avg_time = stats.get('avg_execution_time', 0)
            logger.info(f"  {func_name}: 调用{stats['total_calls']}次, 成功率{stats['success_rate']:.2%}, 平均耗时{avg_time:.3f}秒")
    
    logger.info("性能监控测试完成")
