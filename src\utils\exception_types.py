"""
异常类型定义
定义系统中使用的异常类型、严重程度和分类
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误分类"""
    DATABASE = "database"
    FILE_IO = "file_io"
    NETWORK = "network"
    VALIDATION = "validation"
    BUSINESS = "business"
    SYSTEM = "system"


class RetryStrategy(Enum):
    """重试策略"""
    NO_RETRY = "no_retry"
    IMMEDIATE = "immediate"
    LINEAR_BACKOFF = "linear_backoff"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    CUSTOM = "custom"


@dataclass
class ExceptionInfo:
    """异常信息"""
    exception: Exception
    category: ErrorCategory
    severity: ErrorSeverity
    timestamp: datetime
    context: Dict[str, Any]
    retry_count: int = 0
    message: str = ""
    stack_trace: str = ""
    
    def __post_init__(self):
        if not self.message:
            self.message = str(self.exception)
        if not self.timestamp:
            self.timestamp = datetime.now()


# 异常严重程度映射
EXCEPTION_SEVERITY_MAP = {
    # 系统级严重异常
    'MemoryError': ErrorSeverity.CRITICAL,
    'SystemError': ErrorSeverity.CRITICAL,
    'KeyboardInterrupt': ErrorSeverity.CRITICAL,
    
    # 数据库相关异常
    'pymysql.err.OperationalError': ErrorSeverity.HIGH,
    'sqlalchemy.exc.OperationalError': ErrorSeverity.HIGH,
    'sqlalchemy.exc.DatabaseError': ErrorSeverity.HIGH,
    'ConnectionError': ErrorSeverity.HIGH,
    
    # 文件I/O异常
    'FileNotFoundError': ErrorSeverity.MEDIUM,
    'PermissionError': ErrorSeverity.HIGH,
    'OSError': ErrorSeverity.MEDIUM,
    'IOError': ErrorSeverity.MEDIUM,
    'UnicodeDecodeError': ErrorSeverity.LOW,
    
    # 网络异常
    'requests.exceptions.ConnectionError': ErrorSeverity.MEDIUM,
    'requests.exceptions.Timeout': ErrorSeverity.MEDIUM,
    'urllib3.exceptions.ConnectionError': ErrorSeverity.MEDIUM,
    'socket.error': ErrorSeverity.MEDIUM,
    
    # 验证异常
    'ValueError': ErrorSeverity.LOW,
    'TypeError': ErrorSeverity.LOW,
    'KeyError': ErrorSeverity.LOW,
    'AttributeError': ErrorSeverity.LOW,
    
    # 业务逻辑异常
    'BusinessLogicError': ErrorSeverity.MEDIUM,
    'DataValidationError': ErrorSeverity.MEDIUM,
    'ConfigurationError': ErrorSeverity.HIGH,
}


# 异常分类映射
EXCEPTION_CATEGORY_MAP = {
    # 数据库异常
    'pymysql.err.OperationalError': ErrorCategory.DATABASE,
    'sqlalchemy.exc.OperationalError': ErrorCategory.DATABASE,
    'sqlalchemy.exc.DatabaseError': ErrorCategory.DATABASE,
    'ConnectionError': ErrorCategory.DATABASE,
    
    # 文件I/O异常
    'FileNotFoundError': ErrorCategory.FILE_IO,
    'PermissionError': ErrorCategory.FILE_IO,
    'OSError': ErrorCategory.FILE_IO,
    'IOError': ErrorCategory.FILE_IO,
    'UnicodeDecodeError': ErrorCategory.FILE_IO,
    
    # 网络异常
    'requests.exceptions.ConnectionError': ErrorCategory.NETWORK,
    'requests.exceptions.Timeout': ErrorCategory.NETWORK,
    'urllib3.exceptions.ConnectionError': ErrorCategory.NETWORK,
    'socket.error': ErrorCategory.NETWORK,
    
    # 验证异常
    'ValueError': ErrorCategory.VALIDATION,
    'TypeError': ErrorCategory.VALIDATION,
    'KeyError': ErrorCategory.VALIDATION,
    'AttributeError': ErrorCategory.VALIDATION,
    
    # 业务逻辑异常
    'BusinessLogicError': ErrorCategory.BUSINESS,
    'DataValidationError': ErrorCategory.BUSINESS,
    'ConfigurationError': ErrorCategory.BUSINESS,
    
    # 系统异常
    'MemoryError': ErrorCategory.SYSTEM,
    'SystemError': ErrorCategory.SYSTEM,
    'RuntimeError': ErrorCategory.SYSTEM,
    'KeyboardInterrupt': ErrorCategory.SYSTEM,
}


def get_exception_severity(exception: Exception) -> ErrorSeverity:
    """获取异常严重程度"""
    exception_type = type(exception).__name__
    full_exception_type = f"{type(exception).__module__}.{exception_type}"
    
    # 首先检查完整类型名
    severity = EXCEPTION_SEVERITY_MAP.get(full_exception_type)
    if severity:
        return severity
    
    # 然后检查简单类型名
    severity = EXCEPTION_SEVERITY_MAP.get(exception_type)
    if severity:
        return severity
    
    # 默认为中等严重程度
    return ErrorSeverity.MEDIUM


def get_exception_category(exception: Exception) -> ErrorCategory:
    """获取异常分类"""
    exception_type = type(exception).__name__
    full_exception_type = f"{type(exception).__module__}.{exception_type}"
    
    # 首先检查完整类型名
    category = EXCEPTION_CATEGORY_MAP.get(full_exception_type)
    if category:
        return category
    
    # 然后检查简单类型名
    category = EXCEPTION_CATEGORY_MAP.get(exception_type)
    if category:
        return category
    
    # 默认为系统异常
    return ErrorCategory.SYSTEM


def should_retry_exception(exception: Exception, attempt: int, max_attempts: int = 3) -> bool:
    """判断异常是否应该重试"""
    if attempt >= max_attempts:
        return False
    
    category = get_exception_category(exception)
    
    # 某些类型的异常不适合重试
    if category in [ErrorCategory.VALIDATION, ErrorCategory.BUSINESS]:
        return False
    
    # 严重异常通常不重试
    severity = get_exception_severity(exception)
    if severity == ErrorSeverity.CRITICAL:
        return False
    
    return True


def get_retry_delay(attempt: int, base_delay: float = 1.0, backoff_factor: float = 2.0) -> float:
    """计算重试延迟"""
    return base_delay * (backoff_factor ** attempt)
