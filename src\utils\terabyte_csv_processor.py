#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1TB CSV文件跨文件参数合并处理器
基于现有架构，支持大规模数据处理和跨文件参数合并
"""

import time
import threading
import gc
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
from datetime import datetime
from pathlib import Path

from src.utils.logger import get_logger
from src.utils.performance import monitor_performance
from src.core.database_manager import DatabaseManager
from src.core.config_manager import ConfigManager
from src.handlers.file_processor import FileProcessorHandler
from src.utils.exception_handler import handle_exceptions


class TerabyteCSVProcessor:
    """1TB级CSV文件处理器，支持跨文件参数合并"""
    
    def __init__(self, max_workers: int = 8, memory_limit_gb: float = 4.0):
        """
        初始化处理器
        
        Args:
            max_workers: 最大并发线程数
            memory_limit_gb: 内存使用限制(GB)
        """
        self.logger = get_logger(self.__class__.__name__)
        self.max_workers = max_workers
        self.memory_limit = memory_limit_gb * 1024 * 1024 * 1024  # 转换为字节
        
        # 初始化核心组件
        self.db_manager = DatabaseManager()
        self.config_manager = ConfigManager()

        # 内存监控配置
        self.memory_warning_threshold = self.memory_limit * 0.8  # 80%警告阈值
        self.memory_critical_threshold = self.memory_limit * 0.9  # 90%严重阈值
        self.memory_check_interval = 5  # 每5个文件检查一次内存
        self.gc_force_threshold = self.memory_limit * 0.85  # 85%强制垃圾回收

        # 处理统计
        self.stats = {
            'files_total': 0,
            'files_processed': 0,
            'files_failed': 0,
            'records_total': 0,
            'records_merged': 0,
            'processing_start_time': None,
            'processing_end_time': None,
            'memory_peak_mb': 0,
            'memory_warnings': 0,
            'memory_critical_events': 0,
            'gc_forced_count': 0
        }
        
        # 线程安全锁
        self.stats_lock = threading.Lock()
        
        self.logger.info(f"1TB CSV处理器初始化完成: 最大并发={max_workers}, 内存限制={memory_limit_gb}GB")
    
    @monitor_performance("process_csv_files")
    def process_csv_files(self, file_paths: List[str], batch_id: str = None) -> Dict[str, Any]:
        """
        处理CSV文件列表，支持跨文件参数合并
        
        Args:
            file_paths: CSV文件路径列表
            batch_id: 批次ID
            
        Returns:
            处理结果统计
        """
        if not batch_id:
            batch_id = f"terabyte_batch_{int(time.time())}"
            
        self.stats['processing_start_time'] = datetime.now()
        self.stats['files_total'] = len(file_paths)
        
        self.logger.info(f"开始处理1TB级CSV文件: {len(file_paths)} 个文件, 批次ID: {batch_id}")
        
        try:
            # 按设备分组文件，优化合并效率
            device_groups = self._group_files_by_device(file_paths)
            self.logger.info(f"文件分组完成: {len(device_groups)} 个设备组")
            
            # 并行处理设备组
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                
                for device_name, files in device_groups.items():
                    future = executor.submit(
                        self._process_device_group, 
                        device_name, files, batch_id
                    )
                    futures.append(future)
                
                # 等待所有任务完成
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        self._update_stats(result)
                    except Exception as e:
                        self.logger.error(f"设备组处理失败: {e}")
                        with self.stats_lock:
                            self.stats['files_failed'] += 1
            
            self.stats['processing_end_time'] = datetime.now()
            
            # 最终统计
            total_time = (self.stats['processing_end_time'] - self.stats['processing_start_time']).total_seconds()
            
            final_stats = {
                'success': True,
                'batch_id': batch_id,
                'files_total': self.stats['files_total'],
                'files_processed': self.stats['files_processed'],
                'files_failed': self.stats['files_failed'],
                'records_total': self.stats['records_total'],
                'records_merged': self.stats['records_merged'],
                'processing_time_seconds': total_time,
                'throughput_files_per_second': self.stats['files_processed'] / total_time if total_time > 0 else 0,
                'throughput_records_per_second': self.stats['records_total'] / total_time if total_time > 0 else 0,
                'memory_peak_mb': self.stats['memory_peak_mb'],
                'memory_warnings': self.stats['memory_warnings'],
                'memory_critical_events': self.stats['memory_critical_events'],
                'gc_forced_count': self.stats['gc_forced_count'],
                'memory_stats': self._get_memory_stats()
            }
            
            self.logger.info(f"1TB CSV处理完成: {final_stats}")
            return final_stats
            
        except Exception as e:
            self.logger.error(f"1TB CSV处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'batch_id': batch_id,
                'stats': self.stats
            }

    def cleanup_resources(self):
        """清理所有资源"""
        self.logger.info("开始清理TerabyteCSVProcessor资源")

        try:
            # 1. 清理数据库连接
            if hasattr(self, 'db_manager') and self.db_manager:
                self.logger.info("清理数据库管理器资源")
                self.db_manager.cleanup_resources()
                self.db_manager = None

            # 2. 清理配置管理器
            if hasattr(self, 'config_manager') and self.config_manager:
                self.logger.info("清理配置管理器资源")
                # ConfigManager可能需要清理文件监听器等
                self.config_manager = None

            # 3. 强制垃圾回收
            self.logger.info("执行最终垃圾回收")
            import gc
            collected = gc.collect()
            self.logger.info(f"垃圾回收完成，回收对象数: {collected}")

            # 4. 重置统计信息
            self.stats = {
                'files_processed': 0,
                'files_failed': 0,
                'records_total': 0,
                'records_merged': 0,
                'processing_time': 0.0,
                'memory_peak_mb': 0.0,
                'memory_warnings': 0,
                'memory_critical_events': 0,
                'gc_forced_count': 0
            }

            self.logger.info("TerabyteCSVProcessor资源清理完成")

        except Exception as e:
            self.logger.error(f"TerabyteCSVProcessor资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        self.cleanup_resources()

        if exc_type is not None:
            self.logger.error(f"TerabyteCSVProcessor退出时发生异常: {exc_type.__name__}: {exc_val}")

        return False  # 不抑制异常

    def _group_files_by_device(self, file_paths: List[str]) -> Dict[str, List[str]]:
        """按设备分组文件，优化合并效率"""
        device_groups = {}

        self.logger.info(f"开始文件分组: {len(file_paths)} 个文件")

        for file_path in file_paths:
            try:
                # 修复：使用正确的方法名获取文件映射信息
                mapping_info = self.config_manager.get_file_mapping(file_path)

                if mapping_info:
                    # 使用站点+设备的组合作为分组键，提高分组精度
                    station_id = mapping_info.get('station_id', 'unknown_station')
                    device_name = mapping_info.get('device_name', 'unknown_device')
                    group_key = f"{station_id}_{device_name}"

                    self.logger.debug(f"文件分组: {Path(file_path).name} -> {group_key}")
                else:
                    # 如果映射失败，使用文件名推断分组
                    group_key = self._infer_device_group_from_filename(file_path)
                    self.logger.warning(f"映射失败，使用推断分组: {Path(file_path).name} -> {group_key}")

                if group_key not in device_groups:
                    device_groups[group_key] = []

                device_groups[group_key].append(file_path)

            except Exception as e:
                self.logger.error(f"文件分组异常: {file_path}, 错误: {e}")
                # 放入unknown组
                if 'unknown' not in device_groups:
                    device_groups['unknown'] = []
                device_groups['unknown'].append(file_path)

        # 优化分组结果：合并小组，拆分大组
        optimized_groups = self._optimize_device_groups(device_groups)

        # 输出分组结果统计
        for group_key, files in optimized_groups.items():
            self.logger.info(f"设备组 [{group_key}]: {len(files)} 个文件")

        return optimized_groups

    def _infer_device_group_from_filename(self, file_path: str) -> str:
        """从文件名推断设备分组"""
        try:
            file_name = Path(file_path).name

            # 推断泵站
            station = "unknown_station"
            if "二期" in file_name:
                station = "二期供水泵房" if "供水" in file_name else "二期取水泵房"
            elif "一期" in file_name:
                station = "一期供水泵房" if "供水" in file_name else "一期取水泵房"

            # 推断设备
            device = "unknown_device"
            if "1#" in file_name:
                device = "1#泵"
            elif "2#" in file_name:
                device = "2#泵"
            elif "3#" in file_name:
                device = "3#泵"
            elif "4#" in file_name:
                device = "4#泵"
            elif "5#" in file_name:
                device = "5#泵"
            elif "6#" in file_name:
                device = "6#泵"
            elif "总管" in file_name or "main" in file_name.lower():
                device = "总管"

            return f"{station}_{device}"

        except Exception as e:
            self.logger.error(f"文件名推断失败: {file_path}, 错误: {e}")
            return "unknown"

    def _optimize_device_groups(self, device_groups: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """优化设备分组：合并小组，拆分大组"""
        optimized_groups = {}

        # 配置参数
        min_files_per_group = 2  # 最小文件数
        max_files_per_group = 20  # 最大文件数

        small_groups = []  # 收集小组

        for group_key, files in device_groups.items():
            file_count = len(files)

            if file_count < min_files_per_group:
                # 小组：收集起来后续合并
                small_groups.extend(files)
                self.logger.debug(f"小组收集: {group_key} ({file_count} 个文件)")

            elif file_count > max_files_per_group:
                # 大组：按文件类型拆分
                sub_groups = self._split_large_group(group_key, files, max_files_per_group)
                optimized_groups.update(sub_groups)
                self.logger.debug(f"大组拆分: {group_key} -> {len(sub_groups)} 个子组")

            else:
                # 合适大小的组：直接保留
                optimized_groups[group_key] = files

        # 处理小组：按文件类型合并
        if small_groups:
            merged_groups = self._merge_small_groups(small_groups, max_files_per_group)
            optimized_groups.update(merged_groups)
            self.logger.info(f"小组合并: {len(small_groups)} 个文件 -> {len(merged_groups)} 个合并组")

        return optimized_groups

    def _split_large_group(self, group_key: str, files: List[str], max_size: int) -> Dict[str, List[str]]:
        """拆分大组"""
        sub_groups = {}

        # 按文件类型分组
        type_groups = {}
        for file_path in files:
            file_type = self._get_file_type(file_path)
            if file_type not in type_groups:
                type_groups[file_type] = []
            type_groups[file_type].append(file_path)

        # 为每个类型创建子组
        for file_type, type_files in type_groups.items():
            # 如果类型文件仍然太多，按数量拆分
            for i in range(0, len(type_files), max_size):
                batch_files = type_files[i:i + max_size]
                sub_group_key = f"{group_key}_{file_type}_{i//max_size + 1}"
                sub_groups[sub_group_key] = batch_files

        return sub_groups

    def _merge_small_groups(self, files: List[str], max_size: int) -> Dict[str, List[str]]:
        """合并小组"""
        merged_groups = {}

        # 按文件类型分组
        type_groups = {}
        for file_path in files:
            file_type = self._get_file_type(file_path)
            if file_type not in type_groups:
                type_groups[file_type] = []
            type_groups[file_type].append(file_path)

        # 为每个类型创建合并组
        for file_type, type_files in type_groups.items():
            # 按最大大小分批
            for i in range(0, len(type_files), max_size):
                batch_files = type_files[i:i + max_size]
                merged_group_key = f"merged_{file_type}_{i//max_size + 1}"
                merged_groups[merged_group_key] = batch_files

        return merged_groups

    def _get_file_type(self, file_path: str) -> str:
        """获取文件类型用于分组"""
        file_name = Path(file_path).name.lower()

        # 根据文件名关键词判断类型
        if any(keyword in file_name for keyword in ['pump', '泵']):
            return 'pump'
        elif any(keyword in file_name for keyword in ['main', 'pipeline', '总管']):
            return 'pipeline'
        elif any(keyword in file_name for keyword in ['pressure', '压力']):
            return 'pressure'
        elif any(keyword in file_name for keyword in ['flow', '流量']):
            return 'flow'
        elif any(keyword in file_name for keyword in ['power', '功率']):
            return 'power'
        elif any(keyword in file_name for keyword in ['voltage', '电压']):
            return 'voltage'
        elif any(keyword in file_name for keyword in ['current', '电流']):
            return 'current'
        elif any(keyword in file_name for keyword in ['frequency', '频率']):
            return 'frequency'
        else:
            return 'other'

    def _process_device_group(self, device_name: str, file_paths: List[str], batch_id: str) -> Dict[str, Any]:
        """处理单个设备的所有文件"""
        thread_name = threading.current_thread().name
        self.logger.info(f"[{thread_name}] 开始处理设备组: {device_name}, {len(file_paths)} 个文件")
        
        group_stats = {
            'device_name': device_name,
            'files_processed': 0,
            'files_failed': 0,
            'records_total': 0,
            'processing_time': 0
        }
        
        start_time = time.time()
        
        # 创建设备专用的文件处理器
        file_processor = FileProcessorHandler(f"processor_{device_name}")

        try:
            for file_path in file_paths:
                try:
                    # 智能内存管理
                    memory_action = self._check_and_manage_memory(thread_name, group_stats['files_processed'])
                    if memory_action == 'skip':
                        self.logger.error(f"[{thread_name}] 内存不足，跳过文件: {file_path}")
                        group_stats['files_failed'] += 1
                        continue

                    # 修复：直接调用私有方法_process_file，因为没有公共的process_file方法
                    result = file_processor._process_file(file_path, station_id='unknown', batch_id=batch_id)

                    if result:
                        group_stats['files_processed'] += 1
                        group_stats['records_total'] += result.get('record_count', 0)

                        # 计算合并记录数（如果有数据转换）
                        if result.get('target_table') in ['pump_data', 'main_pipe_data']:
                            # 对于合并表，记录合并的记录数
                            merged_count = result.get('record_count', 0)
                            group_stats['records_merged'] = group_stats.get('records_merged', 0) + merged_count
                            self.logger.debug(f"[{thread_name}] 记录合并: {merged_count}条记录")
                    else:
                        group_stats['files_failed'] += 1
                        self.logger.warning(f"[{thread_name}] 文件处理失败: {file_path}")

                except Exception as e:
                    group_stats['files_failed'] += 1
                    self.logger.error(f"[{thread_name}] 文件处理异常: {file_path}, 错误: {e}")
            
            group_stats['processing_time'] = time.time() - start_time
            
            self.logger.info(f"[{thread_name}] 设备组处理完成: {device_name}, "
                           f"成功: {group_stats['files_processed']}, "
                           f"失败: {group_stats['files_failed']}, "
                           f"记录: {group_stats['records_total']}")
            
            return group_stats
            
        except Exception as e:
            self.logger.error(f"[{thread_name}] 设备组处理异常: {device_name}, 错误: {e}")
            group_stats['processing_time'] = time.time() - start_time
            return group_stats
    
    @handle_exceptions(context={'operation': 'memory_management'})
    def _check_and_manage_memory(self, thread_name: str, files_processed: int) -> str:
        """
        智能内存检查和管理

        Args:
            thread_name: 线程名称
            files_processed: 已处理文件数

        Returns:
            'continue': 继续处理
            'wait': 等待内存释放
            'skip': 跳过当前文件
        """
        try:
            # 定期检查内存（避免频繁检查影响性能）
            if files_processed % self.memory_check_interval != 0:
                return 'continue'

            process = psutil.Process()
            memory_info = process.memory_info()
            current_memory = memory_info.rss
            current_memory_mb = current_memory / (1024 * 1024)

            # 更新峰值内存统计（线程安全）
            with self.stats_lock:
                if current_memory_mb > self.stats['memory_peak_mb']:
                    self.stats['memory_peak_mb'] = current_memory_mb

            # 计算内存使用率
            memory_usage_ratio = current_memory / self.memory_limit

            # 严重内存压力 (>90%)
            if current_memory > self.memory_critical_threshold:
                with self.stats_lock:
                    self.stats['memory_critical_events'] += 1
                self.logger.error(f"[{thread_name}] 严重内存压力: {current_memory_mb:.1f}MB "
                                f"({memory_usage_ratio:.1%}), 限制: {self.memory_limit/(1024*1024*1024):.1f}GB")

                # 强制垃圾回收
                self._force_garbage_collection()

                # 再次检查内存
                new_memory = psutil.Process().memory_info().rss
                new_memory_mb = new_memory / (1024 * 1024)

                if new_memory > self.memory_critical_threshold:
                    self.logger.error(f"[{thread_name}] 垃圾回收后内存仍然过高: {new_memory_mb:.1f}MB，跳过当前文件")
                    return 'skip'
                else:
                    self.logger.info(f"[{thread_name}] 垃圾回收成功: {current_memory_mb:.1f}MB -> {new_memory_mb:.1f}MB")
                    return 'continue'

            # 警告级内存压力 (>80%)
            elif current_memory > self.memory_warning_threshold:
                with self.stats_lock:
                    self.stats['memory_warnings'] += 1
                self.logger.warning(f"[{thread_name}] 内存使用警告: {current_memory_mb:.1f}MB "
                                  f"({memory_usage_ratio:.1%})")

                # 强制垃圾回收阈值 (>85%)
                if current_memory > self.gc_force_threshold:
                    self._force_garbage_collection()

                return 'continue'

            # 正常内存使用
            else:
                self.logger.debug(f"[{thread_name}] 内存使用正常: {current_memory_mb:.1f}MB "
                                f"({memory_usage_ratio:.1%})")
                return 'continue'

        except Exception as e:
            self.logger.error(f"内存检查失败: {e}")
            return 'continue'  # 出错时继续处理，避免阻塞

    def _force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            with self.stats_lock:
                self.stats['gc_forced_count'] += 1
            self.logger.info("执行强制垃圾回收...")

            # 执行多轮垃圾回收
            collected = 0
            for i in range(3):
                collected += gc.collect()

            self.logger.info(f"垃圾回收完成，回收对象数: {collected}")

        except Exception as e:
            self.logger.error(f"强制垃圾回收失败: {e}")

    def _get_memory_stats(self) -> Dict[str, Any]:
        """获取内存统计信息"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                'current_memory_mb': memory_info.rss / (1024 * 1024),
                'memory_limit_mb': self.memory_limit / (1024 * 1024),
                'memory_usage_ratio': memory_info.rss / self.memory_limit,
                'memory_peak_mb': self.stats['memory_peak_mb'],
                'memory_warnings': self.stats['memory_warnings'],
                'memory_critical_events': self.stats['memory_critical_events'],
                'gc_forced_count': self.stats['gc_forced_count']
            }
        except Exception as e:
            self.logger.error(f"获取内存统计失败: {e}")
            return {}
    
    def _update_stats(self, group_result: Dict[str, Any]):
        """更新统计信息"""
        with self.stats_lock:
            self.stats['files_processed'] += group_result.get('files_processed', 0)
            self.stats['files_failed'] += group_result.get('files_failed', 0)
            self.stats['records_total'] += group_result.get('records_total', 0)
            self.stats['records_merged'] += group_result.get('records_merged', 0)  # 修复：添加合并记录统计
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        with self.stats_lock:
            return self.stats.copy()


# 使用示例
if __name__ == "__main__":
    # 创建处理器
    processor = TerabyteCSVProcessor(max_workers=8, memory_limit_gb=4.0)
    
    # 示例文件列表（实际使用时从目录扫描获取）
    csv_files = [
        "data/2天/_二期_供水泵房_1#加压泵__频率__反馈.csv",
        "data/2天/_二期_供水泵房_1#加压泵__有功功率__反馈.csv",
        # ... 更多文件
    ]
    
    # 处理文件
    result = processor.process_csv_files(csv_files)
    print(f"处理结果: {result}")
