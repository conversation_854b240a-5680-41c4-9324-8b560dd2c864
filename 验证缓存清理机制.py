#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存清理机制验证脚本
通过主程序初始化来验证缓存清理机制的完善情况
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主验证函数"""
    print("=" * 80)
    print("缓存清理机制验证测试")
    print("=" * 80)
    
    try:
        # 1. 测试主程序初始化
        print("\n1. 测试主程序初始化...")
        from src.main import PumpOptimizationMain
        
        app = PumpOptimizationMain()
        print("✅ 主程序类创建成功")
        
        # 2. 测试系统初始化
        print("\n2. 测试系统初始化...")
        if app.initialize():
            print("✅ 系统初始化成功")
        else:
            print("❌ 系统初始化失败")
            return False
        
        # 3. 测试缓存组件
        print("\n3. 测试缓存组件...")
        
        # 测试数据转换器缓存
        if app.data_transformer:
            print("✅ 数据转换器已初始化")
            
            # 测试设备缓存
            if hasattr(app.data_transformer, 'device_cache') and app.data_transformer.device_cache:
                device_cache = app.data_transformer.device_cache
                print("✅ 设备ID缓存已初始化")
                
                # 测试缓存清理方法
                if hasattr(device_cache, 'cleanup_resources'):
                    print("✅ 设备缓存支持cleanup_resources方法")
                else:
                    print("❌ 设备缓存缺少cleanup_resources方法")
                
                # 测试上下文管理器
                if hasattr(device_cache, '__enter__') and hasattr(device_cache, '__exit__'):
                    print("✅ 设备缓存支持上下文管理器")
                else:
                    print("❌ 设备缓存缺少上下文管理器支持")
            
            # 测试时间对齐缓存
            if hasattr(app.data_transformer, 'time_cache') and app.data_transformer.time_cache:
                time_cache = app.data_transformer.time_cache
                print("✅ 时间对齐缓存已初始化")
                
                # 测试缓存清理方法
                if hasattr(time_cache, 'cleanup_resources'):
                    print("✅ 时间缓存支持cleanup_resources方法")
                else:
                    print("❌ 时间缓存缺少cleanup_resources方法")
                
                # 测试上下文管理器
                if hasattr(time_cache, '__enter__') and hasattr(time_cache, '__exit__'):
                    print("✅ 时间缓存支持上下文管理器")
                else:
                    print("❌ 时间缓存缺少上下文管理器支持")
        
        # 4. 测试统一缓存管理器
        print("\n4. 测试统一缓存管理器...")
        try:
            from src.utils.cache_manager import get_cache_manager
            cache_manager = get_cache_manager()
            print("✅ 统一缓存管理器创建成功")
            
            # 测试缓存管理器方法
            if hasattr(cache_manager, 'clear_all_caches'):
                print("✅ 缓存管理器支持clear_all_caches方法")
            else:
                print("❌ 缓存管理器缺少clear_all_caches方法")
                
            if hasattr(cache_manager, 'check_memory_usage'):
                print("✅ 缓存管理器支持内存监控")
            else:
                print("❌ 缓存管理器缺少内存监控功能")
                
        except ImportError as e:
            print(f"❌ 统一缓存管理器导入失败: {e}")
        
        # 5. 测试资源管理器集成
        print("\n5. 测试资源管理器集成...")
        try:
            from src.utils.resource_manager import get_resource_manager
            resource_manager = get_resource_manager()
            print("✅ 资源管理器创建成功")
            
            # 检查是否集成了缓存清理
            if hasattr(resource_manager, '_cleanup_all_caches'):
                print("✅ 资源管理器已集成缓存清理功能")
            else:
                print("❌ 资源管理器未集成缓存清理功能")
                
        except ImportError as e:
            print(f"❌ 资源管理器导入失败: {e}")
        
        # 6. 测试性能监控模块
        print("\n6. 测试性能监控模块...")
        try:
            from src.utils.performance_monitor import monitor_performance, get_performance_stats
            print("✅ 性能监控模块导入成功")
            
            # 测试装饰器
            @monitor_performance("test_function")
            def test_func():
                return "测试成功"
            
            result = test_func()
            print(f"✅ 性能监控装饰器测试: {result}")
            
            # 获取统计信息
            stats = get_performance_stats("test_function")
            if stats:
                print("✅ 性能统计功能正常")
            else:
                print("❌ 性能统计功能异常")
                
        except ImportError as e:
            print(f"❌ 性能监控模块导入失败: {e}")
        
        # 7. 测试缓存清理功能
        print("\n7. 测试缓存清理功能...")
        
        # 测试设备缓存清理
        if app.data_transformer and hasattr(app.data_transformer, 'device_cache'):
            device_cache = app.data_transformer.device_cache
            if hasattr(device_cache, 'cleanup_resources'):
                try:
                    device_cache.cleanup_resources()
                    print("✅ 设备缓存清理测试成功")
                except Exception as e:
                    print(f"❌ 设备缓存清理测试失败: {e}")
        
        # 测试时间缓存清理
        if app.data_transformer and hasattr(app.data_transformer, 'time_cache'):
            time_cache = app.data_transformer.time_cache
            if hasattr(time_cache, 'cleanup_resources'):
                try:
                    time_cache.cleanup_resources()
                    print("✅ 时间缓存清理测试成功")
                except Exception as e:
                    print(f"❌ 时间缓存清理测试失败: {e}")
        
        # 8. 关闭程序
        print("\n8. 关闭程序...")
        app.shutdown()
        print("✅ 程序关闭成功")
        
        print("\n" + "=" * 80)
        print("缓存清理机制验证完成")
        print("✅ 第12个任务'完善缓存清理机制'验证通过")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 缓存清理机制完善验证成功！")
        sys.exit(0)
    else:
        print("\n💥 缓存清理机制验证失败！")
        sys.exit(1)
