#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
泵组优化程序启动脚本
提供便捷的程序启动方式
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("启动泵组优化程序...")
    print("=" * 60)
    
    try:
        # 导入主程序
        from src.main import main as main_program
        
        # 运行主程序
        main_program()
        
    except ImportError as e:
        print(f"导入模块失败: {e}")
        print("请确保所有依赖已正确安装")
        sys.exit(1)
    except Exception as e:
        print(f"程序运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
