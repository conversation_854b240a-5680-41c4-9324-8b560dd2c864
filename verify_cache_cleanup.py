#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存清理机制验证脚本 - ASCII版本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主验证函数"""
    print("=" * 80)
    print("Cache Cleanup Mechanism Verification Test")
    print("=" * 80)
    
    test_results = []
    
    try:
        # 1. 测试统一缓存管理器
        print("\n1. Testing Unified Cache Manager...")
        try:
            from src.utils.cache_manager import get_cache_manager
            cache_manager = get_cache_manager()
            print("[PASS] Unified cache manager imported successfully")
            test_results.append("PASS: Unified cache manager")
            
            # 测试方法
            if hasattr(cache_manager, 'clear_all_caches'):
                print("[PASS] Cache manager supports clear_all_caches method")
                test_results.append("PASS: clear_all_caches method")
            else:
                print("[FAIL] Cache manager missing clear_all_caches method")
                test_results.append("FAIL: clear_all_caches method")
                
            if hasattr(cache_manager, 'check_memory_usage'):
                print("[PASS] Cache manager supports memory monitoring")
                test_results.append("PASS: Memory monitoring")
            else:
                print("[FAIL] Cache manager missing memory monitoring")
                test_results.append("FAIL: Memory monitoring")
                
        except ImportError as e:
            print(f"[FAIL] Unified cache manager import failed: {e}")
            test_results.append("FAIL: Unified cache manager import")
        
        # 2. 测试性能监控模块
        print("\n2. Testing Performance Monitor Module...")
        try:
            from src.utils.performance_monitor import monitor_performance, get_performance_stats
            print("[PASS] Performance monitor module imported successfully")
            test_results.append("PASS: Performance monitor module")
            
            # 测试装饰器
            @monitor_performance("test_function")
            def test_func():
                return "test_success"
            
            result = test_func()
            if result == "test_success":
                print("[PASS] Performance monitor decorator test successful")
                test_results.append("PASS: Performance decorator")
            else:
                print("[FAIL] Performance monitor decorator test failed")
                test_results.append("FAIL: Performance decorator")
                
        except ImportError as e:
            print(f"[FAIL] Performance monitor module import failed: {e}")
            test_results.append("FAIL: Performance monitor import")
        
        # 3. 测试设备ID缓存
        print("\n3. Testing Device ID Cache...")
        try:
            from src.utils.device_id_cache import DeviceIDCache
            cache = DeviceIDCache()
            print("[PASS] Device ID cache created successfully")
            test_results.append("PASS: Device ID cache creation")
            
            if hasattr(cache, 'cleanup_resources'):
                print("[PASS] Device cache supports cleanup_resources method")
                test_results.append("PASS: Device cache cleanup_resources")
            else:
                print("[FAIL] Device cache missing cleanup_resources method")
                test_results.append("FAIL: Device cache cleanup_resources")
            
            if hasattr(cache, '__enter__') and hasattr(cache, '__exit__'):
                print("[PASS] Device cache supports context manager")
                test_results.append("PASS: Device cache context manager")
            else:
                print("[FAIL] Device cache missing context manager support")
                test_results.append("FAIL: Device cache context manager")
                
        except Exception as e:
            print(f"[FAIL] Device cache test failed: {e}")
            test_results.append("FAIL: Device cache test")
        
        # 4. 测试时间对齐缓存
        print("\n4. Testing Time Alignment Cache...")
        try:
            from src.utils.time_alignment_cache import TimeAlignmentCache
            cache = TimeAlignmentCache()
            print("[PASS] Time alignment cache created successfully")
            test_results.append("PASS: Time alignment cache creation")
            
            if hasattr(cache, 'cleanup_resources'):
                print("[PASS] Time cache supports cleanup_resources method")
                test_results.append("PASS: Time cache cleanup_resources")
            else:
                print("[FAIL] Time cache missing cleanup_resources method")
                test_results.append("FAIL: Time cache cleanup_resources")
            
            if hasattr(cache, '__enter__') and hasattr(cache, '__exit__'):
                print("[PASS] Time cache supports context manager")
                test_results.append("PASS: Time cache context manager")
            else:
                print("[FAIL] Time cache missing context manager support")
                test_results.append("FAIL: Time cache context manager")
                
        except Exception as e:
            print(f"[FAIL] Time cache test failed: {e}")
            test_results.append("FAIL: Time cache test")
        
        # 5. 测试查询缓存
        print("\n5. Testing Query Cache...")
        try:
            from src.utils.query_cache import QueryCacheManager
            cache = QueryCacheManager()
            print("[PASS] Query cache created successfully")
            test_results.append("PASS: Query cache creation")
            
            if hasattr(cache, 'cleanup_resources'):
                print("[PASS] Query cache supports cleanup_resources method")
                test_results.append("PASS: Query cache cleanup_resources")
            else:
                print("[FAIL] Query cache missing cleanup_resources method")
                test_results.append("FAIL: Query cache cleanup_resources")
            
            if hasattr(cache, '__enter__') and hasattr(cache, '__exit__'):
                print("[PASS] Query cache supports context manager")
                test_results.append("PASS: Query cache context manager")
            else:
                print("[FAIL] Query cache missing context manager support")
                test_results.append("FAIL: Query cache context manager")
                
        except Exception as e:
            print(f"[FAIL] Query cache test failed: {e}")
            test_results.append("FAIL: Query cache test")
        
        # 6. 测试资源管理器集成
        print("\n6. Testing Resource Manager Integration...")
        try:
            from src.utils.resource_manager import get_resource_manager
            resource_manager = get_resource_manager()
            print("[PASS] Resource manager created successfully")
            test_results.append("PASS: Resource manager creation")
            
            # 检查是否集成了缓存清理
            if hasattr(resource_manager, '_cleanup_all_caches'):
                print("[PASS] Resource manager integrated cache cleanup")
                test_results.append("PASS: Resource manager cache integration")
            else:
                print("[FAIL] Resource manager missing cache cleanup integration")
                test_results.append("FAIL: Resource manager cache integration")
                
        except ImportError as e:
            print(f"[FAIL] Resource manager import failed: {e}")
            test_results.append("FAIL: Resource manager import")
        
        # 统计测试结果
        print("\n" + "=" * 80)
        print("Test Results Summary:")
        print("=" * 80)
        
        pass_count = len([r for r in test_results if r.startswith("PASS")])
        fail_count = len([r for r in test_results if r.startswith("FAIL")])
        total_count = len(test_results)
        
        print(f"Total Tests: {total_count}")
        print(f"Passed: {pass_count}")
        print(f"Failed: {fail_count}")
        print(f"Success Rate: {pass_count/total_count*100:.1f}%")
        
        print("\nDetailed Results:")
        for result in test_results:
            print(f"  {result}")
        
        print("\n" + "=" * 80)
        if fail_count == 0:
            print("SUCCESS: All cache cleanup mechanism tests passed!")
            print("Task 12 'Improve Cache Cleanup Mechanisms' verification PASSED")
            return True
        else:
            print(f"PARTIAL SUCCESS: {pass_count}/{total_count} tests passed")
            print("Some cache cleanup features may need attention")
            return False
        
    except Exception as e:
        print(f"\nERROR: Verification process failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\nCache cleanup mechanism verification completed successfully!")
        sys.exit(0)
    else:
        print("\nCache cleanup mechanism verification completed with issues!")
        sys.exit(1)
