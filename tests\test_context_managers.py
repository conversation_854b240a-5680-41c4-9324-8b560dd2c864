#!/usr/bin/env python3
"""
上下文管理器测试 - 验证第13个任务的实现
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import get_logger
from src.core.message_bus import MessageBus, reset_message_bus
from src.core.config_manager import ConfigManagerWithCleanup
from src.utils.cache_manager import get_cache_manager
from src.utils.device_id_cache import get_device_cache
from src.utils.time_alignment_cache import get_time_alignment_cache
from src.utils.query_cache import QueryCacheManager
from src.main import PumpOptimizationMain


def test_message_bus_context_manager():
    """测试MessageBus上下文管理器"""
    logger = get_logger("test_message_bus")
    logger.info("=== 测试MessageBus上下文管理器 ===")
    
    try:
        # 重置消息总线
        reset_message_bus()
        
        # 使用上下文管理器
        with MessageBus(max_queue_size=100, max_workers=2) as bus:
            logger.info("✅ MessageBus上下文管理器进入成功")
            
            # 启动消息总线
            bus.start()
            logger.info("✅ MessageBus启动成功")
            
            # 获取统计信息
            stats = bus.get_stats()
            logger.info(f"✅ MessageBus统计信息: {stats}")
            
            # 测试基本功能
            assert hasattr(bus, 'cleanup_resources'), "MessageBus应该有cleanup_resources方法"
            assert hasattr(bus, '__enter__'), "MessageBus应该有__enter__方法"
            assert hasattr(bus, '__exit__'), "MessageBus应该有__exit__方法"
            
        logger.info("✅ MessageBus上下文管理器退出成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ MessageBus上下文管理器测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False


def test_config_manager_context_manager():
    """测试ConfigManager上下文管理器"""
    logger = get_logger("test_config_manager")
    logger.info("=== 测试ConfigManager上下文管理器 ===")
    
    try:
        # 使用上下文管理器
        with ConfigManagerWithCleanup("config/data_mapping.json") as config_mgr:
            logger.info("✅ ConfigManager上下文管理器进入成功")
            
            # 测试基本功能
            assert hasattr(config_mgr, 'cleanup_resources'), "ConfigManager应该有cleanup_resources方法"
            assert hasattr(config_mgr, '__enter__'), "ConfigManager应该有__enter__方法"
            assert hasattr(config_mgr, '__exit__'), "ConfigManager应该有__exit__方法"
            
            # 配置在初始化时自动加载，检查是否加载成功
            if config_mgr.station_configs:
                logger.info("✅ ConfigManager配置自动加载成功")
            else:
                logger.warning("⚠️ ConfigManager配置为空，可能配置文件不存在")
            
            # 获取统计信息
            stations = config_mgr.get_station_list()
            logger.info(f"✅ ConfigManager泵站数量: {len(stations)}")
            
        logger.info("✅ ConfigManager上下文管理器退出成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ ConfigManager上下文管理器测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False


def test_cache_context_managers():
    """测试缓存组件上下文管理器"""
    logger = get_logger("test_cache_managers")
    logger.info("=== 测试缓存组件上下文管理器 ===")
    
    try:
        # 测试DeviceIDCache
        with get_device_cache() as device_cache:
            logger.info("✅ DeviceIDCache上下文管理器进入成功")
            assert hasattr(device_cache, 'cleanup_resources'), "DeviceIDCache应该有cleanup_resources方法"
            assert hasattr(device_cache, '__enter__'), "DeviceIDCache应该有__enter__方法"
            assert hasattr(device_cache, '__exit__'), "DeviceIDCache应该有__exit__方法"
        
        # 测试TimeAlignmentCache
        with get_time_alignment_cache() as time_cache:
            logger.info("✅ TimeAlignmentCache上下文管理器进入成功")
            assert hasattr(time_cache, 'cleanup_resources'), "TimeAlignmentCache应该有cleanup_resources方法"
            assert hasattr(time_cache, '__enter__'), "TimeAlignmentCache应该有__enter__方法"
            assert hasattr(time_cache, '__exit__'), "TimeAlignmentCache应该有__exit__方法"
        
        # 测试QueryCacheManager
        with QueryCacheManager() as query_cache:
            logger.info("✅ QueryCacheManager上下文管理器进入成功")
            assert hasattr(query_cache, 'cleanup_resources'), "QueryCacheManager应该有cleanup_resources方法"
            assert hasattr(query_cache, '__enter__'), "QueryCacheManager应该有__enter__方法"
            assert hasattr(query_cache, '__exit__'), "QueryCacheManager应该有__exit__方法"
        
        # 测试CacheManager
        with get_cache_manager() as cache_mgr:
            logger.info("✅ CacheManager上下文管理器进入成功")
            assert hasattr(cache_mgr, 'cleanup_resources'), "CacheManager应该有cleanup_resources方法"
            assert hasattr(cache_mgr, '__enter__'), "CacheManager应该有__enter__方法"
            assert hasattr(cache_mgr, '__exit__'), "CacheManager应该有__exit__方法"
        
        logger.info("✅ 所有缓存组件上下文管理器测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 缓存组件上下文管理器测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False


def test_main_program_context_manager():
    """测试主程序上下文管理器"""
    logger = get_logger("test_main_program")
    logger.info("=== 测试主程序上下文管理器 ===")
    
    try:
        # 使用上下文管理器
        with PumpOptimizationMain("config") as app:
            logger.info("✅ PumpOptimizationMain上下文管理器进入成功")
            
            # 测试基本功能
            assert hasattr(app, 'cleanup_resources'), "PumpOptimizationMain应该有cleanup_resources方法"
            assert hasattr(app, '__enter__'), "PumpOptimizationMain应该有__enter__方法"
            assert hasattr(app, '__exit__'), "PumpOptimizationMain应该有__exit__方法"
            
            logger.info("✅ PumpOptimizationMain基本功能验证成功")
            
        logger.info("✅ PumpOptimizationMain上下文管理器退出成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ PumpOptimizationMain上下文管理器测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False


def main():
    """主测试函数"""
    logger = get_logger("context_manager_test")
    logger.info("开始上下文管理器功能测试")
    
    test_results = []
    
    # 执行所有测试
    test_results.append(("MessageBus上下文管理器", test_message_bus_context_manager()))
    test_results.append(("ConfigManager上下文管理器", test_config_manager_context_manager()))
    test_results.append(("缓存组件上下文管理器", test_cache_context_managers()))
    test_results.append(("主程序上下文管理器", test_main_program_context_manager()))
    
    # 统计结果
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    logger.info("="*60)
    logger.info("上下文管理器测试结果汇总:")
    logger.info("="*60)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    logger.info("="*60)
    logger.info(f"测试通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        logger.info("🎉 所有上下文管理器测试通过！")
        return True
    else:
        logger.error(f"❌ {total-passed} 个测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
