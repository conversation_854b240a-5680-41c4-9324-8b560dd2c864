{
  "environment": "production",
  "version": "1.0.0",
  "
  
  "database": {
    "host": "localhost",
    "port": 3306,
    "user": "pump_user",
    "password": "${DB_PASSWORD}",
    "database": "pump_optimization_db",
    "charset": "utf8mb4",
    "pool_settings": {
      "pool_size": 20,
      "max_overflow": 30,
      "pool_timeout": 30,
      "pool_recycle": 3600,
      "pool_pre_ping": true
    },
    "connection_options": {
      "autocommit": false,
      "isolation_level": "READ_COMMITTED"
    }
  },
  
  "application": {
    "name": "pump_optimization_system",
    "debug": false,
    "log_level": "INFO",
    "max_workers": 16,
    "worker_timeout": 300,
    "batch_processing": {
      "default_batch_size": 1000,
      "max_batch_size": 5000,
      "batch_timeout": 600,
      "retry_attempts": 3,
      "retry_delay": 5
    }
  },
  
  "data_processing": {
    "csv_settings": {
      "input_directory": "/opt/pump_optimization/data/input",
      "processed_directory": "/opt/pump_optimization/data/processed",
      "error_directory": "/opt/pump_optimization/data/error",
      "backup_directory": "/opt/pump_optimization/data/backup",
      "max_file_size_mb": 500,
      "encoding": "utf-8",
      "delimiter": ",",
      "chunk_size": 10000
    },
    "time_alignment": {
      "precision": "seconds",
      "timezone": "Asia/Shanghai",
      "data_quality_filter": 192,
      "duplicate_handling": "keep_first",
      "missing_value_strategy": "skip"
    },
    "device_mapping": {
      "mapping_file": "/opt/pump_optimization/config/data_mapping.json",
      "cache_mappings": true,
      "cache_ttl_seconds": 3600,
      "auto_generate_device_ids": true,
      "device_id_prefix": "PUMP"
    }
  },
  
  "performance": {
    "monitoring": {
      "enabled": true,
      "interval_seconds": 5,
      "metrics_retention_days": 30,
      "alert_thresholds": {
        "cpu_usage_percent": 80,
        "memory_usage_percent": 85,
        "disk_usage_percent": 90,
        "processing_time_seconds": 300,
        "error_rate_percent": 5
      }
    },
    "optimization": {
      "enable_connection_pooling": true,
      "enable_query_cache": true,
      "enable_batch_optimization": true,
      "enable_parallel_processing": true,
      "memory_limit_mb": 8192,
      "temp_directory": "/tmp/pump_processing"
    }
  },
  
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "handlers": {
      "file": {
        "enabled": true,
        "filename": "/opt/pump_optimization/logs/application.log",
        "max_bytes": 104857600,
        "backup_count": 10,
        "encoding": "utf-8"
      },
      "console": {
        "enabled": false
      },
      "syslog": {
        "enabled": true,
        "facility": "local0",
        "address": "/dev/log"
      }
    },
    "loggers": {
      "sqlalchemy.engine": "WARNING",
      "sqlalchemy.pool": "WARNING",
      "urllib3": "WARNING"
    }
  },
  
  "security": {
    "encryption": {
      "enabled": true,
      "algorithm": "AES-256-GCM",
      "key_file": "/opt/pump_optimization/config/encryption.key"
    },
    "access_control": {
      "enable_ip_whitelist": true,
      "allowed_ips": ["127.0.0.1", "10.0.0.0/8", "***********/16"],
      "enable_rate_limiting": true,
      "max_requests_per_minute": 1000
    },
    "audit": {
      "enabled": true,
      "log_file": "/opt/pump_optimization/logs/audit.log",
      "log_level": "INFO"
    }
  },
  
  "backup": {
    "enabled": true,
    "schedule": {
      "database_backup": "0 2 * * *",
      "config_backup": "0 3 * * 0",
      "log_backup": "0 4 * * 0"
    },
    "retention": {
      "database_backups_days": 30,
      "config_backups_days": 90,
      "log_backups_days": 7
    },
    "storage": {
      "local_path": "/opt/pump_optimization/backup",
      "remote_enabled": false,
      "remote_type": "s3",
      "remote_config": {
        "bucket": "pump-optimization-backups",
        "region": "us-east-1",
        "access_key": "${AWS_ACCESS_KEY}",
        "secret_key": "${AWS_SECRET_KEY}"
      }
    }
  },
  
  "alerting": {
    "enabled": true,
    "channels": {
      "email": {
        "enabled": true,
        "smtp_server": "smtp.company.com",
        "smtp_port": 587,
        "username": "${SMTP_USERNAME}",
        "password": "${SMTP_PASSWORD}",
        "from_address": "<EMAIL>",
        "to_addresses": ["<EMAIL>", "<EMAIL>"]
      },
      "webhook": {
        "enabled": false,
        "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK",
        "timeout_seconds": 10
      }
    },
    "rules": {
      "critical_errors": {
        "enabled": true,
        "threshold": 1,
        "window_minutes": 5,
        "channels": ["email", "webhook"]
      },
      "high_cpu_usage": {
        "enabled": true,
        "threshold": 90,
        "window_minutes": 10,
        "channels": ["email"]
      },
      "high_memory_usage": {
        "enabled": true,
        "threshold": 95,
        "window_minutes": 10,
        "channels": ["email"]
      },
      "processing_delays": {
        "enabled": true,
        "threshold_seconds": 600,
        "window_minutes": 15,
        "channels": ["email"]
      }
    }
  },
  
  "maintenance": {
    "scheduled_tasks": {
      "database_optimization": {
        "enabled": true,
        "schedule": "0 1 * * 0",
        "tasks": ["OPTIMIZE TABLE", "ANALYZE TABLE", "CHECK TABLE"]
      },
      "log_cleanup": {
        "enabled": true,
        "schedule": "0 0 * * *",
        "retention_days": 30
      },
      "temp_cleanup": {
        "enabled": true,
        "schedule": "*/30 * * * *",
        "max_age_hours": 2
      },
      "partition_maintenance": {
        "enabled": true,
        "schedule": "0 2 1 * *",
        "auto_create_partitions": true,
        "partition_ahead_months": 3
      }
    },
    "health_checks": {
      "enabled": true,
      "interval_seconds": 60,
      "checks": [
        "database_connection",
        "disk_space",
        "memory_usage",
        "process_status",
        "log_file_size"
      ]
    }
  },
  
  "integration": {
    "external_apis": {
      "enabled": false,
      "timeout_seconds": 30,
      "retry_attempts": 3,
      "retry_delay_seconds": 5
    },
    "message_queue": {
      "enabled": true,
      "type": "redis",
      "host": "localhost",
      "port": 6379,
      "database": 0,
      "password": "${REDIS_PASSWORD}",
      "max_connections": 20
    },
    "cache": {
      "enabled": true,
      "type": "redis",
      "host": "localhost",
      "port": 6379,
      "database": 1,
      "ttl_seconds": 3600,
      "max_memory": "1gb"
    }
  },
  
  "development": {
    "debug_mode": false,
    "profiling_enabled": false,
    "test_data_enabled": false,
    "mock_external_services": false
  },
  
  "compliance": {
    "data_retention": {
      "enabled": true,
      "default_retention_days": 2555,
      "table_specific": {
        "pump_data": 2555,
        "main_pipe_data": 2555,
        "audit_logs": 2190
      }
    },
    "data_privacy": {
      "enabled": true,
      "anonymize_logs": true,
      "encrypt_sensitive_data": true
    }
  }
}
