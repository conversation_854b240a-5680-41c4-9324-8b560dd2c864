#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地自动化脚本 - 替代CI/CD工具
用于本地环境的自动化测试、代码检查和部署
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from typing import List, Dict, Any
import json
from datetime import datetime

class LocalAutomation:
    """本地自动化管理器"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self.src_dir = self.project_root / "src"
        self.tests_dir = self.project_root / "tests"
        self.logs_dir = self.project_root / "logs"
        self.reports_dir = self.project_root / "reports"
        
        # 确保目录存在
        self.logs_dir.mkdir(exist_ok=True)
        self.reports_dir.mkdir(exist_ok=True)
        
        self.results = {}
    
    def run_full_pipeline(self) -> Dict[str, Any]:
        """运行完整的自动化流水线"""
        print("🚀 开始本地自动化流水线...")
        
        pipeline_steps = [
            ("代码质量检查", self.run_code_quality_checks),
            ("单元测试", self.run_unit_tests),
            ("集成测试", self.run_integration_tests),
            ("安全检查", self.run_security_checks),
            ("性能测试", self.run_performance_tests),
            ("生成报告", self.generate_reports)
        ]
        
        overall_success = True
        
        for step_name, step_func in pipeline_steps:
            print(f"\n📋 执行: {step_name}")
            try:
                result = step_func()
                self.results[step_name] = result
                
                if result.get('success', False):
                    print(f"✅ {step_name} 完成")
                else:
                    print(f"❌ {step_name} 失败")
                    overall_success = False
                    
            except Exception as e:
                print(f"❌ {step_name} 异常: {e}")
                self.results[step_name] = {'success': False, 'error': str(e)}
                overall_success = False
        
        # 保存结果
        self._save_pipeline_results(overall_success)
        
        return {
            'overall_success': overall_success,
            'results': self.results,
            'timestamp': datetime.now().isoformat()
        }
    
    def run_code_quality_checks(self) -> Dict[str, Any]:
        """运行代码质量检查"""
        results = {}
        
        # Black 代码格式检查
        print("  🔍 检查代码格式...")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'black', '--check', str(self.src_dir)
            ], capture_output=True, text=True, cwd=self.project_root)
            
            results['black'] = {
                'success': result.returncode == 0,
                'output': result.stdout + result.stderr
            }
        except Exception as e:
            results['black'] = {'success': False, 'error': str(e)}
        
        # Flake8 代码风格检查
        print("  🔍 检查代码风格...")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'flake8', str(self.src_dir)
            ], capture_output=True, text=True, cwd=self.project_root)
            
            results['flake8'] = {
                'success': result.returncode == 0,
                'output': result.stdout + result.stderr
            }
        except Exception as e:
            results['flake8'] = {'success': False, 'error': str(e)}
        
        # 计算总体成功率
        success_count = sum(1 for r in results.values() if r.get('success', False))
        total_count = len(results)
        
        return {
            'success': success_count == total_count,
            'details': results,
            'success_rate': success_count / total_count if total_count > 0 else 0
        }
    
    def run_unit_tests(self) -> Dict[str, Any]:
        """运行单元测试"""
        print("  🧪 运行单元测试...")
        
        if not self.tests_dir.exists():
            return {
                'success': False,
                'error': 'tests目录不存在',
                'coverage': 0
            }
        
        try:
            # 运行pytest with coverage
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                str(self.tests_dir),
                '--cov=src',
                '--cov-report=term-missing',
                '--cov-report=json:reports/coverage.json',
                '-v'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            # 解析覆盖率
            coverage_file = self.reports_dir / 'coverage.json'
            coverage_percent = 0
            
            if coverage_file.exists():
                try:
                    with open(coverage_file, 'r') as f:
                        coverage_data = json.load(f)
                        coverage_percent = coverage_data.get('totals', {}).get('percent_covered', 0)
                except:
                    pass
            
            return {
                'success': result.returncode == 0,
                'output': result.stdout + result.stderr,
                'coverage': coverage_percent
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'coverage': 0
            }
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """运行集成测试"""
        print("  🔗 运行集成测试...")
        
        integration_tests_dir = self.tests_dir / 'integration'
        
        if not integration_tests_dir.exists():
            return {
                'success': True,  # 没有集成测试不算失败
                'message': '没有集成测试',
                'tests_run': 0
            }
        
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                str(integration_tests_dir),
                '-v'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            return {
                'success': result.returncode == 0,
                'output': result.stdout + result.stderr
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def run_security_checks(self) -> Dict[str, Any]:
        """运行安全检查"""
        print("  🔒 运行安全检查...")
        
        try:
            # Bandit 安全检查
            result = subprocess.run([
                sys.executable, '-m', 'bandit', 
                '-r', str(self.src_dir),
                '-f', 'json',
                '-o', str(self.reports_dir / 'security.json')
            ], capture_output=True, text=True, cwd=self.project_root)
            
            # 解析安全报告
            security_file = self.reports_dir / 'security.json'
            high_issues = 0
            medium_issues = 0
            
            if security_file.exists():
                try:
                    with open(security_file, 'r') as f:
                        security_data = json.load(f)
                        for issue in security_data.get('results', []):
                            severity = issue.get('issue_severity', '').upper()
                            if severity == 'HIGH':
                                high_issues += 1
                            elif severity == 'MEDIUM':
                                medium_issues += 1
                except:
                    pass
            
            return {
                'success': high_issues == 0,  # 只要没有高危漏洞就算成功
                'high_issues': high_issues,
                'medium_issues': medium_issues,
                'output': result.stdout + result.stderr
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def run_performance_tests(self) -> Dict[str, Any]:
        """运行性能测试"""
        print("  ⚡ 运行性能测试...")
        
        performance_tests_dir = self.tests_dir / 'performance'
        
        if not performance_tests_dir.exists():
            return {
                'success': True,  # 没有性能测试不算失败
                'message': '没有性能测试',
                'tests_run': 0
            }
        
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pytest', 
                str(performance_tests_dir),
                '--benchmark-only',
                '--benchmark-json=reports/benchmark.json',
                '-v'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            return {
                'success': result.returncode == 0,
                'output': result.stdout + result.stderr
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_reports(self) -> Dict[str, Any]:
        """生成综合报告"""
        print("  📊 生成综合报告...")
        
        try:
            report_content = self._create_html_report()
            
            report_file = self.reports_dir / f'automation_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html'
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            return {
                'success': True,
                'report_file': str(report_file)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _create_html_report(self) -> str:
        """创建HTML报告"""
        html_template = """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>自动化流水线报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .success { color: green; }
        .failure { color: red; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .details { background: #f9f9f9; padding: 10px; margin: 10px 0; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>自动化流水线报告</h1>
        <p>生成时间: {timestamp}</p>
        <p>项目路径: {project_root}</p>
    </div>
    
    {sections}
</body>
</html>
        """
        
        sections = ""
        for step_name, result in self.results.items():
            if step_name == "生成报告":
                continue
                
            success_class = "success" if result.get('success', False) else "failure"
            status = "✅ 成功" if result.get('success', False) else "❌ 失败"
            
            section_html = f"""
    <div class="section">
        <h2 class="{success_class}">{step_name} - {status}</h2>
        <div class="details">
            {self._format_result_details(result)}
        </div>
    </div>
            """
            sections += section_html
        
        return html_template.format(
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            project_root=str(self.project_root),
            sections=sections
        )
    
    def _format_result_details(self, result: Dict[str, Any]) -> str:
        """格式化结果详情"""
        details = []
        
        if 'error' in result:
            details.append(f"<p><strong>错误:</strong> {result['error']}</p>")
        
        if 'coverage' in result:
            details.append(f"<p><strong>测试覆盖率:</strong> {result['coverage']:.1f}%</p>")
        
        if 'high_issues' in result:
            details.append(f"<p><strong>高危安全问题:</strong> {result['high_issues']}</p>")
            details.append(f"<p><strong>中危安全问题:</strong> {result['medium_issues']}</p>")
        
        if 'output' in result and result['output'].strip():
            details.append(f"<pre>{result['output']}</pre>")
        
        return "".join(details) if details else "<p>无详细信息</p>"
    
    def _save_pipeline_results(self, overall_success: bool):
        """保存流水线结果"""
        result_file = self.logs_dir / f'pipeline_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        
        pipeline_result = {
            'timestamp': datetime.now().isoformat(),
            'overall_success': overall_success,
            'project_root': str(self.project_root),
            'results': self.results
        }
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(pipeline_result, f, ensure_ascii=False, indent=2)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='本地自动化流水线')
    parser.add_argument('--project-root', help='项目根目录路径')
    parser.add_argument('--step', help='只运行特定步骤', 
                       choices=['quality', 'unit-test', 'integration', 'security', 'performance'])
    
    args = parser.parse_args()
    
    automation = LocalAutomation(args.project_root)
    
    if args.step:
        # 运行特定步骤
        step_map = {
            'quality': automation.run_code_quality_checks,
            'unit-test': automation.run_unit_tests,
            'integration': automation.run_integration_tests,
            'security': automation.run_security_checks,
            'performance': automation.run_performance_tests
        }
        
        if args.step in step_map:
            result = step_map[args.step]()
            print(f"\n结果: {'成功' if result.get('success', False) else '失败'}")
        else:
            print(f"未知步骤: {args.step}")
    else:
        # 运行完整流水线
        result = automation.run_full_pipeline()
        
        print(f"\n🎉 流水线完成!")
        print(f"总体结果: {'✅ 成功' if result['overall_success'] else '❌ 失败'}")
        
        # 显示各步骤结果
        for step_name, step_result in result['results'].items():
            if step_name != "生成报告":
                status = "✅" if step_result.get('success', False) else "❌"
                print(f"  {status} {step_name}")

if __name__ == "__main__":
    main()
