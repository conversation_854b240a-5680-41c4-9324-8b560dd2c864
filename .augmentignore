# Augment索引排除配置文件
# 类似于.gitignore，用于控制哪些文件和目录不被索引

# 虚拟环境目录 - 完全排除
venv/
venv/**
.venv/
.venv/**
env/
env/**

# 数据目录 - 只获取文件名，不索引内容
data/**/*.csv
data/**/*.txt
data/**/*.json
data/**/*.xml
data/**/*.xlsx
data/**/*.xls

# 日志文件 - 通常不需要索引
logs/
logs/**
*.log

# 临时文件和缓存
__pycache__/
__pycache__/**
*.pyc
*.pyo
*.pyd
.Python
*.so
.cache/
.pytest_cache/

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 构建和分发目录
build/
dist/
*.egg-info/

# 数据库文件（如果有）
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old

# 大型二进制文件
*.zip
*.tar.gz
*.rar
*.7z
*.exe
*.dll

# 敏感配置文件（如果包含密码等）
secrets.yaml
.env
.env.local
