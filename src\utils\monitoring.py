#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控模块
提供业务级监控、性能指标收集和告警功能
"""

import time
import psutil
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from src.utils.logger import get_logger, BusinessMetric, record_business_metric


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"      # 计数器
    GAUGE = "gauge"         # 仪表盘
    HISTOGRAM = "histogram"  # 直方图
    TIMER = "timer"         # 计时器


@dataclass
class MetricPoint:
    """指标数据点"""
    timestamp: datetime
    value: float
    labels: Dict[str, str]
    metric_type: MetricType


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.metrics = {}
        self.lock = threading.Lock()
        self.start_time = datetime.now()
        
        # 系统资源监控
        self.cpu_threshold = 80.0  # CPU使用率告警阈值
        self.memory_threshold = 85.0  # 内存使用率告警阈值
        self.disk_threshold = 90.0  # 磁盘使用率告警阈值
        
        # 业务指标阈值
        self.error_rate_threshold = 5.0  # 错误率告警阈值(%)
        self.slow_query_threshold = 2.0  # 慢查询告警阈值(秒)
        
        self.logger.info("系统监控器初始化完成")
    
    def record_metric(self, name: str, value: float, metric_type: MetricType = MetricType.GAUGE,
                     labels: Dict[str, str] = None):
        """记录指标"""
        with self.lock:
            if name not in self.metrics:
                self.metrics[name] = []
            
            point = MetricPoint(
                timestamp=datetime.now(),
                value=value,
                labels=labels or {},
                metric_type=metric_type
            )
            
            self.metrics[name].append(point)
            
            # 保持最近1000个数据点
            if len(self.metrics[name]) > 1000:
                self.metrics[name] = self.metrics[name][-1000:]
    
    def increment_counter(self, name: str, value: float = 1.0, labels: Dict[str, str] = None):
        """递增计数器"""
        self.record_metric(name, value, MetricType.COUNTER, labels)
    
    def set_gauge(self, name: str, value: float, labels: Dict[str, str] = None):
        """设置仪表盘值"""
        self.record_metric(name, value, MetricType.GAUGE, labels)
    
    def record_timer(self, name: str, duration: float, labels: Dict[str, str] = None):
        """记录计时器"""
        self.record_metric(name, duration, MetricType.TIMER, labels)
        
        # 检查慢操作
        if duration > self.slow_query_threshold:
            self.logger.warning(f"慢操作检测: {name} 耗时 {duration:.3f}秒")
            record_business_metric(BusinessMetric.PERFORMANCE_SLOW, duration, {
                'operation': name,
                'duration': duration,
                'labels': labels or {}
            })
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统资源指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用情况
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 网络I/O
            net_io = psutil.net_io_counters()
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            
            metrics = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_available_gb': memory.available / (1024**3),
                'memory_used_gb': memory.used / (1024**3),
                'disk_percent': disk_percent,
                'disk_free_gb': disk.free / (1024**3),
                'network_bytes_sent': net_io.bytes_sent,
                'network_bytes_recv': net_io.bytes_recv,
                'process_memory_mb': process_memory.rss / (1024**2),
                'process_cpu_percent': process.cpu_percent(),
                'uptime_seconds': (datetime.now() - self.start_time).total_seconds()
            }
            
            # 记录系统指标
            self.set_gauge('system.cpu_percent', cpu_percent)
            self.set_gauge('system.memory_percent', memory_percent)
            self.set_gauge('system.disk_percent', disk_percent)
            
            # 检查告警阈值
            self._check_system_alerts(metrics)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"获取系统指标失败: {e}")
            return {}
    
    def _check_system_alerts(self, metrics: Dict[str, Any]):
        """检查系统告警"""
        if metrics['cpu_percent'] > self.cpu_threshold:
            self.logger.warning(f"CPU使用率过高: {metrics['cpu_percent']:.1f}%")
            record_business_metric(BusinessMetric.ERROR_OCCURRED, 1.0, {
                'type': 'high_cpu',
                'value': metrics['cpu_percent']
            })
        
        if metrics['memory_percent'] > self.memory_threshold:
            self.logger.warning(f"内存使用率过高: {metrics['memory_percent']:.1f}%")
            record_business_metric(BusinessMetric.ERROR_OCCURRED, 1.0, {
                'type': 'high_memory',
                'value': metrics['memory_percent']
            })
        
        if metrics['disk_percent'] > self.disk_threshold:
            self.logger.warning(f"磁盘使用率过高: {metrics['disk_percent']:.1f}%")
            record_business_metric(BusinessMetric.ERROR_OCCURRED, 1.0, {
                'type': 'high_disk',
                'value': metrics['disk_percent']
            })
    
    def get_business_metrics(self) -> Dict[str, Any]:
        """获取业务指标摘要"""
        with self.lock:
            summary = {}
            
            for name, points in self.metrics.items():
                if not points:
                    continue
                
                # 计算基本统计
                values = [p.value for p in points]
                recent_points = [p for p in points 
                               if (datetime.now() - p.timestamp).total_seconds() < 3600]
                
                summary[name] = {
                    'current': values[-1] if values else 0,
                    'count': len(values),
                    'recent_1h_count': len(recent_points),
                    'avg': sum(values) / len(values) if values else 0,
                    'min': min(values) if values else 0,
                    'max': max(values) if values else 0,
                    'last_update': points[-1].timestamp.isoformat() if points else None
                }
            
            return summary
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        system_metrics = self.get_system_metrics()
        business_metrics = self.get_business_metrics()
        
        # 计算关键性能指标
        timer_metrics = {name: data for name, data in business_metrics.items() 
                        if 'timer' in name or 'duration' in name}
        
        error_metrics = {name: data for name, data in business_metrics.items() 
                        if 'error' in name or 'failed' in name}
        
        return {
            'system': system_metrics,
            'business': business_metrics,
            'performance': {
                'timers': timer_metrics,
                'errors': error_metrics,
                'uptime_hours': system_metrics.get('uptime_seconds', 0) / 3600
            },
            'timestamp': datetime.now().isoformat()
        }


class PerformanceTimer:
    """性能计时器上下文管理器"""
    
    def __init__(self, monitor: SystemMonitor, name: str, labels: Dict[str, str] = None):
        self.monitor = monitor
        self.name = name
        self.labels = labels or {}
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.monitor.record_timer(self.name, duration, self.labels)


# 全局监控器实例
_global_monitor = SystemMonitor()


def get_monitor() -> SystemMonitor:
    """获取全局监控器实例"""
    return _global_monitor


def monitor_performance(operation_name: str, labels: Dict[str, str] = None):
    """性能监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            with PerformanceTimer(_global_monitor, operation_name, labels):
                return func(*args, **kwargs)
        return wrapper
    return decorator


def record_business_operation(operation: str, success: bool = True, 
                            duration: float = None, context: Dict[str, Any] = None):
    """记录业务操作"""
    monitor = get_monitor()
    
    # 记录操作计数
    status = 'success' if success else 'failed'
    monitor.increment_counter(f'business.{operation}.{status}')
    
    # 记录持续时间
    if duration is not None:
        monitor.record_timer(f'business.{operation}.duration', duration)
    
    # 记录业务指标
    if success:
        if operation == 'file_processed':
            record_business_metric(BusinessMetric.FILE_PROCESSED, 1.0, context)
        elif operation == 'data_inserted':
            record_business_metric(BusinessMetric.DATA_INSERTED, 1.0, context)
        elif operation == 'message_processed':
            record_business_metric(BusinessMetric.MESSAGE_PROCESSED, 1.0, context)
    else:
        record_business_metric(BusinessMetric.ERROR_OCCURRED, 1.0, context)


if __name__ == "__main__":
    # 测试监控系统
    monitor = get_monitor()
    
    # 测试系统指标
    print("系统指标:", monitor.get_system_metrics())
    
    # 测试业务指标
    monitor.increment_counter('test.counter', 5)
    monitor.set_gauge('test.gauge', 42.5)
    
    with PerformanceTimer(monitor, 'test.operation'):
        time.sleep(0.1)
    
    print("业务指标:", monitor.get_business_metrics())
    print("性能摘要:", monitor.get_performance_summary())
