# -*- coding: utf-8 -*-
"""
统一日志管理模块
提供全程日志记录和性能监控功能
支持业务级监控、告警和多种日志输出格式
"""

import os
import sys
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, List
import threading
import traceback
from enum import Enum


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class BusinessMetric(Enum):
    """业务指标枚举"""
    FILE_PROCESSED = "file_processed"
    DATA_INSERTED = "data_inserted"
    ERROR_OCCURRED = "error_occurred"
    PERFORMANCE_SLOW = "performance_slow"
    CACHE_HIT = "cache_hit"
    CACHE_MISS = "cache_miss"
    DATABASE_CONNECTION = "database_connection"
    MESSAGE_PROCESSED = "message_processed"


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class BusinessMonitor:
    """业务监控器"""

    def __init__(self):
        self.metrics = {}
        self.alerts = []
        self.thresholds = {
            BusinessMetric.ERROR_OCCURRED: {'warning': 10, 'critical': 50},
            BusinessMetric.PERFORMANCE_SLOW: {'warning': 5, 'critical': 20},
            BusinessMetric.CACHE_MISS: {'warning': 100, 'critical': 500}
        }

    def record_metric(self, metric: BusinessMetric, value: float = 1.0,
                     context: Dict[str, Any] = None):
        """记录业务指标"""
        timestamp = datetime.now()

        if metric not in self.metrics:
            self.metrics[metric] = []

        self.metrics[metric].append({
            'timestamp': timestamp,
            'value': value,
            'context': context or {}
        })

        # 检查是否需要告警
        self._check_alert_thresholds(metric)

    def _check_alert_thresholds(self, metric: BusinessMetric):
        """检查告警阈值"""
        if metric not in self.thresholds:
            return

        # 计算最近1小时的指标总数
        now = datetime.now()
        recent_metrics = [
            m for m in self.metrics[metric]
            if (now - m['timestamp']).total_seconds() < 3600
        ]

        count = len(recent_metrics)
        thresholds = self.thresholds[metric]

        if count >= thresholds['critical']:
            self._create_alert(AlertLevel.CRITICAL, metric, count)
        elif count >= thresholds['warning']:
            self._create_alert(AlertLevel.WARNING, metric, count)

    def _create_alert(self, level: AlertLevel, metric: BusinessMetric, count: int):
        """创建告警"""
        alert = {
            'timestamp': datetime.now(),
            'level': level,
            'metric': metric,
            'count': count,
            'message': f"{metric.value} 指标异常: 最近1小时发生 {count} 次"
        }

        self.alerts.append(alert)

        # 保持最近100条告警
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]

    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        summary = {}
        for metric, records in self.metrics.items():
            summary[metric.value] = {
                'total_count': len(records),
                'recent_1h': len([
                    r for r in records
                    if (datetime.now() - r['timestamp']).total_seconds() < 3600
                ]),
                'recent_24h': len([
                    r for r in records
                    if (datetime.now() - r['timestamp']).total_seconds() < 86400
                ])
            }
        return summary

    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        from datetime import timedelta
        cutoff = datetime.now() - timedelta(hours=hours)

        return [
            alert for alert in self.alerts
            if alert['timestamp'] >= cutoff
        ]


class UnifiedLoggerManager:
    """统一日志管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    _loggers: Dict[str, logging.Logger] = {}
    _initialized = False
    _business_monitor = None
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._setup_logging()
            self._business_monitor = BusinessMonitor()
            self._initialized = True
    
    def _setup_logging(self):
        """设置日志系统"""
        try:
            # 确保日志目录存在
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            # 清空日志目录（按要求每次启动清空）
            for log_file in log_dir.glob("*.log"):
                try:
                    log_file.unlink()
                except Exception as e:
                    print(f"清空日志文件失败 {log_file}: {e}")
            
            # 设置根日志级别
            logging.getLogger().setLevel(logging.DEBUG)
            
            # 创建格式化器
            self.detailed_formatter = logging.Formatter(
                '%(asctime)s.%(msecs)03d - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            self.simple_formatter = logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            # 创建主日志文件处理器 - 强制刷新
            self.main_handler = logging.handlers.RotatingFileHandler(
                log_dir / "application.log",
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            self.main_handler.setLevel(logging.DEBUG)
            self.main_handler.setFormatter(self.detailed_formatter)
            # 强制立即刷新 - 避免递归
            original_flush = self.main_handler.stream.flush
            def enhanced_flush():
                original_flush()
                try:
                    os.fsync(self.main_handler.stream.fileno())
                except:
                    pass
            self.main_handler.stream.flush = enhanced_flush
            
            # 创建错误日志文件处理器 - 强制刷新
            self.error_handler = logging.handlers.RotatingFileHandler(
                log_dir / "errors.log",
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            self.error_handler.setLevel(logging.ERROR)
            self.error_handler.setFormatter(self.detailed_formatter)
            # 强制立即刷新 - 避免递归
            original_error_flush = self.error_handler.stream.flush
            def enhanced_error_flush():
                original_error_flush()
                try:
                    os.fsync(self.error_handler.stream.fileno())
                except:
                    pass
            self.error_handler.stream.flush = enhanced_error_flush
            
            # 创建控制台处理器
            self.console_handler = logging.StreamHandler(sys.stdout)
            self.console_handler.setLevel(logging.INFO)
            self.console_handler.setFormatter(self.simple_formatter)
            
            print(f"日志系统初始化完成 - {datetime.now()}")
            
        except Exception as e:
            print(f"日志系统初始化失败: {e}")
            traceback.print_exc()
    
    def get_logger(self, name: str, log_file: Optional[str] = None) -> logging.Logger:
        """获取指定名称的日志器"""
        # 使用参数避免警告（log_file为将来功能保留）
        _ = log_file
        if name not in self._loggers:
            logger = logging.getLogger(name)
            logger.setLevel(logging.DEBUG)
            
            # 清除现有处理器
            logger.handlers.clear()
            
            # 添加处理器
            logger.addHandler(self.main_handler)
            logger.addHandler(self.error_handler)
            logger.addHandler(self.console_handler)
            
            # 防止重复日志
            logger.propagate = False
            
            self._loggers[name] = logger
            
            # 记录日志器创建
            logger.debug(f"日志器创建: {name}")
        
        return self._loggers[name]
    
    def create_module_logger(self, module_name: str, log_file: Optional[str] = None) -> logging.Logger:
        """为特定模块创建专用日志器"""
        logger_name = f"module.{module_name}"
        
        if logger_name not in self._loggers:
            logger = logging.getLogger(logger_name)
            logger.setLevel(logging.DEBUG)
            logger.handlers.clear()
            
            # 添加通用处理器
            logger.addHandler(self.main_handler)
            logger.addHandler(self.error_handler)
            logger.addHandler(self.console_handler)
            
            # 如果指定了专用日志文件，添加专用处理器
            if log_file:
                try:
                    log_dir = Path("logs")
                    module_handler = logging.handlers.RotatingFileHandler(
                        log_dir / log_file,
                        maxBytes=5*1024*1024,  # 5MB
                        backupCount=3,
                        encoding='utf-8'
                    )
                    module_handler.setLevel(logging.DEBUG)
                    module_handler.setFormatter(self.detailed_formatter)
                    logger.addHandler(module_handler)
                except Exception as e:
                    logger.error(f"创建模块专用日志文件失败 {log_file}: {e}")
            
            logger.propagate = False
            self._loggers[logger_name] = logger
            
            logger.info(f"模块日志器创建: {module_name}")
        
        return self._loggers[logger_name]
    
    def log_system_info(self):
        """记录系统信息"""
        main_logger = self.get_logger("system")
        
        try:
            import platform
            import psutil
            
            main_logger.info("=== 系统信息 ===")
            main_logger.info(f"操作系统: {platform.system()} {platform.release()}")
            main_logger.info(f"Python版本: {platform.python_version()}")
            main_logger.info(f"CPU核心数: {psutil.cpu_count()}")
            main_logger.info(f"内存总量: {psutil.virtual_memory().total / (1024**3):.2f} GB")
            main_logger.info(f"当前工作目录: {os.getcwd()}")
            main_logger.info("=== 系统信息结束 ===")
            
        except Exception as e:
            main_logger.error(f"记录系统信息失败: {e}")
    
    def log_exception(self, logger_name: str, exception: Exception, context: str = ""):
        """记录异常信息"""
        logger = self.get_logger(logger_name)
        
        error_msg = f"异常发生"
        if context:
            error_msg += f" - {context}"
        
        logger.error(error_msg)
        logger.error(f"异常类型: {type(exception).__name__}")
        logger.error(f"异常信息: {str(exception)}")
        logger.error(f"异常堆栈:\n{traceback.format_exc()}")
    
    def flush_all_logs(self):
        """刷新所有日志缓冲区"""
        try:
            for handler in [self.main_handler, self.error_handler, self.console_handler]:
                handler.flush()
                # 强制同步到磁盘
                if hasattr(handler, 'stream') and hasattr(handler.stream, 'fileno'):
                    try:
                        os.fsync(handler.stream.fileno())
                    except:
                        pass

            for logger in self._loggers.values():
                for handler in logger.handlers:
                    handler.flush()
                    if hasattr(handler, 'stream') and hasattr(handler.stream, 'fileno'):
                        try:
                            os.fsync(handler.stream.fileno())
                        except:
                            pass

        except Exception as e:
            print(f"刷新日志缓冲区失败: {e}")

    def check_log_health(self):
        """检查日志系统健康状态"""
        try:
            test_message = f"日志健康检查 - {datetime.now()}"
            test_logger = self.get_logger("health_check")
            test_logger.info(test_message)

            # 强制刷新
            self.flush_all_logs()

            # 检查文件是否写入
            log_file = Path("logs/application.log")
            if log_file.exists():
                with open(log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if test_message not in content:
                        print("[WARN] 警告：日志文件写入异常")
                        return False
                    else:
                        print("[OK] 日志系统健康检查通过")
                        return True
            else:
                print("[ERROR] 错误：日志文件不存在")
                return False

        except Exception as e:
            print(f"[ERROR] 日志健康检查失败: {e}")
            return False

    def record_business_metric(self, metric: BusinessMetric, value: float = 1.0,
                              context: Dict[str, Any] = None):
        """记录业务指标"""
        if self._business_monitor:
            self._business_monitor.record_metric(metric, value, context)

    def get_business_metrics(self) -> Dict[str, Any]:
        """获取业务指标摘要"""
        if self._business_monitor:
            return self._business_monitor.get_metrics_summary()
        return {}

    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        if self._business_monitor:
            return self._business_monitor.get_recent_alerts(hours)
        return []

    def log_business_event(self, logger_name: str, event_type: BusinessMetric,
                          message: str, context: Dict[str, Any] = None):
        """记录业务事件"""
        logger = self.get_logger(logger_name)

        # 记录日志
        logger.info(f"[BUSINESS] {event_type.value}: {message}")

        # 记录业务指标
        self.record_business_metric(event_type, 1.0, context)


# 全局日志管理器实例
logger_manager = UnifiedLoggerManager()


def get_logger(name: str, log_file: Optional[str] = None) -> logging.Logger:
    """
    获取日志器实例

    Args:
        name: 日志器名称
        log_file: 可选的专用日志文件名

    Returns:
        配置好的日志器实例
    """
    return logger_manager.get_logger(name, log_file)


def record_business_metric(metric: BusinessMetric, value: float = 1.0,
                          context: Dict[str, Any] = None):
    """记录业务指标的便捷函数"""
    logger_manager.record_business_metric(metric, value, context)


def log_business_event(logger_name: str, event_type: BusinessMetric,
                      message: str, context: Dict[str, Any] = None):
    """记录业务事件的便捷函数"""
    logger_manager.log_business_event(logger_name, event_type, message, context)


def get_business_metrics() -> Dict[str, Any]:
    """获取业务指标摘要的便捷函数"""
    return logger_manager.get_business_metrics()


def get_recent_alerts(hours: int = 24) -> List[Dict[str, Any]]:
    """获取最近告警的便捷函数"""
    return logger_manager.get_recent_alerts(hours)


def log_function_call(func):
    """函数调用日志装饰器"""
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)
        func_name = func.__name__
        
        logger.debug(f"函数调用开始: {func_name}")
        logger.debug(f"参数: args={args}, kwargs={kwargs}")
        
        try:
            start_time = datetime.now()
            result = func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.debug(f"函数调用成功: {func_name}, 耗时: {duration:.3f}秒")
            return result
            
        except Exception as e:
            logger_manager.log_exception(func.__module__, e, f"函数调用失败: {func_name}")
            raise
    
    return wrapper


if __name__ == "__main__":
    # 测试日志系统
    test_logger = get_logger("test")
    
    test_logger.info("日志系统测试开始")
    test_logger.debug("这是调试信息")
    test_logger.warning("这是警告信息")
    test_logger.error("这是错误信息")
    
    # 测试异常记录
    try:
        raise ValueError("测试异常")
    except Exception as e:
        logger_manager.log_exception("test", e, "测试异常记录")
    
    test_logger.info("日志系统测试完成")
    logger_manager.flush_all_logs()
