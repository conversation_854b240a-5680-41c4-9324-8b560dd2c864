#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一缓存管理器
负责管理系统中所有缓存的生命周期、清理和监控
"""

import threading
import gc
import psutil
from typing import Dict, Any, Optional
from src.utils.logger import get_logger
from src.utils.performance import monitor_performance


class CacheManager:
    """统一缓存管理器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        
        # 缓存注册表
        self.registered_caches: Dict[str, Any] = {}
        self.cache_stats: Dict[str, Dict] = {}
        
        # 管理状态
        self.is_cleaning = False
        self.cleanup_lock = threading.RLock()
        
        # 内存监控配置
        self.memory_warning_threshold = 0.8  # 80%
        self.memory_critical_threshold = 0.9  # 90%
        self.auto_cleanup_enabled = True
        
        # 统计信息
        self.global_stats = {
            'total_caches': 0,
            'total_cleanup_operations': 0,
            'total_memory_warnings': 0,
            'total_items_cleared': 0,
            'last_cleanup_time': None
        }
        
        self.logger.info("统一缓存管理器初始化完成")
    
    def register_cache(self, cache_name: str, cache_instance: Any, cache_type: str = 'unknown'):
        """注册缓存实例"""
        with self.cleanup_lock:
            if cache_name in self.registered_caches:
                self.logger.warning(f"缓存 {cache_name} 已存在，将被覆盖")
            
            self.registered_caches[cache_name] = cache_instance
            self.cache_stats[cache_name] = {
                'type': cache_type,
                'registered_time': __import__('datetime').datetime.now(),
                'cleanup_count': 0,
                'last_cleanup_time': None,
                'items_cleared': 0
            }
            
            self.global_stats['total_caches'] = len(self.registered_caches)
            self.logger.info(f"注册缓存: {cache_name} (类型: {cache_type})")
    
    def unregister_cache(self, cache_name: str):
        """注销缓存实例"""
        with self.cleanup_lock:
            if cache_name in self.registered_caches:
                del self.registered_caches[cache_name]
                del self.cache_stats[cache_name]
                self.global_stats['total_caches'] = len(self.registered_caches)
                self.logger.info(f"注销缓存: {cache_name}")
    
    def get_cache_info(self, cache_name: str) -> Optional[Dict]:
        """获取指定缓存的信息"""
        if cache_name not in self.registered_caches:
            return None
        
        cache_instance = self.registered_caches[cache_name]
        cache_info = self.cache_stats[cache_name].copy()
        
        # 尝试获取缓存大小
        try:
            if hasattr(cache_instance, '__len__'):
                cache_info['current_size'] = len(cache_instance)
            elif hasattr(cache_instance, 'get_cache_stats'):
                stats = cache_instance.get_cache_stats()
                cache_info['current_size'] = stats.get('cache_size', 0)
                cache_info['cache_stats'] = stats
            else:
                cache_info['current_size'] = 'unknown'
        except Exception as e:
            self.logger.warning(f"获取缓存 {cache_name} 大小失败: {e}")
            cache_info['current_size'] = 'error'
        
        return cache_info
    
    def get_all_cache_info(self) -> Dict[str, Dict]:
        """获取所有缓存的信息"""
        all_info = {}
        for cache_name in self.registered_caches:
            all_info[cache_name] = self.get_cache_info(cache_name)
        return all_info
    
    def clear_cache(self, cache_name: str) -> int:
        """清理指定缓存"""
        if cache_name not in self.registered_caches:
            self.logger.warning(f"缓存 {cache_name} 不存在")
            return 0
        
        cache_instance = self.registered_caches[cache_name]
        cleared_items = 0
        
        try:
            # 尝试不同的清理方法
            if hasattr(cache_instance, 'cleanup_resources'):
                cache_instance.cleanup_resources()
                cleared_items = 1  # 表示清理操作完成
            elif hasattr(cache_instance, 'clear_cache'):
                result = cache_instance.clear_cache()
                if isinstance(result, (int, tuple)):
                    cleared_items = result if isinstance(result, int) else sum(result)
                else:
                    cleared_items = 1
            elif hasattr(cache_instance, 'clear'):
                cache_instance.clear()
                cleared_items = 1
            else:
                self.logger.warning(f"缓存 {cache_name} 没有可用的清理方法")
                return 0
            
            # 更新统计信息
            self.cache_stats[cache_name]['cleanup_count'] += 1
            self.cache_stats[cache_name]['last_cleanup_time'] = __import__('datetime').datetime.now()
            self.cache_stats[cache_name]['items_cleared'] += cleared_items
            
            self.logger.info(f"缓存 {cache_name} 清理完成: 清理项目数 {cleared_items}")
            return cleared_items
            
        except Exception as e:
            self.logger.error(f"清理缓存 {cache_name} 失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")
            return 0
    
    @monitor_performance("clear_all_caches")
    def clear_all_caches(self) -> Dict[str, int]:
        """清理所有注册的缓存"""
        if self.is_cleaning:
            self.logger.warning("缓存清理已在进行中，跳过重复清理")
            return {}
        
        with self.cleanup_lock:
            self.is_cleaning = True
            
            try:
                self.logger.info("开始清理所有缓存")
                
                results = {}
                total_cleared = 0
                
                for cache_name in list(self.registered_caches.keys()):
                    cleared_items = self.clear_cache(cache_name)
                    results[cache_name] = cleared_items
                    total_cleared += cleared_items
                
                # 强制垃圾回收
                collected = gc.collect()
                
                # 更新全局统计
                self.global_stats['total_cleanup_operations'] += 1
                self.global_stats['total_items_cleared'] += total_cleared
                self.global_stats['last_cleanup_time'] = __import__('datetime').datetime.now()
                
                self.logger.info(f"所有缓存清理完成: 清理 {len(results)} 个缓存, "
                               f"总计 {total_cleared} 个项目, 垃圾回收 {collected} 个对象")
                
                return results
                
            except Exception as e:
                self.logger.error(f"清理所有缓存失败: {e}")
                import traceback
                self.logger.error(f"清理错误详情: {traceback.format_exc()}")
                return {}
            
            finally:
                self.is_cleaning = False
    
    def check_memory_usage(self) -> Dict[str, Any]:
        """检查系统内存使用情况"""
        try:
            # 获取系统内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent / 100.0
            
            # 获取当前进程内存信息
            process = psutil.Process()
            process_memory = process.memory_info()
            process_memory_mb = process_memory.rss / 1024 / 1024
            
            memory_info = {
                'system_memory_percent': memory_percent,
                'system_memory_available_gb': memory.available / 1024 / 1024 / 1024,
                'process_memory_mb': process_memory_mb,
                'memory_warning': memory_percent >= self.memory_warning_threshold,
                'memory_critical': memory_percent >= self.memory_critical_threshold
            }
            
            # 检查是否需要自动清理
            if self.auto_cleanup_enabled and memory_info['memory_critical']:
                self.logger.warning(f"内存使用率过高: {memory_percent:.1%}, 触发自动缓存清理")
                self.global_stats['total_memory_warnings'] += 1
                self.clear_all_caches()
            
            return memory_info
            
        except Exception as e:
            self.logger.error(f"检查内存使用失败: {e}")
            return {'error': str(e)}
    
    def get_global_stats(self) -> Dict[str, Any]:
        """获取全局统计信息"""
        stats = self.global_stats.copy()
        
        # 添加当前内存信息
        memory_info = self.check_memory_usage()
        stats.update(memory_info)
        
        # 添加缓存概览
        stats['cache_overview'] = {
            'total_registered': len(self.registered_caches),
            'cache_names': list(self.registered_caches.keys())
        }
        
        return stats
    
    def cleanup_resources(self):
        """清理所有资源"""
        self.logger.info("开始清理CacheManager资源")
        
        try:
            # 清理所有缓存
            results = self.clear_all_caches()
            
            # 清空注册表
            self.registered_caches.clear()
            self.cache_stats.clear()
            
            # 重置统计信息
            self.global_stats = {
                'total_caches': 0,
                'total_cleanup_operations': 0,
                'total_memory_warnings': 0,
                'total_items_cleared': 0,
                'last_cleanup_time': None
            }
            
            self.logger.info(f"CacheManager资源清理完成: {results}")
            
        except Exception as e:
            self.logger.error(f"CacheManager资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        # 使用参数避免警告
        _ = exc_type, exc_val, exc_tb
        self.cleanup_resources()


# 全局缓存管理器实例
_global_cache_manager = None
_cache_manager_lock = threading.Lock()


def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器实例"""
    global _global_cache_manager
    
    if _global_cache_manager is None:
        with _cache_manager_lock:
            if _global_cache_manager is None:
                _global_cache_manager = CacheManager()
    
    return _global_cache_manager


def register_cache(cache_name: str, cache_instance: Any, cache_type: str = 'unknown'):
    """注册缓存到全局管理器"""
    cache_manager = get_cache_manager()
    cache_manager.register_cache(cache_name, cache_instance, cache_type)


def clear_all_system_caches() -> Dict[str, int]:
    """清理系统中所有缓存"""
    cache_manager = get_cache_manager()
    return cache_manager.clear_all_caches()
