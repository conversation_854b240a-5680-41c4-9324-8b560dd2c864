# -*- coding: utf-8 -*-
"""
数据库插入处理器 - 专门处理数据库插入请求
负责接收流式处理器发送的数据并写入数据库
"""

import os
import sys
import time
import threading
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.message_bus import MessageHandler, Message, MessageType
from src.core.database_manager import DatabaseManager
from src.utils.logger import get_logger
from src.utils.performance import monitor_performance

class DatabaseInsertHandler(MessageHandler):
    """数据库插入处理器 - 专门处理数据库插入请求"""
    
    def __init__(self, handler_id: str = "database_insert_handler"):
        super().__init__(handler_id)
        self.logger = get_logger(f"DatabaseInsert.{handler_id}")
        
        # 初始化数据库管理器
        try:
            self.db_manager = DatabaseManager()
            self.logger.info("数据库管理器初始化成功")
        except Exception as e:
            self.logger.error(f"数据库管理器初始化失败: {e}")
            self.db_manager = None
        
        # 批次处理配置
        self.batch_size = 1000  # 批次大小
        self.max_retry_count = 3  # 最大重试次数
        
        # 统计信息
        self.stats = {
            'inserts_processed': 0,
            'inserts_failed': 0,
            'total_records': 0,
            'processing_time': 0.0
        }
        
        self.logger.info(f"数据库插入处理器 {handler_id} 初始化完成")
    
    def _process_message(self, message: Message) -> Optional[Message]:
        """处理数据库插入请求消息"""
        thread_name = threading.current_thread().name
        
        if message.type != MessageType.DATABASE_INSERT_REQUEST:
            self.logger.warning(f"线程 {thread_name} 收到不支持的消息类型: {message.type.value}")
            return None
        
        try:
            # 提取插入参数
            data_records = message.payload.get('data_records', [])
            table_name = message.payload.get('table_name', 'raw_data_by_device')
            chunk_id = message.payload.get('chunk_id', 0)
            is_streaming = message.payload.get('is_streaming', False)
            
            if not data_records:
                self.logger.warning(f"线程 {thread_name} 收到空数据记录")
                return None
            
            self.logger.info(f"线程 {thread_name} 开始插入数据: {len(data_records)} 条记录到表 {table_name}")
            
            # 执行数据库插入
            result = self._insert_data_to_database(data_records, table_name, chunk_id, is_streaming)
            
            # 更新统计信息
            if result['success']:
                self.stats['inserts_processed'] += 1
                self.stats['total_records'] += result.get('inserted_count', 0)
                self.stats['processing_time'] += result.get('processing_time', 0)
            else:
                self.stats['inserts_failed'] += 1
            
            # 创建响应消息
            if result['success']:
                response_message = Message(
                    type=MessageType.DATABASE_INSERT_COMPLETED,
                    payload={
                        'table_name': table_name,
                        'chunk_id': chunk_id,
                        'inserted_count': result.get('inserted_count', 0),
                        'processing_time': result.get('processing_time', 0),
                        'is_streaming': is_streaming
                    },
                    sender=self.handler_id,
                    correlation_id=message.correlation_id
                )
                
                self.logger.info(f"数据库插入完成: {result.get('inserted_count', 0)} 条记录")
                return response_message
            else:
                error_message = Message(
                    type=MessageType.DATABASE_INSERT_FAILED,
                    payload={
                        'table_name': table_name,
                        'chunk_id': chunk_id,
                        'error': result.get('error', 'Unknown error'),
                        'error_type': result.get('error_type', 'DatabaseError'),
                        'is_streaming': is_streaming
                    },
                    sender=self.handler_id,
                    correlation_id=message.correlation_id
                )
                
                self.logger.error(f"数据库插入失败: {result.get('error', 'Unknown error')}")
                return error_message
                
        except Exception as e:
            self.logger.error(f"处理数据库插入消息失败: {e}")
            
            # 更新失败统计
            self.stats['inserts_failed'] += 1
            
            # 创建失败响应消息
            error_message = Message(
                type=MessageType.DATABASE_INSERT_FAILED,
                payload={
                    'table_name': message.payload.get('table_name', 'unknown'),
                    'chunk_id': message.payload.get('chunk_id', 0),
                    'error': str(e),
                    'error_type': type(e).__name__,
                    'is_streaming': message.payload.get('is_streaming', False)
                },
                sender=self.handler_id,
                correlation_id=message.correlation_id
            )
            
            return error_message
    
    @monitor_performance("insert_data_to_database")
    def _insert_data_to_database(self, data_records: List[Dict], table_name: str, 
                                chunk_id: int, is_streaming: bool) -> Dict[str, Any]:
        """执行数据库插入操作"""
        start_time = time.time()
        
        if not self.db_manager:
            return {
                'success': False,
                'error': '数据库管理器未初始化',
                'error_type': 'DatabaseManagerError'
            }
        
        try:
            # 验证表名
            if table_name not in ['raw_data_by_device', 'raw_data_by_station', 'pump_data', 'main_pipe_data']:
                self.logger.warning(f"未知表名，使用默认表: {table_name} -> raw_data_by_device")
                table_name = 'raw_data_by_device'
            
            # 批量插入数据
            inserted_count = 0
            retry_count = 0
            
            while retry_count < self.max_retry_count:
                try:
                    # 分批插入
                    for i in range(0, len(data_records), self.batch_size):
                        batch = data_records[i:i + self.batch_size]
                        
                        # 构建插入SQL
                        if table_name == 'raw_data_by_device':
                            sql = """
                            INSERT INTO raw_data_by_device 
                            (station_id, device_name, param_name, tag_name, data_time, 
                             data_quality, data_value, file_source, batch_id) 
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """
                            
                            values = [
                                (
                                    record.get('station_id', 'unknown'),
                                    record.get('device_name', 'unknown'),
                                    record.get('param_name', 'unknown'),
                                    record.get('tag_name', 'unknown'),
                                    record.get('data_time', '2025-07-29 12:00:00'),
                                    record.get('data_quality', 192),
                                    record.get('data_value', 0.0),
                                    record.get('file_source', ''),
                                    record.get('batch_id', 'unknown')
                                )
                                for record in batch
                            ]
                        else:
                            # 其他表的插入逻辑可以在这里添加
                            self.logger.warning(f"表 {table_name} 的插入逻辑未实现，跳过")
                            continue
                        
                        # 执行批量插入 - 使用DatabaseManager的方法
                        affected_rows = self.db_manager.execute_batch_insert(sql, values)
                        inserted_count += affected_rows
                        
                        self.logger.debug(f"批次插入完成: {len(batch)} 条记录 (总计: {inserted_count})")
                    
                    # 插入成功，跳出重试循环
                    break
                    
                except Exception as e:
                    retry_count += 1
                    self.logger.warning(f"数据库插入失败 (重试 {retry_count}/{self.max_retry_count}): {e}")
                    
                    if retry_count >= self.max_retry_count:
                        raise e
                    
                    # 等待后重试
                    time.sleep(0.5 * retry_count)
            
            processing_time = time.time() - start_time
            
            self.logger.info(f"数据库插入成功: {inserted_count} 条记录, 耗时: {processing_time:.3f}秒")
            
            return {
                'success': True,
                'inserted_count': inserted_count,
                'processing_time': processing_time,
                'table_name': table_name,
                'chunk_id': chunk_id
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            
            self.logger.error(f"数据库插入失败: {e}, 耗时: {processing_time:.3f}秒")
            
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__,
                'processing_time': processing_time,
                'inserted_count': 0
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            **self.stats,
            'handler_id': self.handler_id,
            'is_active': self.is_active
        }

# 测试数据库插入处理器
if __name__ == "__main__":
    print("测试数据库插入处理器...")
    
    try:
        handler = DatabaseInsertHandler("test_db_handler")
        print(f"数据库插入处理器创建成功: {handler.handler_id}")
        
        # 获取统计信息
        stats = handler.get_stats()
        print(f"统计信息: {stats}")
        
        print("数据库插入处理器测试完成")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
