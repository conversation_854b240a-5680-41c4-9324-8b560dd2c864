# -*- coding: utf-8 -*-
"""
流式CSV处理器 - 解决内存占用问题
"""

import gc
import time
import pandas as pd
import psutil
from pathlib import Path
from typing import Dict, Any, List
from datetime import datetime
from src.utils.logger import get_logger
from src.core.database_manager import DatabaseManager
from src.utils.time_alignment_cache import get_time_alignment_cache
from src.utils.device_id_cache import get_device_cache
from src.core.singleton_data_transformer import get_data_transformer

class StreamingCSVProcessor:
    """流式CSV处理器 - 内存优化版本"""
    
    def __init__(self, memory_limit_gb: float = 4.0):
        self.logger = get_logger(__name__)
        self.memory_limit = memory_limit_gb * 1024 * 1024 * 1024  # 转换为字节
        self.chunk_size = 10000  # 默认块大小
        self.min_chunk_size = 1000
        self.max_chunk_size = 50000

        # 动态调整参数
        self.target_insert_time = 2.5  # 目标插入时间（秒）
        self.time_tolerance = 0.5  # 时间容忍度（秒）
        self.adjustment_factor = 0.2  # 调整因子
        self.current_chunk_size = self.chunk_size  # 当前使用的块大小

        # 🔧 初始化缓存系统
        self.time_cache = get_time_alignment_cache()
        self.device_cache = get_device_cache()

        # 初始化数据库管理器 - 直接写入数据库
        try:
            self.db_manager = DatabaseManager()
            self.logger.info("流式处理器数据库连接初始化成功")
        except Exception as e:
            self.logger.error(f"流式处理器数据库连接初始化失败: {e}")
            self.db_manager = None

        # 🔧 初始化数据转换器（传递数据库管理器）
        self.data_transformer = get_data_transformer(self.db_manager)

        self.logger.info(f"流式处理器初始化完成: 内存限制={memory_limit_gb}GB")
        self.logger.info(f"缓存系统状态: 时间缓存={'已启用' if self.time_cache is not None else '未启用'}, "
                        f"设备缓存={'已启用' if self.device_cache is not None else '未启用'}, "
                        f"数据转换器={'已启用' if self.data_transformer is not None else '未启用'}")

        # 加载数据采样配置
        self._load_sampling_config()

        # 🔧 预加载设备ID缓存
        self._preload_device_cache()

    def _load_sampling_config(self):
        """加载数据采样配置"""
        try:
            import yaml
            config_path = Path("config/database_config.yaml")

            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                data_loading_config = config.get('data_loading', {})

                # 数据加载比例 (0.0-1.0)
                self.load_percentage = data_loading_config.get('load_percentage', 1.0)

                # 采样方式
                self.sampling_method = data_loading_config.get('sampling_method', 'head')

                # 测试模式
                self.test_mode = data_loading_config.get('test_mode', False)

                self.logger.info(f"[STREAM] 数据采样配置:")
                self.logger.info(f"[STREAM]   加载比例: {self.load_percentage * 100:.1f}%")
                self.logger.info(f"[STREAM]   采样方式: {self.sampling_method}")
                self.logger.info(f"[STREAM]   测试模式: {self.test_mode}")

                if self.load_percentage < 1.0:
                    self.logger.warning(f"[STREAM] [TEST MODE] 启用部分数据加载模式，仅加载 {self.load_percentage * 100:.1f}% 的数据")

            else:
                # 默认配置
                self.load_percentage = 1.0
                self.sampling_method = 'head'
                self.test_mode = False
                self.logger.warning(f"[STREAM] 数据加载配置文件不存在，使用默认配置: {config_path}")

        except Exception as e:
            # 默认配置
            self.load_percentage = 1.0
            self.sampling_method = 'head'
            self.test_mode = False
            self.logger.error(f"[STREAM] 加载数据加载配置失败: {e}，使用默认配置")

    def _preload_device_cache(self):
        """🔧 预加载设备ID缓存，提升查询性能"""
        if self.db_manager is None or self.device_cache is None:
            self.logger.warning("数据库管理器或设备缓存未初始化，跳过预加载")
            return 0

        try:
            self.logger.info("开始预加载设备ID缓存...")

            # 查询所有设备
            query = "SELECT device_name, device_id FROM devices ORDER BY device_id"
            results = self.db_manager.execute_query(query)

            if not results:
                self.logger.warning("数据库中没有设备记录，无法预加载缓存")
                return 0

            # 批量加载到缓存
            loaded_count = 0
            for row in results:
                device_name = row['device_name']
                device_id = row['device_id']

                if self.device_cache.set_device_id(device_name, device_id):
                    loaded_count += 1
                    self.logger.debug(f"预加载设备: {device_name} -> {device_id}")

            self.logger.info(f"设备ID缓存预加载完成: {loaded_count}个设备")

            # 同时预加载到数据转换器缓存
            if self.data_transformer:
                preloaded = self.data_transformer.preload_device_cache()
                self.logger.info(f"数据转换器缓存预加载: {preloaded}个设备")

            return loaded_count

        except Exception as e:
            self.logger.error(f"设备ID缓存预加载失败: {e}")
            return 0

    def _get_device_id_cached(self, device_name: str) -> int:
        """🔧 使用统一的设备ID查询方法"""
        if not device_name:
            self.logger.warning("设备名称为空，使用默认设备ID=1")
            return 1

        try:
            # 使用数据转换器的统一设备ID查询方法
            if self.data_transformer:
                device_id = self.data_transformer.get_device_id_by_name(device_name)
                if device_id is not None:
                    self.logger.debug(f"设备ID查询成功: {device_name} -> {device_id}")
                    return device_id

            # 查询失败，返回默认值
            self.logger.warning(f"设备ID查询失败: {device_name}, 使用默认值1")
            return 1

        except Exception as e:
            self.logger.error(f"设备ID查询异常: {device_name}, 错误: {e}, 使用默认值1")
            return 1

    def get_device_cache_stats(self) -> Dict[str, Any]:
        """获取设备缓存性能统计"""
        if self.device_cache is not None:
            return self.device_cache.get_cache_stats()
        return {'error': '设备缓存未初始化'}

    def get_processing_performance_summary(self) -> Dict[str, Any]:
        """获取处理性能汇总"""
        return {
            'time_alignment': self.get_time_alignment_stats(),
            'device_cache': self.get_device_cache_stats(),
            'data_transformer': self.data_transformer.get_performance_stats() if self.data_transformer else {'error': '数据转换器未初始化'}
        }

    def process_file_streaming(self, file_path: str, station_id: str,
                             device_name: str, param_name: str, batch_id: str,
                             target_table: str = 'raw_data_by_device') -> Dict[str, Any]:
        """流式处理CSV文件"""
        self.logger.info(f"[STREAM] === 开始流式处理文件 ===")
        self.logger.info(f"[STREAM] 文件路径: {file_path}")
        self.logger.info(f"[STREAM] 泵站ID: {station_id}")
        self.logger.info(f"[STREAM] 设备名称: {device_name}")
        self.logger.info(f"[STREAM] 参数名称: {param_name}")
        self.logger.info(f"[STREAM] 批次ID: {batch_id}")
        self.logger.info(f"[STREAM] 目标表: {target_table}")

        total_processed = 0
        processing_start = time.time()

        # 数据库插入统计
        db_insert_success = 0
        db_insert_failed = 0
        db_records_inserted = 0

        # 性能统计
        chunk_processing_times = []
        chunk_sizes = []

        # 🔧 新增：收集样本数据用于后续转换
        sample_data = []
        max_sample_size = 1000  # 最多收集1000条样本数据

        try:
            # 动态调整块大小
            self.logger.debug("[STREAM] 步骤1: 计算最优块大小")
            optimal_chunk_size = self._calculate_optimal_chunk_size(file_path)
            self.current_chunk_size = optimal_chunk_size
            self.logger.info(f"[STREAM] 初始块大小: {optimal_chunk_size:,} 行")
            self.logger.debug(f"[STREAM] 默认块大小: {self.chunk_size:,} 行")

            # 分块处理文件（使用动态块大小）
            self.logger.debug("[STREAM] 步骤2: 开始分块处理文件")
            chunk_count = 0
            chunk_id = 0

            # 使用动态块大小处理文件
            for chunk in self._read_file_with_dynamic_chunks(file_path):
                chunk_start_time = time.time()
                chunk_size = len(chunk)

                self.logger.debug(f"[STREAM] 处理块 {chunk_id}: {chunk_size:,} 行")

                # 🔧 根据目标表处理数据块
                if target_table in ['pump_data', 'main_pipe_data']:
                    # 直接转换为目标表格式
                    processed_chunk = self._process_chunk_for_target_table(
                        chunk, station_id, device_name, param_name, batch_id, file_path, target_table
                    )
                else:
                    # 传统raw_data格式
                    processed_chunk = self._process_chunk(
                        chunk, station_id, device_name, param_name, batch_id, file_path
                    )

                # 🔧 收集样本数据（前几个块的数据用于后续转换）
                if len(sample_data) < max_sample_size and processed_chunk:
                    remaining_space = max_sample_size - len(sample_data)
                    sample_data.extend(processed_chunk[:remaining_space])
                    self.logger.debug(f"[SAMPLE] 收集样本数据: {len(processed_chunk[:remaining_space])} 条，总计: {len(sample_data)} 条")

                processing_time = time.time() - chunk_start_time

                # 🔧 直接写入目标表
                insert_result = self._write_chunk_to_target_table(processed_chunk, chunk_id, target_table)

                # 检查插入结果并更新统计
                if insert_result['success']:
                    db_insert_success += 1
                    db_records_inserted += insert_result.get('inserted_count', 0)

                    # 记录性能统计
                    total_chunk_time = time.time() - chunk_start_time
                    chunk_processing_times.append(total_chunk_time)
                    chunk_sizes.append(chunk_size)

                    records_per_second = chunk_size / total_chunk_time if total_chunk_time > 0 else 0

                    self.logger.info(f"[STREAM] 块 {chunk_id} 处理完成:")
                    self.logger.info(f"[STREAM]   记录数: {chunk_size:,}")
                    self.logger.info(f"[STREAM]   处理耗时: {processing_time:.3f}秒")
                    self.logger.info(f"[STREAM]   插入耗时: {insert_result.get('processing_time', 0):.3f}秒")
                    self.logger.info(f"[STREAM]   总耗时: {total_chunk_time:.3f}秒")
                    self.logger.info(f"[STREAM]   处理速度: {records_per_second:.1f} 记录/秒")

                    # 动态调整块大小（基于插入性能）
                    insert_time = insert_result.get('processing_time', 0)
                    self._adjust_chunk_size_based_on_performance(insert_time, len(processed_chunk))

                else:
                    db_insert_failed += 1
                    error_msg = insert_result.get('error', 'Unknown error')
                    self.logger.error(f"[STREAM] 块 {chunk_id} 插入失败:")
                    self.logger.error(f"[STREAM]   错误信息: {error_msg}")
                    self.logger.error(f"[STREAM]   记录数: {chunk_size:,}")

                total_processed += len(chunk)
                chunk_count += 1
                chunk_id += 1

                # 内存管理
                self._memory_cleanup(chunk_id)

                # 强制释放内存
                del chunk, processed_chunk

                # 定期报告进度和垃圾回收
                if chunk_count % 10 == 0:
                    elapsed = time.time() - processing_start
                    rate = total_processed / elapsed if elapsed > 0 else 0

                    # 计算平均性能
                    if chunk_processing_times:
                        avg_chunk_time = sum(chunk_processing_times) / len(chunk_processing_times)
                        avg_chunk_size = sum(chunk_sizes) / len(chunk_sizes)
                        avg_chunk_rate = avg_chunk_size / avg_chunk_time if avg_chunk_time > 0 else 0
                    else:
                        avg_chunk_time = avg_chunk_size = avg_chunk_rate = 0

                    self.logger.info(f"[STREAM] === 进度报告 ===")
                    self.logger.info(f"[STREAM] 已处理块数: {chunk_count}")
                    self.logger.info(f"[STREAM] 已处理记录: {total_processed:,}")
                    self.logger.info(f"[STREAM] 总体速度: {rate:.1f} 记录/秒")
                    self.logger.info(f"[STREAM] 平均块大小: {avg_chunk_size:.0f} 记录")
                    self.logger.info(f"[STREAM] 平均块耗时: {avg_chunk_time:.3f}秒")
                    self.logger.info(f"[STREAM] 平均块速度: {avg_chunk_rate:.1f} 记录/秒")
                    self.logger.info(f"[STREAM] 当前块大小: {self.current_chunk_size:,}")

                    gc.collect()

            processing_time = time.time() - processing_start
            avg_rate = total_processed / processing_time if processing_time > 0 else 0

            # 计算最终性能统计
            if chunk_processing_times:
                min_chunk_time = min(chunk_processing_times)
                max_chunk_time = max(chunk_processing_times)
                avg_chunk_time = sum(chunk_processing_times) / len(chunk_processing_times)

                min_chunk_size = min(chunk_sizes)
                max_chunk_size = max(chunk_sizes)
                avg_chunk_size = sum(chunk_sizes) / len(chunk_sizes)
            else:
                min_chunk_time = max_chunk_time = avg_chunk_time = 0
                min_chunk_size = max_chunk_size = avg_chunk_size = 0

            self.logger.info(f"[STREAM] === 流式处理完成 ===")
            self.logger.info(f"[STREAM] 文件: {file_path}")
            self.logger.info(f"[STREAM] 总块数: {chunk_count}")
            self.logger.info(f"[STREAM] 总记录数: {total_processed:,}")
            self.logger.info(f"[STREAM] 总耗时: {processing_time:.2f}秒")
            self.logger.info(f"[STREAM] 平均速度: {avg_rate:.1f} 记录/秒")

            self.logger.info(f"[STREAM] 块大小统计:")
            self.logger.info(f"[STREAM]   最小: {min_chunk_size:,} 记录")
            self.logger.info(f"[STREAM]   最大: {max_chunk_size:,} 记录")
            self.logger.info(f"[STREAM]   平均: {avg_chunk_size:.0f} 记录")

            self.logger.info(f"[STREAM] 块处理时间统计:")
            self.logger.info(f"[STREAM]   最快: {min_chunk_time:.3f}秒")
            self.logger.info(f"[STREAM]   最慢: {max_chunk_time:.3f}秒")
            self.logger.info(f"[STREAM]   平均: {avg_chunk_time:.3f}秒")

            self.logger.info(f"[STREAM] 数据库操作:")
            self.logger.info(f"[STREAM]   插入成功: {db_insert_success} 块")
            self.logger.info(f"[STREAM]   插入失败: {db_insert_failed} 块")
            success_rate = db_insert_success/(db_insert_success+db_insert_failed)*100 if (db_insert_success+db_insert_failed) > 0 else 0
            self.logger.info(f"[STREAM]   成功率: {success_rate:.1f}%")
            self.logger.info(f"[STREAM]   插入记录数: {db_records_inserted:,}")
            
            self.logger.info(f"[SAMPLE] 样本数据收集完成: {len(sample_data)} 条记录")

            return {
                'success': True,
                'total_records': total_processed,
                'processing_time': processing_time,
                'chunks_processed': chunk_count,
                'db_insert_success': db_insert_success,
                'db_insert_failed': db_insert_failed,
                'db_records_inserted': db_records_inserted,
                'average_rate': avg_rate,
                'sample_data': sample_data,  # 🔧 新增：返回样本数据
                'chunk_stats': {
                    'min_size': min_chunk_size,
                    'max_size': max_chunk_size,
                    'avg_size': avg_chunk_size,
                    'min_time': min_chunk_time,
                    'max_time': max_chunk_time,
                    'avg_time': avg_chunk_time
                }
            }
            
        except Exception as e:
            self.logger.error(f"流式处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_records': total_processed
            }
    
    def _calculate_optimal_chunk_size(self, file_path: str) -> int:
        """根据文件大小、可用内存和历史性能计算最优块大小"""
        try:
            file_size = Path(file_path).stat().st_size
            available_memory = psutil.virtual_memory().available

            # 优先使用动态调整后的块大小
            if hasattr(self, 'current_chunk_size') and self.current_chunk_size != self.chunk_size:
                base_chunk_size = self.current_chunk_size
                self.logger.info(f"使用动态调整后的块大小: {base_chunk_size}")
            else:
                # 基于可用内存的初始计算
                if available_memory < self.memory_limit * 0.3:
                    base_chunk_size = self.min_chunk_size
                    self.logger.warning(f"可用内存不足，使用最小块大小: {base_chunk_size}")
                elif available_memory > self.memory_limit * 0.7:
                    base_chunk_size = self.max_chunk_size
                else:
                    # 线性插值
                    ratio = (available_memory - self.memory_limit * 0.3) / (self.memory_limit * 0.4)
                    base_chunk_size = int(self.min_chunk_size +
                                   (self.max_chunk_size - self.min_chunk_size) * ratio)

            # 确保在合理范围内
            optimal_size = max(self.min_chunk_size, min(base_chunk_size, self.max_chunk_size))

            self.logger.info(f"块大小计算: 文件大小={file_size/1024/1024:.1f}MB, 可用内存={available_memory/1024/1024/1024:.1f}GB, 最优块大小={optimal_size}")

            return optimal_size

        except Exception as e:
            self.logger.warning(f"计算块大小失败，使用默认值: {e}")
            return self.chunk_size

    def _adjust_chunk_size_based_on_performance(self, insert_time: float, chunk_records: int):
        """基于插入性能动态调整块大小"""
        try:
            # 计算当前的插入速度（记录/秒）
            records_per_second = chunk_records / insert_time if insert_time > 0 else 0

            self.logger.debug(f"[PERF] 性能分析:")
            self.logger.debug(f"[PERF]   插入时间: {insert_time:.3f}秒")
            self.logger.debug(f"[PERF]   目标时间: {self.target_insert_time:.3f}秒")
            self.logger.debug(f"[PERF]   记录数: {chunk_records:,}")
            self.logger.debug(f"[PERF]   插入速度: {records_per_second:.1f} 记录/秒")
            self.logger.debug(f"[PERF]   当前块大小: {self.current_chunk_size:,}")

            # 判断是否需要调整
            time_diff = insert_time - self.target_insert_time

            if abs(time_diff) <= self.time_tolerance:
                # 在目标范围内，不需要调整
                self.logger.debug(f"[PERF] 插入时间在目标范围内({insert_time:.3f}s)，块大小保持: {self.current_chunk_size:,}")
                return

            old_chunk_size = self.current_chunk_size

            if time_diff > self.time_tolerance:
                # 插入时间过长，减小块大小
                # 使用更激进的调整策略，快速收敛到目标时间
                target_chunk_size = int(chunk_records * self.target_insert_time / insert_time)
                # 限制单次调整幅度，避免过度调整
                max_reduction = int(self.current_chunk_size * 0.3)  # 最多减少30%
                adjustment = min(self.current_chunk_size - target_chunk_size, max_reduction)

                self.current_chunk_size = max(self.min_chunk_size,
                                            self.current_chunk_size - adjustment)
                self.logger.info(f"[ADJUST] 插入时间过长({insert_time:.3f}s > {self.target_insert_time}s)，减小块大小: {old_chunk_size:,} → {self.current_chunk_size:,}")

            elif time_diff < -self.time_tolerance:
                # 插入时间过短，增大块大小
                # 计算理论最优块大小
                target_chunk_size = int(chunk_records * self.target_insert_time / insert_time)
                # 限制单次调整幅度
                max_increase = int(self.current_chunk_size * 0.2)  # 最多增加20%
                adjustment = min(target_chunk_size - self.current_chunk_size, max_increase)

                self.current_chunk_size = min(self.max_chunk_size,
                                            self.current_chunk_size + adjustment)
                self.logger.info(f"[ADJUST] 插入时间过短({insert_time:.3f}s < {self.target_insert_time}s)，增大块大小: {old_chunk_size:,} → {self.current_chunk_size:,}")

            # 记录详细性能指标
            efficiency = (self.target_insert_time / insert_time) * 100 if insert_time > 0 else 0
            self.logger.info(f"[PERF] === 块大小调整完成 ===")
            self.logger.info(f"[PERF] 插入时间: {insert_time:.3f}秒")
            self.logger.info(f"[PERF] 记录数: {chunk_records:,}")
            self.logger.info(f"[PERF] 插入速度: {records_per_second:.0f} 记录/秒")
            self.logger.info(f"[PERF] 时间效率: {efficiency:.1f}%")
            self.logger.info(f"[PERF] 新块大小: {self.current_chunk_size:,}")

        except Exception as e:
            self.logger.error(f"[PERF] 动态调整块大小失败: {e}")
            import traceback
            self.logger.error(f"[PERF] 调整错误详情: {traceback.format_exc()}")

    def _read_file_with_dynamic_chunks(self, file_path: str):
        """使用真正的动态块大小读取文件，支持数据采样"""
        try:
            self.logger.info(f"开始动态读取文件: {file_path}")

            # 首先读取文件头信息
            sample_df = pd.read_csv(file_path, nrows=1)
            columns = sample_df.columns.tolist()

            # 获取文件总行数（不包括头）
            with open(file_path, 'r', encoding='utf-8') as f:
                total_lines = sum(1 for _ in f) - 1  # 减去头行

            # 计算采样后的目标行数
            if self.load_percentage < 1.0:
                target_lines = max(1, int(total_lines * self.load_percentage))
                self.logger.info(f"[SAMPLING] 文件总行数: {total_lines:,}, 采样目标: {target_lines:,} ({self.load_percentage * 100:.1f}%)")
            else:
                target_lines = total_lines
                self.logger.info(f"文件总行数: {total_lines:,}, 初始块大小: {self.current_chunk_size}")

            # 根据采样方式确定读取策略
            if self.load_percentage < 1.0 and self.sampling_method == 'head':
                # 头部采样：只读取前面的数据
                yield from self._read_head_sampling_chunks(file_path, columns, target_lines)
            elif self.load_percentage < 1.0 and self.sampling_method == 'tail':
                # 尾部采样：只读取后面的数据
                yield from self._read_tail_sampling_chunks(file_path, columns, total_lines, target_lines)
            elif self.load_percentage < 1.0 and self.sampling_method == 'random':
                # 随机采样：需要先读取全部数据再采样
                yield from self._read_random_sampling_chunks(file_path, columns, total_lines, target_lines)
            else:
                # 完整读取
                yield from self._read_full_chunks(file_path, columns, total_lines)

        except Exception as e:
            self.logger.error(f"动态读取文件失败: {e}")
            # 降级到普通读取
            try:
                self.logger.warning("降级到普通pandas读取")
                for chunk in pd.read_csv(file_path, chunksize=self.chunk_size):
                    yield chunk
            except Exception as fallback_error:
                self.logger.error(f"降级读取也失败: {fallback_error}")
                raise

    def _read_full_chunks(self, file_path: str, columns: list, total_lines: int):
        """完整读取文件块"""
        current_pos = 0
        chunk_id = 0

        while current_pos < total_lines:
            # 计算当前块的实际大小
            actual_chunk_size = min(self.current_chunk_size, total_lines - current_pos)

            self.logger.debug(f"读取块 {chunk_id}: 位置 {current_pos:,}-{current_pos + actual_chunk_size:,}, 块大小 {actual_chunk_size:,}")

            # 使用skiprows和nrows实现真正的动态块大小
            if current_pos == 0:
                # 第一个块：跳过标题行，读取数据
                chunk = pd.read_csv(
                    file_path,
                    skiprows=1,  # 只跳过标题行
                    nrows=actual_chunk_size,
                    names=columns  # 使用预先读取的列名
                )
            else:
                # 后续块：跳过标题行和之前的数据行
                chunk = pd.read_csv(
                    file_path,
                    skiprows=list(range(1)) + list(range(2, current_pos + 2)),  # 跳过标题行和之前的数据行
                    nrows=actual_chunk_size,
                    names=columns  # 使用预先读取的列名
                )

            if chunk.empty:
                break

            self.logger.debug(f"成功读取块 {chunk_id}: {len(chunk):,} 条记录")

            # 验证数据质量
            if not chunk.empty and 'DataQuality' in chunk.columns:
                sample_quality = chunk['DataQuality'].iloc[0] if len(chunk) > 0 else 'N/A'
                self.logger.debug(f"块 {chunk_id} 数据质量检查: DataQuality样本值={sample_quality}")

            yield chunk

            current_pos += len(chunk)
            chunk_id += 1

            # 如果读取的记录数少于预期，说明文件结束
            if len(chunk) < actual_chunk_size:
                break

        self.logger.info(f"动态读取完成: 总共读取 {chunk_id} 个块, {current_pos:,} 条记录")

    def _read_head_sampling_chunks(self, file_path: str, columns: list, target_lines: int):
        """头部采样读取文件块"""
        current_pos = 0
        chunk_id = 0

        while current_pos < target_lines:
            # 计算当前块的实际大小
            actual_chunk_size = min(self.current_chunk_size, target_lines - current_pos)

            self.logger.debug(f"[HEAD SAMPLING] 读取块 {chunk_id}: 位置 {current_pos:,}-{current_pos + actual_chunk_size:,}")

            # 使用skiprows和nrows实现头部采样
            if current_pos == 0:
                chunk = pd.read_csv(
                    file_path,
                    skiprows=1,
                    nrows=actual_chunk_size,
                    names=columns
                )
            else:
                chunk = pd.read_csv(
                    file_path,
                    skiprows=list(range(1)) + list(range(2, current_pos + 2)),
                    nrows=actual_chunk_size,
                    names=columns
                )

            if chunk.empty:
                break

            yield chunk

            current_pos += len(chunk)
            chunk_id += 1

            if len(chunk) < actual_chunk_size:
                break

        self.logger.info(f"[HEAD SAMPLING] 头部采样完成: 总共读取 {chunk_id} 个块, {current_pos:,} 条记录")

    def _read_tail_sampling_chunks(self, file_path: str, columns: list, total_lines: int, target_lines: int):
        """尾部采样读取文件块"""
        # 计算跳过的行数（不包括标题行）
        skip_lines = total_lines - target_lines

        self.logger.info(f"[TAIL SAMPLING] 跳过前 {skip_lines:,} 行，读取后 {target_lines:,} 行")

        current_pos = 0
        chunk_id = 0

        while current_pos < target_lines:
            actual_chunk_size = min(self.current_chunk_size, target_lines - current_pos)

            # 计算当前块在文件中的实际起始位置
            # skip_lines: 跳过的数据行数
            # current_pos: 当前已读取的目标行数
            # +1: 因为要跳过标题行
            actual_skip_rows = skip_lines + current_pos + 1

            self.logger.debug(f"[TAIL SAMPLING] 块 {chunk_id}: 跳过 {actual_skip_rows} 行，读取 {actual_chunk_size} 行")

            try:
                chunk = pd.read_csv(
                    file_path,
                    skiprows=actual_skip_rows,
                    nrows=actual_chunk_size,
                    names=columns,
                    header=None  # 因为我们已经跳过了标题行
                )

                if chunk.empty:
                    self.logger.debug(f"[TAIL SAMPLING] 块 {chunk_id} 为空，停止读取")
                    break

                self.logger.debug(f"[TAIL SAMPLING] 块 {chunk_id}: 实际读取 {len(chunk)} 行")

                yield chunk

                current_pos += len(chunk)
                chunk_id += 1

                if len(chunk) < actual_chunk_size:
                    self.logger.debug(f"[TAIL SAMPLING] 块 {chunk_id-1} 大小不足，已到文件末尾")
                    break

            except Exception as e:
                self.logger.error(f"[TAIL SAMPLING] 读取块 {chunk_id} 失败: {e}")
                break

        self.logger.info(f"[TAIL SAMPLING] 尾部采样完成: 总共读取 {chunk_id} 个块, {current_pos:,} 条记录")

    def _read_random_sampling_chunks(self, file_path: str, columns: list, total_lines: int, target_lines: int):
        """随机采样读取文件块（需要先读取全部数据）"""
        self.logger.warning(f"[RANDOM SAMPLING] 随机采样需要先读取全部数据，可能消耗较多内存")

        # 读取全部数据
        df = pd.read_csv(file_path, low_memory=False)

        # 随机采样
        sampled_df = df.sample(n=target_lines, random_state=42).sort_index()

        # 分块返回
        chunk_id = 0
        for start_idx in range(0, len(sampled_df), self.current_chunk_size):
            end_idx = min(start_idx + self.current_chunk_size, len(sampled_df))
            chunk = sampled_df.iloc[start_idx:end_idx]

            self.logger.debug(f"[RANDOM SAMPLING] 返回块 {chunk_id}: {len(chunk):,} 条记录")

            yield chunk
            chunk_id += 1

        self.logger.info(f"[RANDOM SAMPLING] 随机采样完成: 总共返回 {chunk_id} 个块, {len(sampled_df):,} 条记录")

    def _process_chunk(self, chunk: pd.DataFrame, station_id: str, device_name: str,
                      param_name: str, batch_id: str, file_path: str) -> List[Dict[str, Any]]:
        """处理数据块"""
        records = []
        
        for _, row in chunk.iterrows():
            record = {
                'station_id': station_id,
                'device_name': device_name,
                'param_name': param_name,
                'tag_name': str(row.get('TagName', 'unknown')),
                'data_time': str(row.get('DataTime', '2025-07-29 12:00:00')),
                'data_quality': int(row.get('DataQuality', 192)),
                'data_value': float(row.get('DataValue', 0.0)),
                'file_source': str(file_path),
                'batch_id': batch_id
            }
            records.append(record)
        
        return records
    
    def _write_chunk_to_database(self, processed_chunk: List[Dict], chunk_id: int) -> Dict[str, Any]:
        """直接写入数据块到数据库"""
        if not self.db_manager:
            self.logger.error("数据库管理器未初始化，无法写入数据")
            return {'success': False, 'error': '数据库管理器未初始化'}

        try:
            # 构建批量插入SQL
            sql = """
            INSERT INTO raw_data_by_device
            (station_id, device_name, param_name, tag_name, data_time,
             data_quality, data_value, file_source, batch_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            # 准备批量插入的数据
            values = [
                (
                    record.get('station_id', 'unknown'),
                    record.get('device_name', 'unknown'),
                    record.get('param_name', 'unknown'),
                    record.get('tag_name', 'unknown'),
                    record.get('data_time', '2025-07-29 12:00:00'),
                    record.get('data_quality', 192),
                    record.get('data_value', 0.0),
                    record.get('file_source', ''),
                    record.get('batch_id', 'unknown')
                )
                for record in processed_chunk
            ]

            # 执行批量插入
            start_time = time.time()
            affected_rows = self.db_manager.execute_batch_insert(sql, values)
            insert_time = time.time() - start_time

            self.logger.info(f"数据块写入成功: chunk_id={chunk_id}, 插入={affected_rows}条记录, 耗时={insert_time:.3f}秒")

            return {
                'success': True,
                'inserted_count': affected_rows,
                'processing_time': insert_time
            }

        except Exception as e:
            self.logger.error(f"数据块写入失败: chunk_id={chunk_id}, 错误={e}")
            return {
                'success': False,
                'error': str(e),
                'inserted_count': 0
            }
    
    def _process_chunk_for_target_table(self, chunk: pd.DataFrame, station_id: str,
                                       device_name: str, param_name: str, batch_id: str,
                                       file_path: str, target_table: str) -> List[Dict[str, Any]]:
        """🔧 为目标表处理数据块，支持时间对齐"""
        records = []

        # 🔧 使用单例数据转换器（已集成设备ID缓存）
        transformer = self.data_transformer if self.data_transformer else None
        if not transformer:
            # 备用方案：创建临时转换器
            from src.core.singleton_data_transformer import get_data_transformer
            transformer = get_data_transformer()
            self.logger.warning("使用备用数据转换器，无缓存优化")

        # 🔧 预查询设备ID（使用缓存）
        device_id = self._get_device_id_cached(device_name)
        self.logger.debug(f"设备ID查询结果: {device_name} -> {device_id}")

        # 首先转换为raw格式
        raw_records = []
        for _, row in chunk.iterrows():
            record = {
                'station_id': station_id,
                'device_name': device_name,
                'device_id': device_id,  # 🔧 添加设备ID
                'param_name': param_name,
                'tag_name': str(row.get('TagName', 'unknown')),
                'data_time': str(row.get('DataTime', '2025-07-29 12:00:00')),
                'data_quality': int(row.get('DataQuality', 192)),
                'data_value': float(row.get('DataValue', 0.0)),
                'file_source': str(file_path),
                'batch_id': batch_id
            }
            raw_records.append(record)

        # 转换为目标表格式
        self.logger.debug(f"[TARGET] 开始数据转换: {len(raw_records)}条记录 -> {target_table}")
        conversion_start = time.time()

        try:
            if target_table == 'pump_data':
                records = transformer._transform_to_pump_format(raw_records)
            elif target_table == 'main_pipe_data':
                records = transformer._transform_to_pipe_format(raw_records)
            else:
                records = raw_records

            conversion_time = time.time() - conversion_start
            self.logger.debug(f"[TARGET] 数据转换完成: 耗时{conversion_time:.3f}秒, 输出{len(records)}条记录")

        except Exception as e:
            conversion_time = time.time() - conversion_start
            self.logger.error(f"[TARGET] 数据转换失败: 耗时{conversion_time:.3f}秒, 错误: {e}")
            # 使用原始数据作为备用
            records = raw_records
            self.logger.warning(f"[TARGET] 使用原始数据格式作为备用: {len(records)}条记录")

        # 🔧 时间对齐处理（使用统一的数据转换器）
        records = self._align_timestamps_unified(records)

        self.logger.debug(f"[TARGET] 目标表处理完成: {len(chunk)} -> {len(records)} 条记录 ({target_table})")
        return records

    def _align_timestamps(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """🔧 智能时间对齐处理，集成缓存系统"""

        # 如果时间缓存未初始化，使用原始逻辑
        if self.time_cache is None:
            self.logger.warning("时间缓存未初始化，使用原始时间对齐逻辑")
            return self._align_timestamps_fallback(records)

        aligned_records = []
        processed_count = 0
        cache_hits = 0
        fast_path_hits = 0

        for record in records:
            processed_count += 1

            try:
                # 获取原始时间
                data_time = record.get('data_time', '')
                if isinstance(data_time, str) and data_time:
                    # 使用智能时间对齐缓存
                    aligned_time = self.time_cache.align_time(data_time)

                    # 统计缓存性能
                    if data_time == aligned_time:
                        fast_path_hits += 1
                    elif data_time in self.time_cache._time_cache:
                        cache_hits += 1

                    record['data_time'] = aligned_time
                else:
                    # 空时间值保持不变
                    pass

                aligned_records.append(record)

            except Exception as e:
                self.logger.warning(f"智能时间对齐失败: {data_time}, 错误: {e}")
                aligned_records.append(record)  # 保留原记录

        # 记录性能统计
        if processed_count > 0:
            fast_path_rate = (fast_path_hits / processed_count) * 100
            cache_hit_rate = (cache_hits / processed_count) * 100

            self.logger.debug(f"时间对齐性能统计: 处理{processed_count}条记录, "
                            f"快速路径命中率{fast_path_rate:.1f}%, "
                            f"缓存命中率{cache_hit_rate:.1f}%")

        return aligned_records

    def _align_timestamps_unified(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """🔧 统一时间对齐处理（使用数据转换器的时间对齐方法）"""
        if not self.data_transformer:
            self.logger.warning("数据转换器未初始化，使用备用时间对齐逻辑")
            return self._align_timestamps_fallback(records)

        aligned_records = []
        for record in records:
            try:
                # 获取原始时间
                data_time = record.get('data_time', '')
                if isinstance(data_time, str) and data_time:
                    # 使用数据转换器的统一时间对齐方法
                    aligned_time = self.data_transformer.align_timestamp(data_time)
                    record['data_time'] = aligned_time

                aligned_records.append(record)
            except Exception as e:
                self.logger.warning(f"统一时间对齐失败: {data_time}, 错误: {e}")
                aligned_records.append(record)  # 保留原记录

        return aligned_records

    def _align_timestamps_fallback(self, records: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """🔧 原始时间对齐处理（备用方案）"""
        from datetime import datetime

        aligned_records = []
        for record in records:
            try:
                # 获取原始时间
                data_time = record.get('data_time', '')
                if isinstance(data_time, str) and data_time:
                    # 解析时间并对齐到秒
                    dt = datetime.strptime(data_time, '%Y-%m-%d %H:%M:%S')
                    # 重新格式化，确保精确到秒
                    aligned_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                    record['data_time'] = aligned_time

                aligned_records.append(record)
            except Exception as e:
                self.logger.warning(f"时间对齐失败: {data_time}, 错误: {e}")
                aligned_records.append(record)  # 保留原记录

        return aligned_records

    def get_time_alignment_stats(self) -> Dict[str, Any]:
        """获取时间对齐性能统计"""
        if self.time_cache is None:
            return {
                'cache_enabled': False,
                'message': '时间缓存未初始化'
            }

        cache_stats = self.time_cache.get_cache_stats()
        return {
            'cache_enabled': True,
            'cache_stats': cache_stats,
            'cache_size': len(self.time_cache),
            'memory_usage_mb': cache_stats.get('cache_memory_mb', 0)
        }

    def optimize_time_cache_memory(self) -> Dict[str, Any]:
        """优化时间缓存内存使用"""
        if self.time_cache is None:
            return {
                'success': False,
                'message': '时间缓存未初始化'
            }

        return self.time_cache.optimize_memory()

    def _write_chunk_to_target_table(self, processed_chunk: List[Dict[str, Any]],
                                   chunk_id: int, target_table: str) -> Dict[str, Any]:
        """🔧 写入数据块到目标表"""
        start_time = time.time()

        if not processed_chunk:
            return {'success': True, 'inserted_count': 0, 'processing_time': 0}

        try:
            # 🔧 使用分区感知UPSERT替代INSERT，支持跨文件参数合并
            if target_table in ['pump_data', 'main_pipe_data', 'pump_data_aligned', 'main_pipe_data_aligned']:
                # 使用数据库管理器的分区感知UPSERT方法
                affected_rows = self.db_manager.execute_batch_upsert_partitioned(target_table, processed_chunk)

                self.logger.info(f"[UPSERT] {target_table} 表批量UPSERT完成: {len(processed_chunk)} 条记录，影响 {affected_rows} 行")

                return {
                    'success': True,
                    'records_processed': len(processed_chunk),
                    'db_affected_rows': affected_rows
                }

            # 传统INSERT逻辑（用于raw_data表）
            elif target_table == 'pump_data_old':
                sql = """
                INSERT INTO pump_data
                (device_id, pump_name, station_id, data_time, frequency, power, kwh, power_factor,
                 voltage_a, voltage_b, voltage_c, current_a, current_b, current_c,
                 outlet_pressure, outlet_flow, head, inlet_pressure, outlet_temperature,
                 pump_status, is_normal, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = [
                    (
                        record.get('device_id', 1),
                        record.get('pump_name', ''),
                        record.get('station_id', 1),
                        record.get('data_time', '2025-07-29 12:00:00'),
                        record.get('frequency', None),
                        record.get('power', None),
                        record.get('kwh', None),
                        record.get('power_factor', None),
                        record.get('voltage_a', None),
                        record.get('voltage_b', None),
                        record.get('voltage_c', None),
                        record.get('current_a', None),
                        record.get('current_b', None),
                        record.get('current_c', None),
                        record.get('outlet_pressure', None),
                        record.get('outlet_flow', None),
                        record.get('head', None),
                        record.get('inlet_pressure', None),
                        record.get('outlet_temperature', None),
                        record.get('pump_status', 'running'),
                        record.get('is_normal', 1),
                        record.get('created_at', datetime.now()),
                        record.get('updated_at', datetime.now())
                    )
                    for record in processed_chunk
                ]
            elif target_table == 'main_pipe_data':
                sql = """
                INSERT INTO main_pipe_data
                (device_id, main_pipe_name, station_id, data_time, pressure, flow_rate, cumulative_flow,
                 reserved_decimal_1, reserved_decimal_2, reserved_decimal_3, reserved_decimal_4,
                 reserved_decimal_5, reserved_decimal_6, reserved_decimal_7, normal, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                values = [
                    (
                        record.get('device_id', 1),
                        record.get('main_pipe_name', ''),
                        record.get('station_id', 1),
                        record.get('data_time', '2025-07-29 12:00:00'),
                        record.get('pressure', None),
                        record.get('flow_rate', None),
                        record.get('cumulative_flow', None),
                        record.get('reserved_decimal_1', None),
                        record.get('reserved_decimal_2', None),
                        record.get('reserved_decimal_3', None),
                        record.get('reserved_decimal_4', None),
                        record.get('reserved_decimal_5', None),
                        record.get('reserved_decimal_6', None),
                        record.get('reserved_decimal_7', None),
                        record.get('normal', 1),
                        record.get('created_at', datetime.now()),
                        record.get('updated_at', None)
                    )
                    for record in processed_chunk
                ]
            else:
                # 回退到原来的raw_data_by_device表
                return self._write_chunk_to_database(processed_chunk, chunk_id)

            # 执行批量插入
            affected_rows = self.db_manager.execute_batch_insert(sql, values)
            insert_time = time.time() - start_time

            self.logger.info(f"[TARGET] 数据块写入成功: table={target_table}, chunk_id={chunk_id}, "
                           f"插入={affected_rows}条记录, 耗时={insert_time:.3f}秒")

            return {
                'success': True,
                'inserted_count': affected_rows,
                'processing_time': insert_time
            }

        except Exception as e:
            self.logger.error(f"[TARGET] 数据块写入失败: table={target_table}, chunk_id={chunk_id}, 错误={e}")
            return {
                'success': False,
                'error': str(e),
                'inserted_count': 0
            }

    def _memory_cleanup(self, chunk_id: int):
        """内存清理"""
        if chunk_id % 5 == 0:  # 每5个块清理一次
            current_memory = psutil.Process().memory_info().rss
            memory_gb = current_memory / 1024 / 1024 / 1024

            if memory_gb > 2.0:  # 超过2GB时强制清理
                self.logger.info(f"内存使用: {memory_gb:.2f}GB, 执行垃圾回收")
                gc.collect()
                
                # 再次检查内存
                new_memory = psutil.Process().memory_info().rss / 1024 / 1024 / 1024
                self.logger.info(f"垃圾回收后内存: {new_memory:.2f}GB")
            
            # 检查内存使用率
            if current_memory > self.memory_limit * 0.8:
                self.logger.warning(f"内存使用率过高: {memory_gb:.2f}GB")

    def cleanup_resources(self):
        """清理所有资源"""
        self.logger.info("开始清理StreamingProcessor资源")

        try:
            # 1. 清理数据库连接
            if hasattr(self, 'db_manager') and self.db_manager:
                self.logger.info("清理数据库管理器资源")
                self.db_manager.cleanup_resources()
                self.db_manager = None

            # 2. 清理缓存
            if hasattr(self, 'time_cache') and self.time_cache:
                self.logger.info("清理时间对齐缓存")
                if hasattr(self.time_cache, 'cleanup_resources'):
                    self.time_cache.cleanup_resources()
                elif hasattr(self.time_cache, 'clear_cache'):
                    self.time_cache.clear_cache()
                elif hasattr(self.time_cache, 'clear'):
                    self.time_cache.clear()
                self.time_cache = None

            if hasattr(self, 'device_cache') and self.device_cache:
                self.logger.info("清理设备缓存")
                if hasattr(self.device_cache, 'cleanup_resources'):
                    self.device_cache.cleanup_resources()
                elif hasattr(self.device_cache, 'clear_cache'):
                    self.device_cache.clear_cache()
                elif hasattr(self.device_cache, 'clear'):
                    self.device_cache.clear()
                self.device_cache = None

            # 3. 强制垃圾回收
            self.logger.info("执行最终垃圾回收")
            import gc
            collected = gc.collect()
            self.logger.info(f"垃圾回收完成，回收对象数: {collected}")

            # 4. 重置配置参数
            self.current_chunk_size = self.chunk_size

            self.logger.info("StreamingProcessor资源清理完成")

        except Exception as e:
            self.logger.error(f"StreamingProcessor资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        self.cleanup_resources()

        if exc_type is not None:
            self.logger.error(f"StreamingProcessor退出时发生异常: {exc_type.__name__}: {exc_val}")

        return False  # 不抑制异常
