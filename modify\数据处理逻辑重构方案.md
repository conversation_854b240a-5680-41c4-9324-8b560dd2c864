# 数据处理逻辑重构方案

**生成时间**: 2025-08-01  
**问题**: 时间对齐和表分布问题  
**状态**: 🔧 准备重构

## 🚨 问题确认

### 当前错误逻辑
```
设备1有9个参数文件:
- 频率.csv (25,914行)
- 电压A.csv (25,914行)  
- 电流A.csv (25,914行)
- 电压B.csv (25,914行)
- 电流B.csv (25,914行)
- 电压C.csv (25,914行)
- 电流C.csv (25,914行)
- 功率.csv (25,914行)
- 电度.csv (25,914行)

当前处理: 每个文件单独处理 → 9 × 25,914 = 233,226条记录
正确处理: 按时间合并 → 25,914条记录 (每条包含9个参数)
```

### 重复记录证据
```
设备1在2025-05-03 08:48:35有69条重复记录
= 9个参数文件 × 7.67个重复值 (可能有部分文件重复处理)
```

## 🎯 重构目标

### 1. 数据合并逻辑
- **按设备分组**: 同一设备的所有参数文件一起处理
- **按时间合并**: 同一时间点的所有参数合并为一条记录
- **参数映射**: 正确映射参数到数据库字段

### 2. 表路由修复
- **管道文件路径**: 修复配置文件中的路径错误
- **设备类型识别**: 确保管道设备正确路由到main_pipe_data表
- **文件处理顺序**: 优先处理管道文件

### 3. 时间对齐优化
- **唯一时间约束**: 确保每个(device_id, data_time)组合只有一条记录
- **时间格式统一**: 标准化时间格式处理
- **重复检测**: 在插入前检测并合并重复记录

## 🛠️ 实施步骤

### 第一步: 修复配置文件
1. 更新管道文件路径配置
2. 验证所有文件路径存在

### 第二步: 重构数据转换器
1. 修改`_transform_to_pump_format`方法
2. 实现按设备分组的数据合并逻辑
3. 添加重复记录检测和合并

### 第三步: 修复表选择逻辑
1. 确保管道设备正确识别
2. 修复设备类型到表名的映射

### 第四步: 清理和重新处理
1. 清空所有数据表
2. 重新运行数据处理程序
3. 验证结果

## 📋 验证标准

### 成功标准
1. **pump_data表**: 每个设备每个时间点只有1条记录
2. **main_pipe_data表**: 包含管道设备数据 (>0条记录)
3. **时间唯一性**: 无重复时间戳
4. **参数完整性**: 每条记录包含多个参数值

### 预期结果
```
设备1: 25,914条记录 (而不是233,226条)
设备2-12: 每个设备约25,914条记录
管道设备: main_pipe_data表有数据
总记录数: 约13 × 25,914 = 336,882条 (而不是2,935,454条)
```

## 🔧 技术实现

### 数据合并算法
```python
def merge_device_data_by_time(device_files):
    """按时间合并设备的所有参数数据"""
    time_grouped_data = {}
    
    for param_name, file_path in device_files.items():
        df = read_csv(file_path)
        for _, row in df.iterrows():
            time_key = row['DataTime']
            if time_key not in time_grouped_data:
                time_grouped_data[time_key] = {}
            time_grouped_data[time_key][param_name] = row['DataValue']
    
    return time_grouped_data
```

### 重复检测逻辑
```python
def detect_and_merge_duplicates(records):
    """检测并合并重复记录"""
    unique_records = {}
    
    for record in records:
        key = (record['device_id'], record['data_time'])
        if key in unique_records:
            # 合并参数值
            unique_records[key].update(record)
        else:
            unique_records[key] = record
    
    return list(unique_records.values())
```

这个重构将彻底解决时间对齐和表分布问题。
