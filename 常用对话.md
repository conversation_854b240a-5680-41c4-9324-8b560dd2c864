开始开发工作。
解决遇到的错误和问题和警告。
遇到的所有问题、错误、警告都要解决。
严格遵守约定。
逐步思考，一步一步的细致解决问题彻底解决问题。
遇到困难问题，倒着排查代码。
完成一个，测试一个。
完成一个，添加完整日志功能。
日志分析+结果验证才是确定是否有问题、错误、警依据，不能简单看终端输出。
保证每次启动程序前，清空cmdout和logs目录。
在前台运行程序。

继续工作。
解决遇到的错误和问题和警告。
遇到的所有问题、错误、警告都要解决。
严格遵守约定。
逐步思考，一步一步的细致解决问题彻底解决问题。
遇到困难问题，倒着排查代码。
完成一个，测试一个。
完成一个，添加完整日志功能。
日志分析+结果验证才是确定是否有问题、错误、警依据，不能简单看终端输出。
保证每次启动程序前，清空cmdout和logs目录。
在前台运行程序。


清空数据库所有表格和分的数据，运行完整程序处理csv映射文件中的所有泵站数据，持续观察程序是否存在问题、检查数据库表中数据是否正确直到程序正常结束。
通过分析程序日志、终端输出、数据库表中的内容发现和解决遇到的错误、问题、警告和功能缺失、缺陷，日志缺失和不全，立刻停止程序解决问题，然后再次测试。，每次启动程序前，彻底清空数据库中的所有表和分区。
严格实事求是，不要说谎。
遇到的所有问题、错误、警告都要解决。
严格遵守约定。
逐步思考，一步一步的细致解决问题彻底解决问题。
遇到困难问题，倒着排查代码。
完成一个，测试一个。
完成一个，添加完整日志功能。
日志分析+结果验证才是确定是否有问题、错误、警依据，不能简单看终端输出。
保证每次启动程序前，清空cmdout和logs目录。
在前台运行程序，让我能看到终端输出。
我们删除了一些代码，因为重复了，能用就用，不能用就修改，不行就删除原有代码新建。
配置文件是正确的！

 