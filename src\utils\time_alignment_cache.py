#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间对齐缓存系统
智能时间对齐处理，支持快速格式检查和已转换时间值缓存
"""

import re
import time
import threading
from typing import Dict, Optional, Any, List, Tuple, Set
from collections import OrderedDict
from datetime import datetime
import logging
import gc

class TimeAlignmentCache:
    """
    智能时间对齐缓存系统
    
    特性:
    - 快速格式检查，跳过无意义转换
    - 时间值缓存，避免重复解析
    - 多格式支持
    - 内存友好设计
    - 性能统计
    """
    
    # 支持的时间格式定义 (扩展版)
    TIME_FORMATS = {
        # 标准格式系列
        'standard': '%Y-%m-%d %H:%M:%S',           # 标准格式: 2025-04-30 16:00:00
        'standard_ms': '%Y-%m-%d %H:%M:%S.%f',     # 毫秒格式: 2025-04-30 16:00:00.123
        'standard_us': '%Y-%m-%d %H:%M:%S.%f',     # 微秒格式: 2025-04-30 16:00:00.123456

        # ISO格式系列
        'iso': '%Y-%m-%dT%H:%M:%S',                # ISO格式: 2025-04-30T16:00:00
        'iso_ms': '%Y-%m-%dT%H:%M:%S.%f',          # ISO毫秒: 2025-04-30T16:00:00.123
        'iso_z': '%Y-%m-%dT%H:%M:%SZ',             # ISO UTC: 2025-04-30T16:00:00Z
        'iso_tz': '%Y-%m-%dT%H:%M:%S%z',           # ISO时区: 2025-04-30T16:00:00+08:00

        # 紧凑格式系列
        'compact': '%Y%m%d%H%M%S',                 # 紧凑格式: 20250430160000
        'compact_ms': '%Y%m%d%H%M%S%f',            # 紧凑毫秒: 20250430160000123

        # 斜杠分隔格式
        'slash_date': '%Y/%m/%d %H:%M:%S',         # 斜杠格式: 2025/04/30 16:00:00
        'slash_date_ms': '%Y/%m/%d %H:%M:%S.%f',   # 斜杠毫秒: 2025/04/30 16:00:00.123

        # 中文格式
        'chinese_date': '%Y年%m月%d日 %H:%M:%S',    # 中文格式: 2025年04月30日 16:00:00

        # 部分格式
        'date_only': '%Y-%m-%d',                   # 日期格式: 2025-04-30
        'time_only': '%H:%M:%S',                   # 时间格式: 16:00:00
        'time_ms': '%H:%M:%S.%f',                  # 时间毫秒: 16:00:00.123

        # 美式格式
        'us_date': '%m/%d/%Y %H:%M:%S',            # 美式格式: 04/30/2025 16:00:00
        'us_date_ms': '%m/%d/%Y %H:%M:%S.%f',      # 美式毫秒: 04/30/2025 16:00:00.123

        # 欧式格式
        'eu_date': '%d/%m/%Y %H:%M:%S',            # 欧式格式: 30/04/2025 16:00:00
        'eu_date_ms': '%d/%m/%Y %H:%M:%S.%f',      # 欧式毫秒: 30/04/2025 16:00:00.123
    }
    
    # 格式检查正则表达式 (扩展版)
    FORMAT_PATTERNS = {
        # 标准格式系列
        'standard': re.compile(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'),
        'standard_ms': re.compile(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{1,6}$'),
        'standard_us': re.compile(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{6}$'),

        # ISO格式系列
        'iso': re.compile(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$'),
        'iso_ms': re.compile(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{1,6}$'),
        'iso_z': re.compile(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$'),
        'iso_tz': re.compile(r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$'),

        # 紧凑格式系列
        'compact': re.compile(r'^\d{14}$'),
        'compact_ms': re.compile(r'^\d{17,20}$'),

        # 斜杠分隔格式
        'slash_date': re.compile(r'^\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}$'),
        'slash_date_ms': re.compile(r'^\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}\.\d{1,6}$'),

        # 中文格式
        'chinese_date': re.compile(r'^\d{4}年\d{1,2}月\d{1,2}日 \d{2}:\d{2}:\d{2}$'),

        # 部分格式
        'date_only': re.compile(r'^\d{4}-\d{2}-\d{2}$'),
        'time_only': re.compile(r'^\d{2}:\d{2}:\d{2}$'),
        'time_ms': re.compile(r'^\d{2}:\d{2}:\d{2}\.\d{1,6}$'),

        # 美式格式
        'us_date': re.compile(r'^\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}$'),
        'us_date_ms': re.compile(r'^\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}\.\d{1,6}$'),

        # 欧式格式
        'eu_date': re.compile(r'^\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}$'),
        'eu_date_ms': re.compile(r'^\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}\.\d{1,6}$'),

        # Unix时间戳
        'timestamp': re.compile(r'^\d{10}$'),
        'timestamp_ms': re.compile(r'^\d{13}$'),
    }

    # 快速检查模式 - 标准格式
    FAST_CHECK_PATTERN = re.compile(r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$')
    
    def __init__(self,
                 target_format: str = 'standard',
                 max_cache_size: int = 100000,
                 memory_limit_mb: float = 200.0,
                 enable_fast_check: bool = True,
                 alignment_interval_seconds: int = 1):
        """
        初始化时间对齐缓存系统

        Args:
            target_format: 目标时间格式 (默认'standard')
            max_cache_size: 最大缓存条目数 (默认1000个时间值)
            memory_limit_mb: 内存限制MB (默认10MB)
            enable_fast_check: 是否启用快速格式检查 (默认True)
            alignment_interval_seconds: 时间对齐间隔（秒），默认60秒（1分钟）
        """
        self.target_format = target_format
        self.target_format_str = self.TIME_FORMATS.get(target_format, self.TIME_FORMATS['standard'])
        self.max_cache_size = max_cache_size
        self.memory_limit_mb = memory_limit_mb
        self.enable_fast_check = enable_fast_check
        self.alignment_interval_seconds = alignment_interval_seconds
        
        # 时间值缓存 - 使用OrderedDict实现LRU
        self._time_cache: OrderedDict[str, str] = OrderedDict()
        
        # 格式检查缓存 - 缓存已识别的格式
        self._format_cache: Dict[str, str] = {}
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 性能统计
        self._stats = {
            'total_requests': 0,
            'fast_path_hits': 0,      # 快速路径命中 (无需转换)
            'cache_hits': 0,          # 缓存命中
            'cache_misses': 0,        # 缓存未命中
            'format_detections': 0,   # 格式检测次数
            'parsing_errors': 0,      # 解析错误次数
            'memory_cleanups': 0,     # 内存清理次数
            'cache_evictions': 0,     # 缓存清理次数
            'batch_requests': 0,      # 批量请求次数
            'batch_fast_path_hits': 0, # 批量快速路径命中
            'format_learning_sessions': 0, # 格式学习会话
            'learned_formats': 0      # 学习到的格式数
        }
        
        # 日志记录器
        self.logger = logging.getLogger(__name__)
        
        self.logger.info(f"时间对齐缓存系统初始化完成:")
        self.logger.info(f"  目标格式: {target_format} ({self.target_format_str})")
        self.logger.info(f"  最大缓存大小: {max_cache_size}个时间值")
        self.logger.info(f"  内存限制: {memory_limit_mb}MB")
        self.logger.info(f"  快速检查: {enable_fast_check}")
    
    def _detect_time_format(self, time_str: str) -> Optional[str]:
        """
        检测时间字符串的格式
        
        Args:
            time_str: 时间字符串
            
        Returns:
            格式名称，如果无法识别返回None
        """
        if not time_str or not isinstance(time_str, str):
            return None
        
        # 检查格式缓存
        if time_str in self._format_cache:
            return self._format_cache[time_str]
        
        # 使用正则表达式快速检测
        for format_name, pattern in self.FORMAT_PATTERNS.items():
            if pattern.match(time_str):
                self._format_cache[time_str] = format_name
                self._stats['format_detections'] += 1
                return format_name
        
        return None
    
    def _is_target_format(self, time_str: str) -> bool:
        """
        检查时间字符串是否已经是目标格式
        
        Args:
            time_str: 时间字符串
            
        Returns:
            是否为目标格式
        """
        detected_format = self._detect_time_format(time_str)
        return detected_format == self.target_format
    
    def _parse_and_format_time(self, time_str: str) -> Optional[str]:
        """
        解析并格式化时间字符串 (增强版)

        Args:
            time_str: 原始时间字符串

        Returns:
            格式化后的时间字符串，失败返回None
        """
        if not time_str:
            return None

        # 检测输入格式
        input_format = self._detect_time_format(time_str)

        # 处理Unix时间戳
        if input_format == 'timestamp':
            try:
                timestamp = int(time_str)
                dt = datetime.fromtimestamp(timestamp)
                return dt.strftime(self.target_format_str)
            except (ValueError, OSError) as e:
                self.logger.warning(f"Unix时间戳转换失败: {time_str}, 错误: {e}")
                return None

        elif input_format == 'timestamp_ms':
            try:
                timestamp_ms = int(time_str)
                timestamp = timestamp_ms / 1000.0
                dt = datetime.fromtimestamp(timestamp)
                return dt.strftime(self.target_format_str)
            except (ValueError, OSError) as e:
                self.logger.warning(f"毫秒时间戳转换失败: {time_str}, 错误: {e}")
                return None

        # 处理已知格式
        if input_format and input_format in self.TIME_FORMATS:
            # 如果已经是目标格式，直接返回
            if input_format == self.target_format:
                return time_str

            # 转换格式
            try:
                input_format_str = self.TIME_FORMATS[input_format]

                # 处理微秒格式的特殊情况
                if input_format.endswith('_ms') or input_format.endswith('_us'):
                    # 对于微秒格式，需要特殊处理
                    if '.' in time_str:
                        # 截取微秒部分，确保不超过6位
                        parts = time_str.split('.')
                        if len(parts) == 2:
                            microseconds = parts[1][:6].ljust(6, '0')  # 补齐到6位
                            time_str = f"{parts[0]}.{microseconds}"

                dt = datetime.strptime(time_str, input_format_str)
                formatted_time = dt.strftime(self.target_format_str)
                return formatted_time

            except (ValueError, KeyError) as e:
                self.logger.warning(f"已知格式转换失败: {time_str}, 格式: {input_format}, 错误: {e}")

        # 未知格式，尝试常见格式解析
        if not input_format:
            # 按优先级尝试格式解析
            priority_formats = [
                'standard', 'standard_ms', 'iso', 'iso_ms',
                'slash_date', 'us_date', 'eu_date', 'compact'
            ]

            for format_name in priority_formats:
                if format_name in self.TIME_FORMATS:
                    try:
                        format_str = self.TIME_FORMATS[format_name]
                        dt = datetime.strptime(time_str, format_str)
                        formatted_time = dt.strftime(self.target_format_str)

                        # 缓存检测到的格式
                        self._format_cache[time_str] = format_name
                        self._stats['format_detections'] += 1

                        return formatted_time
                    except ValueError:
                        continue

            self._stats['parsing_errors'] += 1
            self.logger.warning(f"无法解析时间格式: {time_str}")
            return None

        self._stats['parsing_errors'] += 1
        return None

    def _align_to_interval(self, time_str: str) -> str:
        """
        将时间对齐到指定间隔

        Args:
            time_str: 时间字符串（标准格式）

        Returns:
            对齐后的时间字符串
        """
        try:
            # 解析时间
            dt = datetime.strptime(time_str, self.target_format_str)

            # 计算对齐后的时间
            # 将时间对齐到指定间隔的整数倍
            total_seconds = dt.hour * 3600 + dt.minute * 60 + dt.second
            aligned_seconds = (total_seconds // self.alignment_interval_seconds) * self.alignment_interval_seconds

            # 重新构造对齐后的时间
            aligned_hour = aligned_seconds // 3600
            aligned_minute = (aligned_seconds % 3600) // 60
            aligned_second = aligned_seconds % 60

            # 创建对齐后的datetime对象
            aligned_dt = dt.replace(hour=aligned_hour, minute=aligned_minute, second=aligned_second, microsecond=0)

            # 格式化返回
            aligned_time_str = aligned_dt.strftime(self.target_format_str)

            self.logger.debug(f"时间间隔对齐: {time_str} -> {aligned_time_str} (间隔: {self.alignment_interval_seconds}秒)")
            return aligned_time_str

        except Exception as e:
            self.logger.warning(f"时间间隔对齐失败: {time_str}, 错误: {e}")
            return time_str  # 返回原值
    
    def _cleanup_cache(self, target_size: Optional[int] = None) -> int:
        """
        清理时间缓存
        
        Args:
            target_size: 目标缓存大小
            
        Returns:
            清理的条目数
        """
        if not self._time_cache:
            return 0
        
        if target_size is None:
            target_size = int(self.max_cache_size * 0.7)  # 清理到70%
        
        removed_count = 0
        
        # 移除最少使用的条目
        while len(self._time_cache) > target_size and self._time_cache:
            self._time_cache.popitem(last=False)
            removed_count += 1
        
        if removed_count > 0:
            self._stats['memory_cleanups'] += 1
            self.logger.debug(f"时间缓存清理完成: 移除{removed_count}个条目")
        
        return removed_count
    
    def _get_cache_memory_usage(self) -> float:
        """估算缓存内存使用量(MB)"""
        if not self._time_cache:
            return 0.0
        
        # 估算每个缓存条目的内存使用
        # 时间字符串(平均20字符) × 2 + OrderedDict开销
        avg_time_str_size = 20  # 字符
        entry_size = (avg_time_str_size * 2 * 2) + 64  # 输入+输出字符串 + 开销
        
        total_size = len(self._time_cache) * entry_size
        format_cache_size = len(self._format_cache) * 50  # 格式缓存
        
        return (total_size + format_cache_size) / 1024 / 1024  # 转换为MB

    def _estimate_cache_memory(self) -> float:
        """估算缓存内存使用量(MB) - 别名方法"""
        return self._get_cache_memory_usage()
    
    def align_time(self, time_str: str) -> str:
        """
        对齐时间字符串到目标格式
        
        Args:
            time_str: 原始时间字符串
            
        Returns:
            对齐后的时间字符串
        """
        if not time_str or not isinstance(time_str, str):
            return time_str
        
        with self._lock:
            self._stats['total_requests'] += 1
            
            # 🔧 修改快速路径检查 - 即使是目标格式，也需要进行时间间隔对齐
            if self.enable_fast_check and self._is_target_format(time_str):
                # 对标准格式的时间进行间隔对齐
                aligned_time = self._align_to_interval(time_str)
                self._stats['fast_path_hits'] += 1
                self.logger.debug(f"快速路径+间隔对齐: {time_str} -> {aligned_time}")

                # 缓存结果
                if len(self._time_cache) < self.max_cache_size:
                    self._time_cache[time_str] = aligned_time

                return aligned_time
            
            # 检查缓存
            if time_str in self._time_cache:
                # 缓存命中 - 移动到末尾 (最近使用)
                aligned_time = self._time_cache.pop(time_str)
                self._time_cache[time_str] = aligned_time
                self._stats['cache_hits'] += 1
                self.logger.debug(f"缓存命中: {time_str} -> {aligned_time}")
                return aligned_time
            
            # 缓存未命中 - 解析并格式化
            self._stats['cache_misses'] += 1
            formatted_time = self._parse_and_format_time(time_str)

            if formatted_time is None:
                # 解析失败，返回原值
                return time_str

            # 🔧 对格式化后的时间进行间隔对齐
            aligned_time = self._align_to_interval(formatted_time)

            # 检查是否需要清理缓存
            if len(self._time_cache) >= self.max_cache_size:
                self._cleanup_cache()

            # 缓存结果
            self._time_cache[time_str] = aligned_time

            self.logger.debug(f"时间对齐: {time_str} -> {aligned_time}")
            return aligned_time
    
    def align_time_batch(self, time_strings: List[str]) -> List[str]:
        """
        批量对齐时间字符串 (优化版)

        特性:
        - 批量快速路径检查
        - 格式分组处理
        - 内存友好的分块处理
        - 智能缓存预热

        Args:
            time_strings: 时间字符串列表

        Returns:
            对齐后的时间字符串列表
        """
        if not time_strings:
            return []

        # 如果数据量小，直接使用原方法
        if len(time_strings) <= 50:
            return self._align_time_batch_simple(time_strings)

        # 大数据量使用优化批处理
        return self._align_time_batch_optimized(time_strings)

    def _align_time_batch_simple(self, time_strings: List[str]) -> List[str]:
        """简单批量处理（小数据量）"""
        aligned_times = []
        for time_str in time_strings:
            aligned_time = self.align_time(time_str)
            aligned_times.append(aligned_time)
        return aligned_times

    def _align_time_batch_optimized(self, time_strings: List[str]) -> List[str]:
        """优化批量处理（大数据量）"""
        aligned_times = []

        with self._lock:
            # 1. 批量快速路径检查
            fast_path_results = self._batch_fast_path_check(time_strings)

            # 2. 格式分组处理
            format_groups = self._group_by_format(time_strings, fast_path_results)

            # 3. 分组批量处理
            for format_type, group_data in format_groups.items():
                if format_type == 'fast_path':
                    # 🔧 快速路径：进行间隔对齐
                    for idx, time_str in group_data:
                        aligned_time = self._align_to_interval(time_str)
                        aligned_times.append((idx, aligned_time))
                        self._stats['fast_path_hits'] += 1

                        # 缓存结果
                        if len(self._time_cache) < self.max_cache_size:
                            self._time_cache[time_str] = aligned_time
                else:
                    # 需要转换的数据
                    converted_results = self._batch_convert_format(group_data, format_type)
                    aligned_times.extend(converted_results)

        # 4. 按原始顺序排序并返回
        aligned_times.sort(key=lambda x: x[0])
        return [result[1] for result in aligned_times]

    def _batch_fast_path_check(self, time_strings: List[str]) -> Dict[int, bool]:
        """批量快速路径检查"""
        fast_path_results = {}

        if not self.enable_fast_check:
            return fast_path_results

        for i, time_str in enumerate(time_strings):
            if isinstance(time_str, str) and time_str:
                # 使用快速检查正则
                is_fast_path = bool(self.FAST_CHECK_PATTERN.match(time_str))
                fast_path_results[i] = is_fast_path
            else:
                fast_path_results[i] = False

        return fast_path_results

    def _group_by_format(self, time_strings: List[str], fast_path_results: Dict[int, bool]) -> Dict[str, List[Tuple[int, str]]]:
        """按格式分组时间字符串"""
        format_groups = {
            'fast_path': [],
            'unknown': []
        }

        for i, time_str in enumerate(time_strings):
            if fast_path_results.get(i, False):
                format_groups['fast_path'].append((i, time_str))
            else:
                # 检测格式
                detected_format = self._detect_time_format(time_str)
                if detected_format and detected_format != self.target_format:
                    if detected_format not in format_groups:
                        format_groups[detected_format] = []
                    format_groups[detected_format].append((i, time_str))
                else:
                    format_groups['unknown'].append((i, time_str))

        return format_groups

    def _batch_convert_format(self, group_data: List[Tuple[int, str]], format_type: str) -> List[Tuple[int, str]]:
        """批量转换指定格式的时间字符串"""
        converted_results = []

        if format_type == 'unknown':
            # 未知格式，逐个处理
            for idx, time_str in group_data:
                aligned_time = self._parse_and_format_time(time_str)
                if aligned_time:
                    converted_results.append((idx, aligned_time))
                    # 缓存结果
                    if len(self._time_cache) < self.max_cache_size:
                        self._time_cache[time_str] = aligned_time
                    self._stats['cache_misses'] += 1
                else:
                    converted_results.append((idx, time_str))  # 保持原值
        else:
            # 已知格式，批量转换
            format_str = self.TIME_FORMATS.get(format_type)
            if format_str:
                for idx, time_str in group_data:
                    try:
                        # 检查缓存
                        if time_str in self._time_cache:
                            aligned_time = self._time_cache[time_str]
                            self._stats['cache_hits'] += 1
                        else:
                            # 批量解析
                            aligned_time = self._batch_parse_time(time_str, format_type, format_str)
                            if aligned_time and len(self._time_cache) < self.max_cache_size:
                                self._time_cache[time_str] = aligned_time
                            self._stats['cache_misses'] += 1

                        converted_results.append((idx, aligned_time or time_str))
                    except Exception as e:
                        self.logger.warning(f"批量转换失败: {time_str}, 格式: {format_type}, 错误: {e}")
                        converted_results.append((idx, time_str))
            else:
                # 格式不支持，保持原值
                for idx, time_str in group_data:
                    converted_results.append((idx, time_str))

        return converted_results

    def _batch_parse_time(self, time_str: str, format_type: str, format_str: str) -> Optional[str]:
        """批量解析时间字符串（优化版）"""
        try:
            # Unix时间戳特殊处理
            if format_type == 'timestamp':
                timestamp = int(time_str)
                dt = datetime.fromtimestamp(timestamp)
                formatted_time = dt.strftime(self.target_format_str)
            elif format_type == 'timestamp_ms':
                timestamp_ms = int(time_str)
                timestamp = timestamp_ms / 1000.0
                dt = datetime.fromtimestamp(timestamp)
                formatted_time = dt.strftime(self.target_format_str)
            else:
                # 标准格式解析
                dt = datetime.strptime(time_str, format_str)
                formatted_time = dt.strftime(self.target_format_str)

            # 🔧 对格式化后的时间进行间隔对齐
            aligned_time = self._align_to_interval(formatted_time)
            return aligned_time

        except (ValueError, OSError) as e:
            self.logger.debug(f"批量解析失败: {time_str}, 格式: {format_type}, 错误: {e}")
            return None

    def preload_cache_smart(self, time_samples: List[str], max_preload: int = 500) -> Dict[str, Any]:
        """智能缓存预热 (优化版)"""
        if not time_samples:
            return {
                'total_samples': 0,
                'preloaded_count': 0,
                'format_distribution': {},
                'preload_time_ms': 0.0
            }

        import time as time_module
        start_time = time_module.time()

        # 限制预加载数量
        samples_to_process = time_samples[:max_preload]

        format_distribution = {}
        preloaded_count = 0

        with self._lock:
            # 1. 快速格式分析
            format_groups = {}
            for time_str in samples_to_process:
                if not time_str or not isinstance(time_str, str):
                    continue

                # 检查是否已在缓存中
                if time_str in self._time_cache:
                    continue

                # 检测格式
                detected_format = self._detect_time_format(time_str)
                if detected_format:
                    format_distribution[detected_format] = format_distribution.get(detected_format, 0) + 1

                    # 只预加载非目标格式
                    if detected_format != self.target_format:
                        if detected_format not in format_groups:
                            format_groups[detected_format] = []
                        format_groups[detected_format].append(time_str)

            # 2. 批量预加载
            for format_type, time_strings in format_groups.items():
                if len(self._time_cache) >= self.max_cache_size:
                    break

                format_str = self.TIME_FORMATS.get(format_type)
                if format_str:
                    for time_str in time_strings:
                        if len(self._time_cache) >= self.max_cache_size:
                            break

                        aligned_time = self._batch_parse_time(time_str, format_type, format_str)
                        if aligned_time:
                            self._time_cache[time_str] = aligned_time
                            preloaded_count += 1

        preload_time = (time_module.time() - start_time) * 1000

        return {
            'total_samples': len(samples_to_process),
            'preloaded_count': preloaded_count,
            'format_distribution': format_distribution,
            'preload_time_ms': preload_time
        }

    def align_time_batch_chunked(self, time_strings: List[str], chunk_size: int = 1000) -> List[str]:
        """分块批量对齐时间字符串（内存友好）"""
        if not time_strings:
            return []

        if len(time_strings) <= chunk_size:
            return self.align_time_batch(time_strings)

        # 分块处理
        aligned_results = []
        total_chunks = (len(time_strings) + chunk_size - 1) // chunk_size

        for i in range(0, len(time_strings), chunk_size):
            chunk = time_strings[i:i + chunk_size]
            chunk_results = self.align_time_batch(chunk)
            aligned_results.extend(chunk_results)

            # 记录进度
            current_chunk = (i // chunk_size) + 1
            if current_chunk % 10 == 0 or current_chunk == total_chunks:
                self.logger.debug(f"分块处理进度: {current_chunk}/{total_chunks}")

            # 内存优化：定期清理
            if current_chunk % 50 == 0:
                self._optimize_memory_usage()

        return aligned_results

    def _optimize_memory_usage(self):
        """优化内存使用"""
        with self._lock:
            # 如果缓存接近上限，清理最少使用的项
            if len(self._time_cache) > self.max_cache_size * 0.8:
                # 保留最近使用的80%
                keep_count = int(self.max_cache_size * 0.6)
                if keep_count > 0:
                    # OrderedDict保持插入顺序，保留最后的项
                    items_to_keep = list(self._time_cache.items())[-keep_count:]
                    self._time_cache.clear()
                    self._time_cache.update(items_to_keep)

                    self.logger.debug(f"内存优化: 缓存清理到{keep_count}项")

            # 清理格式缓存
            if len(self._format_cache) > 200:
                # 保留最近的100项
                items_to_keep = list(self._format_cache.items())[-100:]
                self._format_cache.clear()
                self._format_cache.update(items_to_keep)

                self.logger.debug(f"内存优化: 格式缓存清理到100项")

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息（扩展版）"""
        with self._lock:
            total_requests = self._stats['cache_hits'] + self._stats['cache_misses']
            cache_hit_rate = (self._stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0

            fast_path_total = self._stats['fast_path_hits'] + self._stats['cache_hits'] + self._stats['cache_misses']
            fast_path_rate = (self._stats['fast_path_hits'] / fast_path_total * 100) if fast_path_total > 0 else 0

            return {
                'cache_stats': {
                    'cache_size': len(self._time_cache),
                    'format_cache_size': len(self._format_cache),
                    'cache_hit_rate': cache_hit_rate,
                    'fast_path_rate': fast_path_rate,
                    'cache_memory_mb': self._estimate_cache_memory(),
                    'max_cache_size': self.max_cache_size,
                    'memory_limit_mb': self.memory_limit_mb
                },
                'request_stats': {
                    'total_requests': total_requests,
                    'cache_hits': self._stats['cache_hits'],
                    'cache_misses': self._stats['cache_misses'],
                    'fast_path_hits': self._stats['fast_path_hits'],
                    'format_detections': self._stats['format_detections']
                },
                'performance_metrics': {
                    'avg_cache_hit_rate': cache_hit_rate,
                    'avg_fast_path_rate': fast_path_rate,
                    'memory_efficiency': (self._estimate_cache_memory() / self.memory_limit_mb * 100) if self.memory_limit_mb > 0 else 0
                }
            }
    
    def preload_time_formats(self, time_samples: List[str]) -> Dict[str, Any]:
        """
        预加载时间格式检测 (增强版)

        Args:
            time_samples: 时间样本列表

        Returns:
            预加载结果统计
        """
        if not time_samples:
            return {
                'total_samples': 0,
                'detected_formats': {},
                'success_rate': 0.0,
                'cache_preloaded': 0
            }

        detected_formats = {}
        cache_preloaded = 0

        with self._lock:
            for time_str in time_samples:
                if not time_str or not isinstance(time_str, str):
                    continue

                # 检测格式
                format_type = self._detect_time_format(time_str)
                if format_type:
                    detected_formats[format_type] = detected_formats.get(format_type, 0) + 1

                    # 预加载到缓存（如果不是目标格式）
                    if format_type != self.target_format:
                        aligned_time = self._parse_and_format_time(time_str)
                        if aligned_time and len(self._time_cache) < self.max_cache_size:
                            self._time_cache[time_str] = aligned_time
                            cache_preloaded += 1

        total_detected = sum(detected_formats.values())
        success_rate = (total_detected / len(time_samples)) * 100 if time_samples else 0

        result = {
            'total_samples': len(time_samples),
            'detected_formats': detected_formats,
            'success_rate': success_rate,
            'cache_preloaded': cache_preloaded
        }

        self.logger.info(f"时间格式预加载完成: {total_detected}/{len(time_samples)}个样本 "
                        f"(成功率{success_rate:.1f}%), 预缓存{cache_preloaded}个时间值")

        return result

    def learn_from_data(self, time_strings: List[str], max_learn: int = 1000) -> Dict[str, Any]:
        """
        从数据中学习时间格式模式

        Args:
            time_strings: 时间字符串列表
            max_learn: 最大学习数量

        Returns:
            学习结果统计
        """
        if not time_strings:
            return {'learned_patterns': {}, 'cache_populated': 0}

        # 限制学习数量
        learn_data = time_strings[:max_learn] if len(time_strings) > max_learn else time_strings

        learned_patterns = {}
        cache_populated = 0

        with self._lock:
            for time_str in learn_data:
                if not time_str or time_str in self._time_cache:
                    continue

                # 尝试解析并学习格式
                format_type = self._detect_time_format(time_str)
                if format_type:
                    learned_patterns[format_type] = learned_patterns.get(format_type, 0) + 1

                    # 如果缓存未满，预处理并缓存
                    if len(self._time_cache) < self.max_cache_size:
                        aligned_time = self._parse_and_format_time(time_str)
                        if aligned_time:
                            self._time_cache[time_str] = aligned_time
                            cache_populated += 1

        result = {
            'learned_patterns': learned_patterns,
            'cache_populated': cache_populated,
            'total_processed': len(learn_data)
        }

        self.logger.info(f"格式学习完成: 处理{len(learn_data)}个样本, "
                        f"学习到{len(learned_patterns)}种格式, 预缓存{cache_populated}个时间值")

        return result
    
    def clear_cache(self) -> Tuple[int, int]:
        """
        清空所有缓存

        Returns:
            (时间缓存条目数, 格式缓存条目数)
        """
        with self._lock:
            time_count = len(self._time_cache)
            format_count = len(self._format_cache)

            self._time_cache.clear()
            self._format_cache.clear()

            # 更新统计信息
            self._stats['memory_cleanups'] += 1
            self._stats['cache_evictions'] += time_count

            # 强制垃圾回收
            collected = gc.collect()

            self.logger.info(f"时间对齐缓存已清空: 时间缓存{time_count}个, 格式缓存{format_count}个, 垃圾回收{collected}个对象")
            return time_count, format_count

    def cleanup_resources(self):
        """清理所有资源"""
        self.logger.info("开始清理TimeAlignmentCache资源")

        try:
            # 清空所有缓存
            time_count, format_count = self.clear_cache()

            # 重置统计信息
            self._stats = {
                'total_requests': 0,
                'cache_hits': 0,
                'cache_misses': 0,
                'fast_path_hits': 0,
                'format_conversions': 0,
                'memory_cleanups': 0,
                'cache_evictions': 0,
                'batch_requests': 0,
                'batch_fast_path_hits': 0,
                'format_learning_sessions': 0,
                'learned_formats': 0
            }

            self.logger.info(f"TimeAlignmentCache资源清理完成: 清理时间缓存{time_count}个, 格式缓存{format_count}个")

        except Exception as e:
            self.logger.error(f"TimeAlignmentCache资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        self.cleanup_resources()

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息（别名方法）"""
        return self.get_cache_stats()

    def get_cache_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计信息字典
        """
        with self._lock:
            total_requests = self._stats['total_requests']
            
            # 计算各种命中率
            fast_path_rate = (self._stats['fast_path_hits'] / total_requests * 100) if total_requests > 0 else 0.0
            cache_hit_rate = (self._stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0.0
            overall_efficiency = ((self._stats['fast_path_hits'] + self._stats['cache_hits']) / total_requests * 100) if total_requests > 0 else 0.0
            
            cache_memory = self._get_cache_memory_usage()
            
            return {
                'time_cache_size': len(self._time_cache),
                'format_cache_size': len(self._format_cache),
                'max_cache_size': self.max_cache_size,
                'total_requests': total_requests,
                'fast_path_hits': self._stats['fast_path_hits'],
                'cache_hits': self._stats['cache_hits'],
                'cache_misses': self._stats['cache_misses'],
                'format_detections': self._stats.get('format_detections', 0),
                'parsing_errors': self._stats.get('parsing_errors', 0),
                'memory_cleanups': self._stats['memory_cleanups'],
                'cache_evictions': self._stats['cache_evictions'],
                'batch_requests': self._stats['batch_requests'],
                'batch_fast_path_hits': self._stats['batch_fast_path_hits'],
                'format_learning_sessions': self._stats['format_learning_sessions'],
                'learned_formats': self._stats['learned_formats'],
                'fast_path_rate': fast_path_rate,
                'cache_hit_rate': cache_hit_rate,
                'overall_efficiency': overall_efficiency,
                'cache_memory_mb': cache_memory,
                'memory_limit_mb': self.memory_limit_mb,
                'target_format': self.target_format,
                'enable_fast_check': self.enable_fast_check
            }
    
    def optimize_memory(self) -> Dict[str, Any]:
        """
        优化内存使用
        
        Returns:
            优化结果统计
        """
        with self._lock:
            before_time_cache = len(self._time_cache)
            before_format_cache = len(self._format_cache)
            before_memory = self._get_cache_memory_usage()
            
            # 清理时间缓存到50%
            time_removed = self._cleanup_cache(target_size=int(self.max_cache_size * 0.5))
            
            # 清理格式缓存中的旧条目 (保留最近的100个)
            if len(self._format_cache) > 100:
                # 简单清理：清空重建 (格式缓存很小，影响不大)
                self._format_cache.clear()
            
            # 强制垃圾回收
            gc.collect()
            
            after_time_cache = len(self._time_cache)
            after_format_cache = len(self._format_cache)
            after_memory = self._get_cache_memory_usage()
            
            result = {
                'before_time_cache_size': before_time_cache,
                'after_time_cache_size': after_time_cache,
                'before_format_cache_size': before_format_cache,
                'after_format_cache_size': after_format_cache,
                'time_entries_removed': time_removed,
                'format_entries_removed': before_format_cache - after_format_cache,
                'before_memory_mb': before_memory,
                'after_memory_mb': after_memory,
                'memory_saved_mb': before_memory - after_memory
            }
            
            self.logger.info(f"时间缓存内存优化完成: 节省{result['memory_saved_mb']:.2f}MB")
            return result
    
    def __len__(self) -> int:
        """返回时间缓存大小"""
        return len(self._time_cache)
    
    def __contains__(self, time_str: str) -> bool:
        """检查时间字符串是否在缓存中"""
        with self._lock:
            return time_str in self._time_cache
    
    def __repr__(self) -> str:
        """字符串表示"""
        stats = self.get_cache_stats()
        return (f"TimeAlignmentCache(size={stats['time_cache_size']}/{stats['max_cache_size']}, "
                f"efficiency={stats['overall_efficiency']:.1f}%, "
                f"memory={stats['cache_memory_mb']:.2f}/{stats['memory_limit_mb']}MB)")


# 全局单例实例
_time_cache_instance: Optional[TimeAlignmentCache] = None
_time_cache_lock = threading.Lock()


def get_time_alignment_cache(target_format: str = 'standard',
                           max_cache_size: int = 10000,
                           memory_limit_mb: float = 200.0,
                           enable_fast_check: bool = True,
                           alignment_interval_seconds: int = 1) -> TimeAlignmentCache:
    """
    获取时间对齐缓存系统单例实例

    Args:
        target_format: 目标时间格式
        max_cache_size: 最大缓存大小
        memory_limit_mb: 内存限制MB
        enable_fast_check: 是否启用快速检查
        alignment_interval_seconds: 时间对齐间隔（秒）

    Returns:
        时间对齐缓存系统实例
    """
    global _time_cache_instance

    with _time_cache_lock:
        if _time_cache_instance is None:
            _time_cache_instance = TimeAlignmentCache(
                target_format=target_format,
                max_cache_size=max_cache_size,
                memory_limit_mb=memory_limit_mb,
                enable_fast_check=enable_fast_check,
                alignment_interval_seconds=alignment_interval_seconds
            )
        else:
            # 如果参数不同，重新创建实例
            if (_time_cache_instance.alignment_interval_seconds != alignment_interval_seconds or
                _time_cache_instance.target_format != target_format):
                _time_cache_instance = TimeAlignmentCache(
                    target_format=target_format,
                    max_cache_size=max_cache_size,
                    memory_limit_mb=memory_limit_mb,
                    enable_fast_check=enable_fast_check,
                    alignment_interval_seconds=alignment_interval_seconds
                )

        return _time_cache_instance


def reset_time_alignment_cache():
    """重置时间对齐缓存系统单例"""
    global _time_cache_instance
    
    with _time_cache_lock:
        if _time_cache_instance is not None:
            _time_cache_instance.clear_cache()
            _time_cache_instance = None
