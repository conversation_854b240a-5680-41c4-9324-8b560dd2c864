# -*- coding: utf-8 -*-
"""
配置管理模块
负责读取和管理data_mapping.json配置文件，理解泵站结构和CSV映射关系
集成智能表选择功能
"""

import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.utils.logger import get_logger
from src.utils.performance import monitor_performance
from src.utils.exception_handler import handle_exceptions, retry_on_exception
import logging


class ConfigManager:
    """配置管理器 - 管理data_mapping.json和参数定义"""
    
    def __init__(self, config_file: str = "config/data_mapping.json"):
        self.logger = get_logger(__name__)
        self.config_file = Path(config_file)
        
        # 配置数据
        self.station_configs: Dict[str, Dict] = {}
        self.file_to_station_map: Dict[str, str] = {}
        self.station_to_files_map: Dict[str, List[str]] = {}
        self.parameter_definitions: Dict[str, Dict] = {}
        
        # 统计信息
        self.total_stations = 0
        self.total_files = 0
        self.missing_files = 0

        # 🔧 集成表选择功能
        self.pump_keywords = [
            '泵', 'pump', '加压泵', '取水泵', '供水泵', '离心泵',
            '功率', '电流', '电压', '频率', '转速', '流量', '压力',
            '温度', '振动', 'power', 'current', 'voltage', 'frequency',
            'speed', 'flow', 'pressure', 'temperature', 'vibration'
        ]

        self.pipe_keywords = [
            '总管', '主管', '管道', 'pipe', 'main', 'pipeline',
            '流量计', '压力计', '温度计', 'flowmeter', 'pressure_meter',
            '进水', '出水', 'inlet', 'outlet', '管网', 'network'
        ]

        self.logger.info(f"配置管理器初始化: {config_file}")

        # 加载配置
        try:
            self._load_configuration()
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            # 设置默认值
            self.station_configs = {}
            self.file_mappings = {}
            self.total_stations = 0
            self.total_files = 0
            self.missing_files = 0
    
    @handle_exceptions(context={'operation': 'load_configuration'})
    @retry_on_exception(max_attempts=2, delay=1.0)
    @monitor_performance("load_configuration")
    def _load_configuration(self):
        """加载配置文件"""
        try:
            self.logger.info(f"开始加载配置文件: {self.config_file}")
            
            if not self.config_file.exists():
                raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
            
            # 读取JSON配置文件
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.logger.info(f"配置文件读取成功，包含 {len(config_data)} 个泵站")
            
            # 解析配置数据
            self._parse_configuration(config_data)
            
            # 验证配置
            self._validate_configuration()
            
            # 生成统计信息
            self._generate_statistics()
            
            self.logger.info("配置加载完成")
            
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            raise
    
    def _parse_configuration(self, config_data: Dict):
        """解析配置数据"""
        self.logger.debug("开始解析配置数据")
        
        for station_id, station_config in config_data.items():
            self.logger.debug(f"解析泵站配置: {station_id}")
            
            # 存储泵站配置
            self.station_configs[station_id] = station_config
            
            # 解析文件映射
            self._parse_station_files(station_id, station_config)
        
        self.total_stations = len(self.station_configs)
        self.logger.info(f"解析完成，共 {self.total_stations} 个泵站")
    
    def _parse_station_files(self, station_id: str, station_config: Dict):
        """解析单个泵站的文件映射"""
        station_files = []

        for device_name, device_config in station_config.items():
            self.logger.debug(f"解析设备配置: {station_id}.{device_name}")

            for param_name, file_list in device_config.items():
                # 跳过type字段，它不是文件路径
                if param_name == 'type':
                    continue

                if isinstance(file_list, list):
                    for file_path in file_list:
                        # 标准化文件路径
                        normalized_path = self._normalize_path(file_path)

                        # 建立文件到泵站的映射
                        self.file_to_station_map[normalized_path] = station_id
                        station_files.append(normalized_path)

                        self.logger.debug(f"文件映射: {normalized_path} -> {station_id}.{device_name}.{param_name}")

        # 存储泵站到文件的映射
        self.station_to_files_map[station_id] = station_files
        self.total_files += len(station_files)
    
    def _normalize_path(self, file_path: str) -> str:
        """标准化文件路径"""
        from pathlib import Path
        import os

        # 转换为Path对象处理
        path_obj = Path(file_path)

        # 如果是绝对路径，转换为相对于项目根目录的路径
        if path_obj.is_absolute():
            try:
                # 获取项目根目录 - 修复导入路径
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = Path(current_dir).parent.parent

                # 计算相对路径
                relative_path = path_obj.relative_to(project_root)
                normalized = str(relative_path).replace('\\', '/')
                self.logger.debug(f"绝对路径转换: {file_path} -> {normalized}")
                return normalized
            except (ValueError, ImportError) as e:
                # 如果无法计算相对路径，使用文件名匹配
                self.logger.debug(f"无法计算相对路径，使用文件名: {path_obj.name}, 错误: {e}")
                return path_obj.name

        # 处理相对路径
        file_path = str(path_obj)

        # 移除可能的前缀
        if file_path.startswith('src/'):
            file_path = file_path[4:]

        # 统一使用正斜杠
        normalized = file_path.replace('\\', '/')

        return normalized
    
    @monitor_performance("validate_configuration")
    def _validate_configuration(self):
        """验证配置文件"""
        self.logger.info("开始验证配置")

        # 定义抽象文件类型（不需要实际存在的文件）
        abstract_types = {'pump', 'variable_frequency', 'variable_frequency ', 'soft_start', 'soft_start '}

        missing_files = []

        # 检查文件存在性
        for file_path in self.file_to_station_map.keys():
            # 跳过抽象文件类型
            if file_path in abstract_types:
                self.logger.debug(f"跳过抽象文件类型验证: {file_path}")
                continue

            full_path = Path(file_path)
            if not full_path.exists():
                missing_files.append(str(full_path))

        self.missing_files = len(missing_files)

        if missing_files:
            self.logger.warning(f"发现 {len(missing_files)} 个缺失文件")
            # 只记录前5个缺失文件，避免日志过长
            for missing_file in missing_files[:5]:
                self.logger.warning(f"缺失文件: {missing_file}")
            if len(missing_files) > 5:
                self.logger.warning(f"... 还有 {len(missing_files) - 5} 个文件缺失")
        else:
            self.logger.info("所有配置文件验证通过")
    
    def _generate_statistics(self):
        """生成配置统计信息"""
        self.logger.info("=== 配置统计信息 ===")
        self.logger.info(f"泵站总数: {self.total_stations}")
        self.logger.info(f"文件总数: {self.total_files}")
        self.logger.info(f"缺失文件: {self.missing_files}")
        if self.total_files > 0:
            self.logger.info(f"文件完整率: {((self.total_files - self.missing_files) / self.total_files * 100):.1f}%")
        else:
            self.logger.info("文件完整率: 无文件配置")
        
        # 按泵站统计
        for station_id, station_config in self.station_configs.items():
            device_count = len(station_config)
            file_count = len(self.station_to_files_map.get(station_id, []))
            self.logger.info(f"  {station_id}: {device_count}个设备, {file_count}个文件")
        
        self.logger.info("=== 统计信息结束 ===")
    
    def get_station_list(self) -> List[str]:
        """获取所有泵站ID列表"""
        return list(self.station_configs.keys())
    
    def get_station_config(self, station_id: str) -> Optional[Dict]:
        """获取指定泵站的配置"""
        return self.station_configs.get(station_id)
    
    def get_device_list(self, station_id: str) -> List[str]:
        """获取指定泵站的设备列表"""
        station_config = self.get_station_config(station_id)
        if station_config:
            return list(station_config.keys())
        return []
    
    def get_parameter_list(self, station_id: str, device_name: str) -> List[str]:
        """获取指定设备的参数列表"""
        station_config = self.get_station_config(station_id)
        if station_config and device_name in station_config:
            return list(station_config[device_name].keys())
        return []

    def get_pump_type(self, station_id: str, device_name: str) -> Optional[str]:
        """从配置文件中获取水泵类型

        Args:
            station_id: 泵站ID
            device_name: 设备名称

        Returns:
            Optional[str]: 水泵类型 ('variable_frequency', 'soft_start') 或 None
        """
        try:
            station_config = self.get_station_config(station_id)
            if not station_config or device_name not in station_config:
                self.logger.debug(f"未找到设备配置: {station_id}.{device_name}")
                return None

            device_config = station_config[device_name]
            pump_type_list = device_config.get('pump_type')

            if pump_type_list and isinstance(pump_type_list, list) and len(pump_type_list) > 0:
                pump_type = pump_type_list[0].strip()  # 去除可能的空格
                self.logger.debug(f"从配置获取水泵类型: {station_id}.{device_name} -> {pump_type}")
                return pump_type

            # 如果没有pump_type配置，使用备用逻辑
            if 'frequency' in device_config:
                self.logger.debug(f"备用逻辑判断为变频泵: {station_id}.{device_name}")
                return 'variable_frequency'
            else:
                self.logger.debug(f"备用逻辑判断为软起泵: {station_id}.{device_name}")
                return 'soft_start'

        except Exception as e:
            self.logger.error(f"获取水泵类型失败: {station_id}.{device_name}, 错误: {e}")
            return None

    def get_pump_type_by_file(self, file_path: str) -> Optional[str]:
        """根据文件路径获取水泵类型

        Args:
            file_path: 文件路径

        Returns:
            Optional[str]: 水泵类型 ('variable_frequency', 'soft_start') 或 None
        """
        try:
            mapping = self.get_file_mapping(file_path)
            if not mapping:
                self.logger.debug(f"未找到文件映射: {file_path}")
                return None

            return self.get_pump_type(mapping['station_id'], mapping['device_name'])

        except Exception as e:
            self.logger.error(f"根据文件路径获取水泵类型失败: {file_path}, 错误: {e}")
            return None
    
    def get_file_list(self, station_id: str, device_name: str = None, param_name: str = None) -> List[str]:
        """获取文件列表"""
        if station_id not in self.station_configs:
            return []
        
        files = []
        station_config = self.station_configs[station_id]
        
        for dev_name, dev_config in station_config.items():
            if device_name and dev_name != device_name:
                continue
            
            for par_name, file_list in dev_config.items():
                if param_name and par_name != param_name:
                    continue
                
                if isinstance(file_list, list):
                    files.extend([self._normalize_path(f) for f in file_list])
        
        return files
    
    def get_station_by_file(self, file_path: str) -> Optional[str]:
        """根据文件路径获取所属泵站"""
        normalized_path = self._normalize_path(file_path)
        return self.file_to_station_map.get(normalized_path)

    def get_file_mapping(self, file_path: str) -> Optional[Dict[str, str]]:
        """根据文件路径获取完整的映射信息

        Args:
            file_path: 文件路径

        Returns:
            包含station_id, device_name, param_name的字典，如果未找到返回None
        """
        normalized_path = self._normalize_path(file_path)

        self.logger.debug(f"[SEARCH] 查找文件映射: {normalized_path}")

        # 第一次尝试：精确路径匹配
        for station_id, station_config in self.station_configs.items():
            for device_name, device_config in station_config.items():
                for param_name, file_list in device_config.items():
                    if isinstance(file_list, list):
                        for config_file_path in file_list:
                            config_normalized = self._normalize_path(config_file_path)
                            if config_normalized == normalized_path:
                                mapping_info = {
                                    'station_id': station_id,
                                    'device_name': device_name,
                                    'param_name': param_name,
                                    'file_path': normalized_path
                                }
                                self.logger.debug(f"精确路径匹配成功: {mapping_info}")
                                return mapping_info

        # 第二次尝试：文件名匹配（备用方案）
        from pathlib import Path
        input_filename = Path(file_path).name

        self.logger.debug(f"[SEARCH] 尝试文件名匹配: {input_filename}")

        for station_id, station_config in self.station_configs.items():
            for device_name, device_config in station_config.items():
                for param_name, file_list in device_config.items():
                    if isinstance(file_list, list):
                        for config_file_path in file_list:
                            config_filename = Path(config_file_path).name
                            self.logger.debug(f"[SEARCH] 比较文件名: {input_filename} vs {config_filename}")
                            if config_filename == input_filename:
                                mapping_info = {
                                    'station_id': station_id,
                                    'device_name': device_name,
                                    'param_name': param_name,
                                    'file_path': normalized_path
                                }
                                self.logger.debug(f"文件名匹配成功: {mapping_info}")
                                return mapping_info

        self.logger.warning(f"[WARN] 未找到文件映射: {file_path}")
        self.logger.debug(f"[DEBUG] 输入文件名: {input_filename}")

        # 🔧 调试：显示所有可用的配置文件名
        self.logger.debug("[DEBUG] 配置中的所有文件名:")
        for station_id, station_config in self.station_configs.items():
            for device_name, device_config in station_config.items():
                for param_name, file_list in device_config.items():
                    if isinstance(file_list, list):
                        for config_file_path in file_list:
                            config_filename = Path(config_file_path).name
                            self.logger.debug(f"  {station_id} -> {device_name} -> {param_name}: {config_filename}")

        return None

    def get_batch_file_mappings(self, file_paths: List[str]) -> Dict[str, Optional[Dict[str, str]]]:
        """批量获取文件路径的映射信息

        Args:
            file_paths: 文件路径列表

        Returns:
            文件路径到映射信息的字典
        """
        self.logger.info(f"[SEARCH] 批量查找 {len(file_paths)} 个文件的映射信息")

        mappings = {}
        found_count = 0

        for file_path in file_paths:
            mapping = self.get_file_mapping(file_path)
            mappings[file_path] = mapping
            if mapping:
                found_count += 1

        self.logger.info(f"[OK] 批量映射查找完成: {found_count}/{len(file_paths)} 个文件找到映射")

        return mappings

    def get_all_file_mappings(self) -> Dict[str, Dict[str, str]]:
        """获取所有文件的映射信息

        Returns:
            所有文件路径到映射信息的字典
        """
        self.logger.info("[SEARCH] 获取所有文件映射信息")

        all_mappings = {}

        for station_id, station_config in self.station_configs.items():
            for device_name, device_config in station_config.items():
                for param_name, file_list in device_config.items():
                    # 跳过type字段，它不是文件路径
                    if param_name == 'type':
                        continue

                    if isinstance(file_list, list):
                        for config_file_path in file_list:
                            normalized_path = self._normalize_path(config_file_path)
                            all_mappings[normalized_path] = {
                                'station_id': station_id,
                                'device_name': device_name,
                                'param_name': param_name,
                                'file_path': normalized_path
                            }

        self.logger.info(f"[OK] 获取到 {len(all_mappings)} 个文件映射")
        return all_mappings

    def normalize_param_name(self, param_name: str) -> str:
        """标准化参数名

        Args:
            param_name: 原始参数名

        Returns:
            标准化后的参数名
        """
        if not param_name:
            return "unknown"

        # 参数名映射表
        param_mapping = {
            # 电气参数
            'frequency': 'frequency',
            'power': 'power',
            'kwh': 'kwh',
            'power_factor': 'power_factor',

            # 分相电压
            'voltage_a': 'voltage_a',
            'voltage_b': 'voltage_b',
            'voltage_c': 'voltage_c',

            # 分相电流
            'current_a': 'current_a',
            'current_b': 'current_b',
            'current_c': 'current_c',

            # 水力参数
            'pressure': 'pressure',
            'flow_rate': 'flow_rate',
            'cumulative_flow': 'cumulative_flow',
            'inlet_pressure': 'inlet_pressure',
            'outlet_pressure': 'outlet_pressure',
            'outlet_flow': 'outlet_flow',
            'head': 'head',
            'outlet_temperature': 'outlet_temperature',

            # 水质参数
            'temperature': 'temperature',
            'ph_value': 'ph_value',
            'turbidity': 'turbidity'
        }

        # 标准化参数名
        normalized = param_name.lower().strip()

        return param_mapping.get(normalized, normalized)


    
    def get_missing_files(self) -> List[str]:
        """获取缺失文件列表"""
        missing_files = []
        
        for file_path in self.file_to_station_map.keys():
            full_path = Path(file_path)
            if not full_path.exists():
                missing_files.append(str(full_path))
        
        return missing_files
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """获取配置摘要信息"""
        return {
            'total_stations': self.total_stations,
            'total_files': self.total_files,
            'missing_files': self.missing_files,
            'completeness_rate': (self.total_files - self.missing_files) / self.total_files if self.total_files > 0 else 0,
            'stations': {
                station_id: {
                    'devices': len(config),
                    'files': len(self.station_to_files_map.get(station_id, [])),
                    'device_list': list(config.keys())
                }
                for station_id, config in self.station_configs.items()
            }
        }
    
    def reload_configuration(self):
        """重新加载配置"""
        self.logger.info("重新加载配置")

        # 清空现有配置
        self.station_configs.clear()
        self.file_to_station_map.clear()
        self.station_to_files_map.clear()

        # 重新加载
        self._load_configuration()

    def get_file_mapping_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件映射信息 - 为流式处理器提供支持"""
        try:
            # 首先尝试使用完整的映射方法
            mapping = self.get_file_mapping(file_path)
            if mapping:
                # 如果找到了完整映射，直接返回
                self.logger.debug(f"使用完整映射: {file_path} -> {mapping['param_name']}")
                return {
                    'station_id': mapping['station_id'],
                    'device_name': mapping['device_name'],
                    'param_name': mapping['param_name'],
                    'file_name': Path(file_path).name
                }

            # 如果没有找到完整映射，使用备用逻辑
            file_name = Path(file_path).name
            self.logger.debug(f"使用备用逻辑解析文件: {file_name}")

            # 查找文件对应的泵站
            station_id = None
            for station, files in self.station_to_files_map.items():
                if any(file_name in f for f in files):
                    station_id = station
                    break

            if not station_id:
                # 如果找不到映射，从文件名推断
                if "二期" in file_name:
                    station_id = "二期供水泵房" if "供水" in file_name else "二期取水泵房"
                elif "一期" in file_name:
                    station_id = "一期供水泵房" if "供水" in file_name else "一期取水泵房"
                else:
                    station_id = "未知泵站"

            # 从文件名推断设备名和参数名
            device_name = "未知设备"
            param_name = "未知参数"

            # 改进的文件名解析逻辑
            if "pump" in file_name.lower():
                device_name = "pump"
                param_name = "pump_data"
            elif "main" in file_name.lower() or "pipeline" in file_name.lower() or "总管" in file_name:
                device_name = "main_pipeline"
                param_name = "pipeline_data"
            else:
                # 尝试从文件名解析更多信息
                if "频率" in file_name:
                    param_name = "frequency"
                elif "A相电压" in file_name:
                    param_name = "voltage_a"
                elif "B相电压" in file_name:
                    param_name = "voltage_b"
                elif "C相电压" in file_name:
                    param_name = "voltage_c"
                elif "A相电流" in file_name:
                    param_name = "current_a"
                elif "B相电流" in file_name:
                    param_name = "current_b"
                elif "C相电流" in file_name:
                    param_name = "current_c"
                elif "有功功率" in file_name:
                    param_name = "power"
                elif "功率因数" in file_name:
                    param_name = "power_factor"
                elif "电度" in file_name:
                    param_name = "kwh"
                elif "压力" in file_name:
                    param_name = "pressure"
                elif "瞬时流量" in file_name:
                    param_name = "flow_rate"
                elif "累计流量" in file_name:
                    param_name = "cumulative_flow"

                # 设备名推断
                if "泵" in file_name:
                    if "1#" in file_name:
                        device_name = f"{station_id}1#泵"
                    elif "2#" in file_name:
                        device_name = f"{station_id}2#泵"
                    elif "3#" in file_name:
                        device_name = f"{station_id}3#泵"
                    elif "4#" in file_name:
                        device_name = f"{station_id}4#泵"
                    elif "5#" in file_name:
                        device_name = f"{station_id}5#泵"
                    elif "6#" in file_name:
                        device_name = f"{station_id}6#泵"
                elif "总管" in file_name:
                    device_name = f"{station_id}总管"

            self.logger.debug(f"备用逻辑解析结果: {param_name}")
            return {
                'station_id': station_id,
                'device_name': device_name,
                'param_name': param_name,
                'file_name': file_name
            }

        except Exception as e:
            self.logger.error(f"获取文件映射信息失败: {file_path}, 错误: {e}")
            return {
                'station_id': "未知泵站",
                'device_name': f"未知设备_{Path(file_path).stem}",
                'param_name': "未知参数",
                'file_name': Path(file_path).name
            }

    # 🔧 集成表选择功能
    def select_target_table(self, file_path: str, device_name: Optional[str] = None) -> str:
        """
        根据CSV映射配置文件中的type字段选择目标表

        Args:
            file_path: 文件路径
            device_name: 设备名称（可选）

        Returns:
            str: 目标表名
        """
        try:
            self.logger.debug(f"[TABLE] === 开始表选择过程 ===")
            self.logger.debug(f"[TABLE] 目标文件: {file_path}")
            self.logger.debug(f"[TABLE] 设备名称: {device_name}")

            # 1. 优先使用配置管理器获取设备类型
            self.logger.debug("[TABLE] 步骤1: 使用配置管理器获取设备类型")
            mapping_info = self.get_file_mapping(file_path)

            if mapping_info:
                device_name = mapping_info['device_name']
                station_id = mapping_info['station_id']
                param_name = mapping_info['param_name']

                self.logger.debug(f"[TABLE] 映射信息: 设备={device_name}, 泵站={station_id}, 参数={param_name}")

                # 根据设备名称查找设备类型
                device_type = self._get_device_type_from_config(station_id, device_name)

                if device_type:
                    target_table = self._map_device_type_to_table(device_type)
                    self.logger.info(f"[TABLE] 配置选择成功: {file_path}")
                    self.logger.info(f"[TABLE]   设备类型: {device_type}")
                    self.logger.info(f"[TABLE]   目标表: {target_table}")
                    return target_table
                else:
                    self.logger.warning(f"[TABLE] 配置中未找到设备类型: {device_name}")
            else:
                self.logger.warning(f"[TABLE] 配置中未找到文件映射: {file_path}")

            # 2. 备用方法：使用关键词匹配
            self.logger.debug("[TABLE] 步骤2: 使用备用关键词匹配方法")
            filename = Path(file_path).name.lower()
            self.logger.debug(f"[TABLE] 文件名: {filename}")

            # 检查是否为泵数据
            if self._is_pump_data_by_keywords(filename):
                target_table = 'pump_data'
                self.logger.info(f"[TABLE] 备用方法识别为泵数据: {target_table}")
                return target_table

            # 检查是否为管道数据
            if self._is_pipe_data_by_keywords(filename):
                target_table = 'main_pipe_data'
                self.logger.info(f"[TABLE] 备用方法识别为管道数据: {target_table}")
                return target_table

            # 3. 默认表
            self.logger.debug("[TABLE] 步骤3: 使用默认表")
            target_table = 'raw_data_by_device'
            self.logger.info(f"[TABLE] 使用默认表: {target_table}")
            return target_table

        except Exception as e:
            self.logger.error(f"[TABLE] 表选择失败: {e}")
            # 出错时返回默认表
            return 'raw_data_by_device'

    def _get_device_type_from_config(self, station_id: str, device_name: str) -> Optional[str]:
        """从配置中获取设备类型"""
        try:
            if station_id in self.station_configs:
                station_config = self.station_configs[station_id]

                # 检查泵设备
                pumps = station_config.get('pumps', {})
                if device_name in pumps:
                    return 'pump'

                # 检查管道设备
                pipelines = station_config.get('pipelines', {})
                if device_name in pipelines:
                    return 'pipeline'

                # 检查其他设备
                other_devices = station_config.get('other_devices', {})
                if device_name in other_devices:
                    device_info = other_devices[device_name]
                    return device_info.get('type', 'unknown')

            return None

        except Exception as e:
            self.logger.error(f"[TABLE] 获取设备类型失败: {e}")
            return None

    def _map_device_type_to_table(self, device_type: str) -> str:
        """将设备类型映射到目标表"""
        mapping = {
            'pump': 'pump_data',
            'pipeline': 'main_pipe_data',
            'main_pipe': 'main_pipe_data',
            'pipe': 'main_pipe_data',
            'flowmeter': 'main_pipe_data',
            'pressure_meter': 'main_pipe_data',
            'temperature_meter': 'main_pipe_data'
        }

        return mapping.get(device_type.lower(), 'raw_data_by_device')

    def _is_pump_data_by_keywords(self, filename: str) -> bool:
        """通过关键词判断是否为泵数据"""
        filename_lower = filename.lower()

        for keyword in self.pump_keywords:
            if keyword.lower() in filename_lower:
                self.logger.debug(f"[TABLE] 泵关键词匹配: {keyword}")
                return True

        return False

    def _is_pipe_data_by_keywords(self, filename: str) -> bool:
        """通过关键词判断是否为管道数据"""
        filename_lower = filename.lower()

        for keyword in self.pipe_keywords:
            if keyword.lower() in filename_lower:
                self.logger.debug(f"[TABLE] 管道关键词匹配: {keyword}")
                return True

        return False

    def get_device_type_from_file(self, file_path: str) -> Optional[str]:
        """从文件路径获取设备类型"""
        try:
            mapping_info = self.get_file_mapping(file_path)
            if mapping_info:
                station_id = mapping_info['station_id']
                device_name = mapping_info['device_name']
                return self._get_device_type_from_config(station_id, device_name)

            return None

        except Exception as e:
            self.logger.error(f"[TABLE] 从文件获取设备类型失败: {e}")
            return None


if __name__ == "__main__":
    # 测试配置管理器
    test_logger = logging.getLogger("config_test")
    
    test_logger.info("配置管理器测试开始")
    
    try:
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 测试基本功能
        stations = config_manager.get_station_list()
        test_logger.info(f"发现泵站: {stations}")
        
        # 测试每个泵站的配置
        for station_id in stations:
            devices = config_manager.get_device_list(station_id)
            test_logger.info(f"{station_id} 的设备: {devices}")
            
            for device_name in devices:
                params = config_manager.get_parameter_list(station_id, device_name)
                test_logger.info(f"  {device_name} 的参数: {params}")
        
        # 获取配置摘要
        summary = config_manager.get_configuration_summary()
        test_logger.info(f"配置摘要: {summary}")
        
        # 测试缺失文件
        missing_files = config_manager.get_missing_files()
        if missing_files:
            test_logger.warning(f"缺失文件数量: {len(missing_files)}")
        else:
            test_logger.info("所有文件都存在")
        
        test_logger.info("配置管理器测试完成")

    except Exception as e:
        test_logger.error(f"配置管理器测试失败: {e}")
        raise


class ConfigManagerWithCleanup(ConfigManager):
    """带资源清理功能的配置管理器"""

    def cleanup_resources(self):
        """清理所有配置管理器资源"""
        self.logger.info("开始清理ConfigManager资源")

        try:
            # 1. 清理配置数据
            config_count = len(self.station_configs)
            if config_count > 0:
                self.logger.info(f"清理 {config_count} 个泵站配置")
                self.station_configs.clear()

            # 2. 清理文件映射
            file_mapping_count = len(getattr(self, 'file_to_station_map', {}))
            if file_mapping_count > 0:
                self.logger.info(f"清理 {file_mapping_count} 个文件映射")
                if hasattr(self, 'file_to_station_map'):
                    self.file_to_station_map.clear()
                if hasattr(self, 'station_to_files_map'):
                    self.station_to_files_map.clear()

            # 3. 清理缺失文件记录
            if isinstance(self.missing_files, int):
                missing_count = self.missing_files
                if missing_count > 0:
                    self.logger.info(f"清理 {missing_count} 个缺失文件记录")
                self.missing_files = 0
            else:
                missing_count = len(self.missing_files) if hasattr(self.missing_files, '__len__') else 0
                if missing_count > 0:
                    self.logger.info(f"清理 {missing_count} 个缺失文件记录")
                    if hasattr(self.missing_files, 'clear'):
                        self.missing_files.clear()
                    else:
                        self.missing_files = 0

            # 4. 重置状态
            self.config_file = None
            self.is_loaded = False

            self.logger.info("ConfigManager资源清理完成")

        except Exception as e:
            self.logger.error(f"ConfigManager资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        self.cleanup_resources()

        if exc_type is not None:
            self.logger.error(f"ConfigManager退出时发生异常: {exc_type.__name__}: {exc_val}")
            if exc_tb is not None:
                self.logger.debug("异常追踪信息已记录")

        return False  # 不抑制异常
