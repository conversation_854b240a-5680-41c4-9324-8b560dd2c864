#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
泵组优化程序主程序
整合所有模块，提供统一的程序入口
"""

import sys
import argparse
import signal
import time
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.message_bus import get_message_bus, reset_message_bus, MessageType, Message
from src.handlers.file_processor import FileProcessorHandler
from src.handlers.database_handler import DatabaseHandler
from src.handlers.database_insert_handler import DatabaseInsertHandler
from src.core.database_manager import DatabaseManager
from src.core.config_manager import ConfigManager
from src.core.singleton_data_transformer import get_data_transformer
from src.utils.logger import get_logger
from src.utils.performance import monitor_performance
from src.utils.device_group_processor import DeviceGroupProcessor

class PumpOptimizationMain:
    """泵组优化程序主类"""
    
    def __init__(self, config_dir: str = "config"):
        """初始化主程序"""
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        
        # 核心组件
        self.config_manager: Optional[ConfigManager] = None
        self.db_manager: Optional[DatabaseManager] = None
        self.data_transformer = None  # 🔧 使用集成的数据转换器替代设备管理器
        self.message_bus = None
        self.file_processor: Optional[FileProcessorHandler] = None
        self.database_handler: Optional[DatabaseHandler] = None
        self.database_insert_handler: Optional[DatabaseInsertHandler] = None
        
        # 运行状态
        self.is_running = False
        self.shutdown_requested = False

        # 设备分组处理器
        self.device_group_processor: Optional[DeviceGroupProcessor] = None
        
        # 统计信息
        self.start_time = None
        self.processed_files = 0
        self.processed_records = 0
        
        self.logger.info("泵组优化程序主类初始化完成")
    
    @monitor_performance("initialize_system")
    def initialize(self):
        """初始化所有系统组件"""
        try:
            self.logger.info("=" * 80)
            self.logger.info("开始初始化泵组优化程序")
            self.logger.info("=" * 80)
            
            # 1. 清空logs目录
            self._clear_logs_directory()
            
            # 2. 初始化配置管理器
            self._initialize_config_manager()
            
            # 3. 初始化数据库管理器
            self._initialize_database_manager()
            
            # 4. 初始化数据转换器（集成设备管理功能）
            self._initialize_data_transformer()

            # 5. 初始化消息总线
            self._initialize_message_bus()

            # 6. 初始化处理器
            self._initialize_handlers()

            # 7. 设置信号处理
            self._setup_signal_handlers()
            
            self.logger.info("=" * 80)
            self.logger.info("泵组优化程序初始化完成")
            self.logger.info("=" * 80)
            
            return True
            
        except Exception as e:
            self.logger.error(f"系统初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _clear_logs_directory(self):
        """清空logs目录"""
        try:
            logs_dir = project_root / "logs"
            if logs_dir.exists():
                for file in logs_dir.glob("*"):
                    if file.is_file():
                        try:
                            file.unlink()
                        except PermissionError:
                            pass  # 忽略正在使用的日志文件
            
            logs_dir.mkdir(exist_ok=True)
            self.logger.info("logs目录已清空")
            
        except Exception as e:
            self.logger.warning(f"[WARN] 清空logs目录时遇到问题: {e}")
    
    def _initialize_config_manager(self):
        """初始化配置管理器"""
        try:
            self.logger.info("初始化配置管理器...")

            config_file = self.config_dir / "data_mapping.json"
            if not config_file.exists():
                self.logger.warning(f"配置文件不存在: {config_file}")
                # 创建默认配置文件
                self._create_default_config()

            self.config_manager = ConfigManager(str(config_file))
            self.logger.info("配置管理器初始化完成")
            
        except Exception as e:
            self.logger.error(f"配置管理器初始化失败: {e}")
            raise

    def _initialize_database_manager(self):
        """初始化数据库管理器"""
        try:
            self.logger.info("初始化数据库管理器...")

            db_config_file = self.config_dir / "database_config.yaml"
            if not db_config_file.exists():
                raise FileNotFoundError(f"数据库配置文件不存在: {db_config_file}")

            self.db_manager = DatabaseManager(str(db_config_file))
            self.logger.info("数据库管理器初始化完成")

        except Exception as e:
            self.logger.error(f"数据库管理器初始化失败: {e}")
            raise

    def _initialize_data_transformer(self):
        """初始化数据转换器（集成设备管理功能）"""
        try:
            self.logger.info("初始化数据转换器...")

            # 创建数据转换器（集成设备管理功能）
            self.data_transformer = get_data_transformer(self.db_manager)

            # 设置配置管理器
            if self.config_manager:
                self.data_transformer.set_config_manager(self.config_manager)

            # 初始化泵站和设备信息
            self.logger.info("开始初始化泵站和设备信息...")
            success = self.data_transformer.initialize_stations_and_devices()

            if success:
                self.logger.info("泵站和设备信息初始化成功")
            else:
                self.logger.warning("泵站和设备信息初始化失败，但程序将继续运行")

            self.logger.info("数据转换器初始化完成")

        except Exception as e:
            self.logger.error(f"数据转换器初始化失败: {e}")
            # 不抛出异常，允许程序继续运行
            self.logger.warning("数据转换器初始化失败，程序将在没有设备关联的情况下运行")

    def _initialize_message_bus(self):
        """初始化消息总线"""
        try:
            self.logger.info("初始化消息总线...")

            # 重置消息总线
            reset_message_bus()

            # 获取消息总线实例
            self.message_bus = get_message_bus()

            # 启动消息总线
            self.message_bus.start()

            self.logger.info("消息总线初始化完成")
            
        except Exception as e:
            self.logger.error(f"消息总线初始化失败: {e}")
            raise
    
    def _initialize_handlers(self):
        """初始化处理器"""
        try:
            self.logger.info("初始化处理器...")

            # 创建文件处理器
            self.file_processor = FileProcessorHandler("main_file_processor")

            # 创建数据库处理器
            self.database_handler = DatabaseHandler("main_database_handler")

            # 创建数据库插入处理器
            self.database_insert_handler = DatabaseInsertHandler("main_database_insert_handler")

            # 订阅消息
            self.message_bus.subscribe(MessageType.FILE_PROCESS_REQUEST, self.file_processor)
            self.message_bus.subscribe(MessageType.FILE_PROCESS_COMPLETED, self.database_handler)
            self.message_bus.subscribe(MessageType.DATABASE_INSERT_REQUEST, self.database_insert_handler)

            # 创建设备分组处理器
            self.device_group_processor = DeviceGroupProcessor(
                config_manager=self.config_manager,
                logger=self.logger
            )

            self.logger.info("处理器初始化完成")

        except Exception as e:
            self.logger.error(f"处理器初始化失败: {e}")
            raise
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            self.logger.info("信号处理器设置完成")
            
        except Exception as e:
            self.logger.warning(f"[WARN] 信号处理器设置失败: {e}")
    
    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        # 使用frame参数避免警告
        if frame is not None:
            self.logger.debug("信号处理器接收到帧信息")
        self.logger.info(f"接收到信号 {signum}，开始优雅关闭...")
        self.shutdown_requested = True
    
    def _create_default_config(self):
        """创建默认配置文件"""
        try:
            self.logger.info("创建默认配置文件...")

            default_config = {
                "stations": {
                    "default_station": {
                        "name": "默认泵站",
                        "files": [],
                        "description": "默认配置，请根据实际情况修改"
                    }
                }
            }

            config_file = self.config_dir / "data_mapping.json"
            config_file.parent.mkdir(parents=True, exist_ok=True)

            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)

            self.logger.info(f"默认配置文件已创建: {config_file}")
            
        except Exception as e:
            self.logger.error(f"创建默认配置文件失败: {e}")
            raise
    
    @monitor_performance("run_data_processing")
    def run_data_processing(self, station_id: Optional[str] = None):
        """运行数据处理 - 只处理配置文件中映射的CSV文件"""
        try:
            self.logger.info("开始数据处理模式")
            self.start_time = datetime.now()
            self.is_running = True

            # 从配置管理器获取所有映射的文件
            if not self.config_manager:
                self.logger.error("配置管理器未初始化")
                return

            # 获取所有配置中映射的文件
            all_mappings = self.config_manager.get_all_file_mappings()
            if not all_mappings:
                self.logger.warning("配置文件中未找到任何文件映射")
                return

            # 过滤存在的文件
            existing_files = []
            abstract_types = {'pump', 'variable_frequency', 'variable_frequency ', 'soft_start', 'soft_start '}

            for file_path, mapping_info in all_mappings.items():
                # 跳过抽象文件类型
                if file_path in abstract_types:
                    self.logger.debug(f"跳过抽象文件类型: {file_path}")
                    continue

                full_path = project_root / file_path
                if full_path.exists():
                    existing_files.append((str(full_path), mapping_info))
                else:
                    self.logger.warning(f"配置中的文件不存在: {file_path}")

            if not existing_files:
                self.logger.warning("配置中的文件都不存在")
                return

            self.logger.info(f"配置中找到 {len(all_mappings)} 个文件映射，其中 {len(existing_files)} 个文件存在")

            # 处理文件
            for i, (file_path, mapping_info) in enumerate(existing_files, 1):
                if self.shutdown_requested:
                    self.logger.info("接收到关闭请求，停止处理")
                    break

                file_name = Path(file_path).name
                station_id_from_mapping = mapping_info.get('station_id', 'unknown')
                self.logger.info(f"处理文件 {i}/{len(existing_files)}: {file_name}")

                # 预先选择目标表（使用集成的表选择功能）
                target_table = self.config_manager.select_target_table(file_path, mapping_info)
                self.logger.info(f"为文件 {file_name} 选择目标表: {target_table}")

                # 发送文件处理请求
                message = Message(
                    type=MessageType.FILE_PROCESS_REQUEST,
                    payload={
                        'file_path': file_path,
                        'station_id': station_id or station_id_from_mapping,
                        'batch_id': f'main_batch_{datetime.now().strftime("%Y%m%d_%H%M%S")}_{i:03d}',
                        'mapping_info': mapping_info,  # 传递映射信息
                        'target_table': target_table  # 添加目标表信息
                    },
                    sender='main_program'
                )

                self.message_bus.publish(message)
                self.processed_files += 1

                # 短暂等待，避免消息队列过载
                time.sleep(0.1)

            # 等待处理完成
            self._wait_for_completion()

            # 生成处理报告
            self._generate_processing_report()

            self.logger.info("数据处理完成")
            
        except Exception as e:
            self.logger.error(f"数据处理失败: {e}")
            raise
        finally:
            self.is_running = False

    @monitor_performance("run_device_group_processing")
    def run_device_group_processing(self):
        """运行设备分组处理 - 新的重构版本"""
        try:
            self.logger.info("开始设备分组处理模式")
            self.start_time = datetime.now()
            self.is_running = True

            if not self.device_group_processor:
                self.logger.error("设备分组处理器未初始化")
                return

            # 清空cmdout和logs目录
            self._clear_output_directories()

            # 清空数据库表
            self._clear_database_tables()

            # 初始化泵站和设备
            if not self.data_transformer.initialize_stations_and_devices():
                self.logger.error("泵站和设备初始化失败")
                return

            # 执行设备分组处理
            self.logger.info("开始执行设备分组处理...")
            result = self.device_group_processor.process_all_devices()

            if not result.get('success', False):
                self.logger.error(f"设备分组处理失败: {result.get('error', 'Unknown error')}")
                return

            # 处理结果并插入数据库
            self._process_device_group_results(result)

            # 生成处理报告
            self._generate_device_group_report(result)

            self.logger.info("设备分组处理完成")

        except Exception as e:
            self.logger.error(f"设备分组处理失败: {e}")
            raise
        finally:
            self.is_running = False

    def _clear_output_directories(self):
        """清空输出目录"""
        try:
            import shutil

            # 清空cmdout目录
            cmdout_dir = Path('cmdout')
            if cmdout_dir.exists():
                shutil.rmtree(cmdout_dir)
            cmdout_dir.mkdir(exist_ok=True)

            # 跳过清空logs目录，因为程序正在使用日志文件
            self.logger.info("cmdout目录已清空")

        except Exception as e:
            self.logger.error(f"清空输出目录失败: {e}")

    def _clear_database_tables(self):
        """清空数据库表（只清空数据表，保留基础数据表）"""
        try:
            if not self.db_manager:
                self.logger.error("数据库管理器未初始化")
                return

            # 只清空数据表，不清空基础数据表（pump_stations, devices）
            tables_to_clear = [
                'pump_data', 'main_pipe_data', 'raw_data_by_device',
                'raw_data_by_station'
            ]

            for table in tables_to_clear:
                try:
                    self.db_manager.execute_query(f"DELETE FROM {table}")
                    self.logger.info(f"已清空数据表: {table}")
                except Exception as e:
                    self.logger.warning(f"清空数据表失败: {table}, 错误: {e}")

            self.logger.info("数据库数据表已清空（保留基础数据表）")

        except Exception as e:
            self.logger.error(f"清空数据库表失败: {e}")

    def _process_device_group_results(self, result: Dict[str, Any]):
        """处理设备分组结果并插入数据库"""
        try:
            device_results = result.get('device_results', {})

            for device_name, device_result in device_results.items():
                if not device_result.get('success', False):
                    self.logger.warning(f"跳过失败的设备: {device_name}")
                    continue

                records = device_result.get('records', [])
                target_table = device_result.get('target_table', 'raw_data_by_device')

                if not records:
                    self.logger.warning(f"设备 {device_name} 没有记录")
                    continue

                # 填充device_id
                self._fill_device_ids(records, device_name)

                # 批量插入数据库
                self._batch_insert_records(records, target_table, device_name)

        except Exception as e:
            self.logger.error(f"处理设备分组结果失败: {e}")
            raise

    def _fill_device_ids(self, records: List[Dict], device_name: str):
        """填充记录的device_id"""
        try:
            device_id = self.data_transformer.get_device_id_by_name(device_name)
            if device_id is None:
                self.logger.warning(f"设备ID查询失败: {device_name}")
                device_id = 1  # 默认值

            for record in records:
                record['device_id'] = device_id

        except Exception as e:
            self.logger.error(f"填充device_id失败: {device_name}, 错误: {e}")

    def _batch_insert_records(self, records: List[Dict], target_table: str, device_name: str):
        """批量插入记录"""
        try:
            batch_size = 1000
            total_inserted = 0

            for i in range(0, len(records), batch_size):
                batch = records[i:i + batch_size]

                # 使用数据库处理器插入
                if target_table == 'pump_data':
                    sql = self.data_transformer.get_insert_sql('pump_data')
                    params = [self._extract_pump_params(record) for record in batch]
                elif target_table == 'main_pipe_data':
                    sql = self.data_transformer.get_insert_sql('main_pipe_data')
                    params = [self._extract_pipe_params(record) for record in batch]
                else:
                    # raw_data格式
                    sql = """
                    INSERT INTO raw_data_by_device
                    (station_id, device_name, param_name, tag_name, data_time,
                     data_quality, data_value, file_source, batch_id, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    params = [self._extract_raw_params(record) for record in batch]

                # 执行批量插入
                self.db_manager.execute_batch_insert(sql, params)
                total_inserted += len(batch)

                self.logger.debug(f"设备 {device_name} 批量插入: {len(batch)} 条记录")

            self.logger.info(f"设备 {device_name} 插入完成: {total_inserted} 条记录到 {target_table}")

        except Exception as e:
            self.logger.error(f"批量插入失败: {device_name} -> {target_table}, 错误: {e}")
            raise

    def _extract_pump_params(self, record: Dict) -> List:
        """提取泵记录参数"""
        return [
            record.get('device_id', 1),
            record.get('pump_name', ''),
            record.get('station_id', 1),
            record.get('data_time', '2025-07-29 12:00:00'),
            record.get('frequency', None),
            record.get('power', None),
            record.get('kwh', None),
            record.get('power_factor', None),
            record.get('voltage_a', None),
            record.get('voltage_b', None),
            record.get('voltage_c', None),
            record.get('current_a', None),
            record.get('current_b', None),
            record.get('current_c', None),
            record.get('outlet_pressure', None),
            record.get('outlet_flow', None),
            record.get('head', None),
            record.get('inlet_pressure', None),
            record.get('outlet_temperature', None),
            record.get('pump_status', 'running'),
            record.get('is_normal', 1),
            record.get('created_at', datetime.now()),
            record.get('updated_at', datetime.now())
        ]

    def _extract_pipe_params(self, record: Dict) -> List:
        """提取管道记录参数"""
        return [
            record.get('device_id', 1),
            record.get('main_pipe_name', ''),
            record.get('station_id', 1),
            record.get('data_time', '2025-07-29 12:00:00'),
            record.get('pressure', None),
            record.get('flow_rate', None),
            record.get('cumulative_flow', None),
            record.get('reserved_decimal_1', None),
            record.get('reserved_decimal_2', None),
            record.get('reserved_decimal_3', None),
            record.get('reserved_decimal_4', None),
            record.get('reserved_decimal_5', None),
            record.get('reserved_decimal_6', None),
            record.get('reserved_decimal_7', None),
            record.get('normal', 1),
            record.get('created_at', datetime.now()),
            record.get('updated_at', None)
        ]

    def _extract_raw_params(self, record: Dict) -> List:
        """提取原始记录参数"""
        return [
            record.get('station_id', ''),
            record.get('device_name', ''),
            record.get('param_name', ''),
            record.get('tag_name', ''),
            record.get('data_time', '2025-07-29 12:00:00'),
            record.get('data_quality', 192),
            record.get('data_value', 0.0),
            record.get('file_source', ''),
            record.get('batch_id', ''),
            record.get('created_at', datetime.now())
        ]

    def _generate_device_group_report(self, result: Dict[str, Any]):
        """生成设备分组处理报告"""
        try:
            end_time = datetime.now()
            duration = (end_time - self.start_time).total_seconds()

            stats = result.get('stats', {})
            device_results = result.get('device_results', {})

            # 统计成功和失败的设备
            successful_devices = sum(1 for r in device_results.values() if r.get('success', False))
            failed_devices = len(device_results) - successful_devices
            total_records = sum(r.get('record_count', 0) for r in device_results.values())

            report_lines = [
                "# 设备分组处理完成报告",
                "",
                f"## 处理信息",
                f"- **开始时间**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- **结束时间**: {end_time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- **总耗时**: {duration:.2f}秒 ({duration/60:.1f}分钟)",
                "",
                f"## 设备处理统计",
                f"- **总设备数**: {len(device_results)}",
                f"- **处理成功**: {successful_devices}",
                f"- **处理失败**: {failed_devices}",
                f"- **总记录数**: {total_records:,}",
                "",
                f"## 详细统计",
                f"- **文件处理数**: {stats.get('files_processed', 0)}",
                f"- **时间点处理数**: {stats.get('time_points_processed', 0)}",
                f"- **记录合并数**: {stats.get('records_merged', 0)}",
                "",
                f"## 设备详情"
            ]

            # 添加每个设备的详情
            for device_name, device_result in device_results.items():
                if device_result.get('success', False):
                    record_count = device_result.get('record_count', 0)
                    target_table = device_result.get('target_table', 'unknown')
                    param_count = device_result.get('param_count', 0)
                    time_points = device_result.get('time_points', 0)

                    report_lines.extend([
                        f"- **{device_name}**: {record_count:,} 条记录 → {target_table}",
                        f"  - 参数文件数: {param_count}",
                        f"  - 时间点数: {time_points:,}"
                    ])
                else:
                    error = device_result.get('error', 'Unknown error')
                    report_lines.append(f"- **{device_name}**: 处理失败 - {error}")

            # 保存报告
            report_content = "\n".join(report_lines)
            report_path = Path('cmdout/设备分组处理报告.md')
            report_path.parent.mkdir(exist_ok=True)

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)

            self.logger.info(f"设备分组处理报告已生成: {report_path}")

            # 输出到终端
            print("\n" + "="*50)
            print("设备分组处理完成")
            print("="*50)
            print(f"总设备数: {len(device_results)}")
            print(f"处理成功: {successful_devices}")
            print(f"处理失败: {failed_devices}")
            print(f"总记录数: {total_records:,}")
            print(f"总耗时: {duration:.1f}秒")
            print("="*50)

        except Exception as e:
            self.logger.error(f"生成设备分组处理报告失败: {e}")

    def _wait_for_completion(self):
        """等待处理完成"""
        self.logger.info("等待所有任务完成...")

        timeout = 3600  # 1小时超时
        check_interval = 30  # 每30秒检查一次
        elapsed = 0

        while elapsed < timeout and not self.shutdown_requested:
            time.sleep(check_interval)
            elapsed += check_interval

            # 获取统计信息
            if self.file_processor and self.database_handler:
                file_stats = self.file_processor.get_stats()
                db_stats = self.database_handler.get_stats()

                progress_msg = f"进度报告 (已运行 {elapsed//60}分{elapsed%60}秒):"
                files_msg = f"   文件处理: 成功 {file_stats['files_processed']}, 失败 {file_stats['files_failed']}"
                db_msg = f"   数据库操作: 插入 {db_stats.get('insert_success', 0)}, 错误 {db_stats.get('insert_errors', 0)}"
                db_msg = f"   数据库插入: 成功 {file_stats['insert_success']}, 失败 {file_stats['insert_failed']}"
                records_msg = f"   总记录数: {file_stats['db_records']}"

                # 同时输出到日志和终端
                self.logger.info(progress_msg)
                self.logger.info(files_msg)
                self.logger.info(db_msg)
                self.logger.info(records_msg)

                # 输出到终端
                print(f"\n{progress_msg}")
                print(files_msg)
                print(db_msg)
                print(records_msg)

                # 检查是否完成
                total_processed = file_stats['files_processed'] + file_stats['files_failed']
                if total_processed >= self.processed_files:
                    self.logger.info("所有任务已完成！")
                    break

        if elapsed >= timeout:
            self.logger.warning("等待超时，强制结束")
    
    def _generate_processing_report(self):
        """生成处理报告"""
        try:
            if not self.start_time:
                return

            end_time = datetime.now()
            duration = (end_time - self.start_time).total_seconds()

            # 获取最终统计 - 修复统计信息来源
            file_stats = self.file_processor.get_stats() if self.file_processor else {}
            db_stats = self.database_handler.get_stats() if self.database_handler else {}

            # 使用正确的统计字段名
            insert_success = file_stats.get('insert_success', 0)
            insert_failed = file_stats.get('insert_failed', 0)
            total_records = file_stats.get('db_records', 0)

            # 数据库统计信息（用于日志记录）
            db_insert_success = db_stats.get('insert_success', 0)
            if db_insert_success > 0:
                self.logger.info(f"数据库统计: 成功插入 {db_insert_success} 条记录")

            report_lines = [
                "# 数据处理完成报告",
                "",
                f"## 处理信息",
                f"- **开始时间**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- **结束时间**: {end_time.strftime('%Y-%m-%d %H:%M:%S')}",
                f"- **总耗时**: {duration:.2f}秒 ({duration/60:.1f}分钟)",
                "",
                f"## 处理统计",
                f"- **处理文件数**: {self.processed_files}",
                f"- **文件处理成功**: {file_stats.get('files_processed', 0)}",
                f"- **文件处理失败**: {file_stats.get('files_failed', 0)}",
                f"- **数据库插入成功**: {insert_success}",
                f"- **数据库插入失败**: {insert_failed}",
                f"- **总记录数**: {total_records:,}",
                "",
                f"## 性能指标",
            ]

            # 计算平均处理速度
            if duration > 0 and total_records > 0:
                speed = total_records / duration
                report_lines.append(f"- **平均处理速度**: {speed:,.0f} 条/秒")
                report_lines.append(f"- **处理效率**: {(total_records/1000000):.1f}M 条记录")

            # 添加详细统计信息
            if file_stats:
                report_lines.extend([
                    "",
                    f"## 详细统计",
                    f"- **文件处理总时间**: {file_stats.get('processing_time', 0):.1f}秒",
                    f"- **平均文件处理时间**: {file_stats.get('processing_time', 0) / max(file_stats.get('files_processed', 1), 1):.2f}秒/文件",
                ])

            report_lines.extend([
                "",
                f"**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            ])

            # 保存报告
            report_path = project_root / "cmdout" / "processing_report.md"
            report_path.parent.mkdir(exist_ok=True)

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))

            self.logger.info(f"处理报告已保存: {report_path}")

        except Exception as e:
            self.logger.error(f"生成处理报告失败: {e}")
    
    def shutdown(self):
        """关闭程序"""
        try:
            self.logger.info("开始关闭程序...")

            # 停止消息总线
            if self.message_bus:
                self.message_bus.stop()
                self.logger.info("消息总线已停止")

            # 关闭数据库连接
            if self.db_manager:
                self.db_manager.close_connection()
                self.logger.info("数据库连接已关闭")

            self.logger.info("程序关闭完成")

        except Exception as e:
            self.logger.error(f"程序关闭时发生错误: {e}")

    def cleanup_resources(self):
        """清理所有程序资源"""
        self.logger.info("开始清理PumpOptimizationMain资源")

        try:
            # 1. 清理消息总线
            if hasattr(self, 'message_bus') and self.message_bus:
                if hasattr(self.message_bus, 'cleanup_resources'):
                    self.message_bus.cleanup_resources()
                else:
                    self.message_bus.stop()
                self.logger.info("消息总线资源已清理")

            # 2. 清理数据库管理器
            if hasattr(self, 'db_manager') and self.db_manager:
                if hasattr(self.db_manager, 'cleanup_resources'):
                    self.db_manager.cleanup_resources()
                else:
                    self.db_manager.close_connection()
                self.logger.info("数据库管理器资源已清理")

            # 3. 清理配置管理器
            if hasattr(self, 'config_manager') and self.config_manager:
                if hasattr(self.config_manager, 'cleanup_resources'):
                    self.config_manager.cleanup_resources()
                self.logger.info("配置管理器资源已清理")

            self.logger.info("PumpOptimizationMain资源清理完成")

        except Exception as e:
            self.logger.error(f"PumpOptimizationMain资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def cleanup_resources(self):
        """清理所有程序资源"""
        self.logger.info("开始清理PumpOptimizationMain资源")

        try:
            # 1. 清理消息总线
            if hasattr(self, 'message_bus') and self.message_bus:
                if hasattr(self.message_bus, 'cleanup_resources'):
                    self.message_bus.cleanup_resources()
                else:
                    self.message_bus.stop()
                self.logger.info("消息总线资源已清理")

            # 2. 清理数据库管理器
            if hasattr(self, 'db_manager') and self.db_manager:
                if hasattr(self.db_manager, 'cleanup_resources'):
                    self.db_manager.cleanup_resources()
                else:
                    self.db_manager.close_connection()
                self.logger.info("数据库管理器资源已清理")

            # 3. 清理配置管理器
            if hasattr(self, 'config_manager') and self.config_manager:
                if hasattr(self.config_manager, 'cleanup_resources'):
                    self.config_manager.cleanup_resources()
                self.logger.info("配置管理器资源已清理")

            # 4. 清理数据转换器
            if hasattr(self, 'data_transformer') and self.data_transformer:
                if hasattr(self.data_transformer, 'cleanup_resources'):
                    self.data_transformer.cleanup_resources()
                self.logger.info("数据转换器资源已清理")

            # 5. 清理处理器
            if hasattr(self, 'file_processor') and self.file_processor:
                if hasattr(self.file_processor, 'cleanup_resources'):
                    self.file_processor.cleanup_resources()
                self.logger.info("文件处理器资源已清理")

            self.logger.info("PumpOptimizationMain资源清理完成")

        except Exception as e:
            self.logger.error(f"PumpOptimizationMain资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        self.cleanup_resources()

        if exc_type is not None:
            self.logger.error(f"PumpOptimizationMain退出时发生异常: {exc_type.__name__}: {exc_val}")
            if exc_tb is not None:
                self.logger.debug("异常追踪信息已记录")

        return False  # 不抑制异常

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='泵组优化程序')
    parser.add_argument('--config-dir', default='config', help='配置文件目录')
    parser.add_argument('--action', choices=['process', 'device_group'],
                       default='device_group', help='执行动作')
    parser.add_argument('--station-id', help='指定泵站ID')
    
    args = parser.parse_args()
    
    # 创建主程序实例
    app = PumpOptimizationMain(args.config_dir)
    
    try:
        # 初始化
        if not app.initialize():
            print("程序初始化失败")
            sys.exit(1)
        
        # 执行指定动作
        if args.action == 'process':
            app.run_data_processing(args.station_id)
        elif args.action == 'device_group':
            app.run_device_group_processing()
        else:
            print(f"不支持的动作: {args.action}")
            print("支持的动作: process, device_group")
            sys.exit(1)

        print("程序执行完成")
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"程序执行失败: {e}")
        sys.exit(1)
    finally:
        app.shutdown()

if __name__ == "__main__":
    main()
