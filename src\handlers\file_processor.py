# -*- coding: utf-8 -*-
"""
文件处理器 - 解耦的文件处理组件
负责CSV文件读取和数据预处理
通过消息总线与其他组件通信
"""

import os
import sys
import time
import threading
import math
import re
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.core.message_bus import MessageHandler, Message, MessageType, get_message_bus
from src.core.config_manager import ConfigManager
from src.utils.logger import get_logger, BusinessMetric, record_business_metric
from src.utils.performance import monitor_performance
from src.utils.monitoring import record_business_operation, PerformanceTimer, get_monitor
from src.utils.exception_handler import handle_exceptions, retry_on_exception

# 导入优化组件
try:
    from src.utils.streaming_processor import StreamingCSVProcessor
    STREAMING_AVAILABLE = True
except ImportError:
    STREAMING_AVAILABLE = False


class FileProcessorHandler(MessageHandler):
    """文件处理器 - 专门处理文件读取和预处理"""
    
    def __init__(self, handler_id: str = "file_processor", config_file: Optional[str] = None):
        super().__init__(handler_id)
        self.logger = get_logger(f"FileProcessor.{handler_id}")

        # 初始化配置管理器
        if config_file is None:
            # 默认配置文件路径
            config_file = os.path.join(project_root, "config", "data_mapping.json")

        try:
            self.config_manager = ConfigManager(config_file)
            self.logger.info(f"配置管理器初始化成功: {config_file}")
        except Exception as e:
            self.logger.error(f"配置管理器初始化失败: {e}")
            self.config_manager = None

        # 🔧 使用集成的表选择功能（不再需要单独的TableSelector）
        # self.table_selector = TableSelector(config_file)  # 已集成到config_manager中

        # 文件处理配置
        self.supported_encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        self.chunk_size = 10000  # 分块读取大小
        self.max_file_size_mb = 500  # 最大文件大小限制

        # 加载数据加载配置
        self._load_data_loading_config()

        # 性能统计
        self.stats = {
            'files_processed': 0,
            'files_failed': 0,
            'total_records': 0,
            'processing_time': 0.0,
            'insert_success': 0,  # 数据库插入成功次数
            'insert_failed': 0,   # 数据库插入失败次数
            'db_records': 0       # 数据库插入记录数
        }

        # 初始化流式处理器
        if STREAMING_AVAILABLE:
            self.streaming_processor = StreamingCSVProcessor()
            self.enable_streaming = True
            self.logger.info("流式处理器已启用")
        else:
            self.streaming_processor = None
            self.enable_streaming = False
            self.logger.warning("流式处理器不可用，使用传统处理方式")

        self.logger.info(f"文件处理器 {handler_id} 初始化完成")

    def _load_data_loading_config(self):
        """加载数据加载配置"""
        try:
            import yaml
            config_path = Path("config/database_config.yaml")

            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)

                data_loading_config = config.get('data_loading', {})

                # 数据加载比例 (0.0-1.0)
                self.load_percentage = data_loading_config.get('load_percentage', 1.0)

                # 采样方式
                self.sampling_method = data_loading_config.get('sampling_method', 'head')

                # 测试模式
                self.test_mode = data_loading_config.get('test_mode', False)

                self.logger.info(f"数据加载配置已加载:")
                self.logger.info(f"  加载比例: {self.load_percentage * 100:.1f}%")
                self.logger.info(f"  采样方式: {self.sampling_method}")
                self.logger.info(f"  测试模式: {self.test_mode}")

                if self.load_percentage < 1.0:
                    self.logger.warning(f"[TEST MODE] 启用部分数据加载模式，仅加载 {self.load_percentage * 100:.1f}% 的数据")

            else:
                # 默认配置
                self.load_percentage = 1.0
                self.sampling_method = 'head'
                self.test_mode = False
                self.logger.warning(f"数据加载配置文件不存在，使用默认配置: {config_path}")

        except Exception as e:
            # 默认配置
            self.load_percentage = 1.0
            self.sampling_method = 'head'
            self.test_mode = False
            self.logger.error(f"加载数据加载配置失败: {e}，使用默认配置")

    def _process_message(self, message: Message) -> Optional[Message]:
        """处理文件处理请求消息"""
        thread_name = threading.current_thread().name

        if message.type != MessageType.FILE_PROCESS_REQUEST:
            self.logger.warning(f"线程 {thread_name} 收到不支持的消息类型: {message.type.value}")
            return None

        try:
            # 提取文件路径和处理参数
            file_path = message.payload.get('file_path')
            station_id = message.payload.get('station_id', 'unknown')
            batch_id = message.payload.get('batch_id', 'unknown')
            target_table = message.payload.get('target_table', 'raw_data_by_device')  # 从消息中获取目标表

            if not file_path:
                raise ValueError("消息中缺少file_path参数")

            self.logger.info(f"[FILE] 线程 {thread_name} 开始处理文件: {file_path} (批次: {batch_id}, 目标表: {target_table})")

            # 执行文件处理，传递目标表信息
            with PerformanceTimer(get_monitor(), 'file_processing', {'file': file_path}):
                result = self._process_file(file_path, station_id, batch_id, target_table)

            # 🔧 使用传递的目标表信息
            result_target_table = result.get('target_table', target_table)

            # 🔧 简化表选择验证（已集成到config_manager中）
            # 基本验证：确保表名在支持的表列表中
            supported_tables = ['pump_data', 'main_pipe_data', 'raw_data_by_station', 'raw_data_by_device']
            if result_target_table not in supported_tables:
                self.logger.warning(f"不支持的表名: {result_target_table}，使用默认表: raw_data_by_device")
                result_target_table = 'raw_data_by_device'

            # 更新统计信息
            self.stats['files_processed'] += 1
            self.stats['total_records'] += result.get('record_count', 0)
            self.stats['processing_time'] += result.get('processing_time', 0)

            # 记录业务指标
            record_business_operation('file_processed', True,
                                    result.get('processing_time', 0), {
                                        'file_path': file_path,
                                        'record_count': result.get('record_count', 0),
                                        'target_table': result_target_table
                                    })

            # 创建成功响应消息
            response_message = Message(
                type=MessageType.FILE_PROCESS_COMPLETED,
                payload={
                    'file_path': file_path,
                    'station_id': station_id,
                    'batch_id': batch_id,
                    'record_count': result.get('record_count', 0),
                    'processing_time': result.get('processing_time', 0),
                    'processed_data': result.get('processed_data', []),
                    'target_table': result_target_table  # 使用验证后的目标表信息
                },
                sender=self.handler_id,
                correlation_id=message.correlation_id
            )

            self.logger.info(f"文件处理完成: {file_path}, 记录数: {result.get('record_count', 0)}, 目标表: {result_target_table}")

            return response_message
            
        except Exception as e:
            self.logger.error(f"文件处理失败: {e}")

            # 更新失败统计
            self.stats['files_failed'] += 1

            # 记录错误业务指标
            record_business_operation('file_processed', False, context={
                'file_path': file_path,
                'error': str(e),
                'error_type': type(e).__name__
            })
            
            # 创建失败响应消息
            error_message = Message(
                type=MessageType.FILE_PROCESS_FAILED,
                payload={
                    'file_path': message.payload.get('file_path'),
                    'station_id': message.payload.get('station_id'),
                    'batch_id': message.payload.get('batch_id'),
                    'error': str(e),
                    'error_type': type(e).__name__
                },
                sender=self.handler_id,
                correlation_id=message.correlation_id
            )
            
            return error_message
    
    @handle_exceptions(context={'operation': 'process_csv_file'})
    @retry_on_exception(max_attempts=2, delay=1.0)
    @monitor_performance("process_file")
    def _process_file(self, file_path: str, station_id: str, batch_id: str, target_table: str = None) -> Dict[str, Any]:
        """处理单个CSV文件 - 集成流式处理"""
        thread_name = threading.current_thread().name
        start_time = time.time()

        self.logger.info(f"[ANALYZE] 线程 {thread_name} 开始文件分析: {file_path}")

        try:
            file_path_obj = Path(file_path)

            # 检查文件是否存在
            if not file_path_obj.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")

            # 检查文件大小
            file_size_mb = file_path_obj.stat().st_size / 1024 / 1024
            if file_size_mb > self.max_file_size_mb:
                raise ValueError(f"文件过大: {file_size_mb:.2f}MB > {self.max_file_size_mb}MB")

            self.logger.info(f"[FILE] 文件大小: {file_size_mb:.2f}MB")

            # 获取文件映射信息
            mapping_info = self._get_file_mapping_info(file_path)
            if not mapping_info:
                raise ValueError(f"无法获取文件映射信息: {file_path}")

            device_name = mapping_info.get('device_name', 'unknown')
            param_name = mapping_info.get('param_name', 'unknown')

            # 🔧 使用集成的表选择功能
            if target_table is None:
                target_table = self.config_manager.select_target_table(file_path)
                self.logger.info(f"自动为文件 {Path(file_path).name} 选择目标表: {target_table}")
            else:
                self.logger.info(f"使用指定目标表: {target_table} (文件: {Path(file_path).name})")

            # 使用流式处理（如果可用且文件较大）
            if self.enable_streaming and file_size_mb > 10:  # 大于10MB使用流式处理
                self.logger.info(f"使用流式处理: {file_path}")
                result = self.streaming_processor.process_file_streaming(
                    file_path, station_id, device_name, param_name, batch_id, target_table
                )

                if result['success']:
                    # 更新数据库插入统计
                    self.stats['insert_success'] += result.get('db_insert_success', 0)
                    self.stats['insert_failed'] += result.get('db_insert_failed', 0)
                    self.stats['db_records'] += result.get('db_records_inserted', 0)

                    self.logger.info(f"流式处理完成: {result['total_records']} 条记录, "
                                   f"数据库插入: {result.get('db_insert_success', 0)} 成功, "
                                   f"{result.get('db_insert_failed', 0)} 失败")
                    # 🔧 关键修复：为数据转换准备样本数据
                    sample_data = result.get('sample_data', [])
                    if sample_data:
                        self.logger.info(f"流式处理提供样本数据: {len(sample_data)} 条记录用于数据转换")

                    return {
                        'record_count': result['total_records'],
                        'processing_time': result['processing_time'],
                        'processed_data': sample_data,  # 返回样本数据用于转换
                        'batch_id': batch_id,  # 添加批次ID用于后续查询
                        'is_streaming': True,  # 标记为流式处理
                        'target_table': target_table  # 目标表信息
                    }
                else:
                    self.logger.warning(f"流式处理失败，回退到传统处理: {result.get('error', 'Unknown error')}")

            # 传统处理方式（作为备用）
            self.logger.info(f"使用传统处理方式: {file_path}")
            df = self._read_csv_with_encoding(file_path_obj)

            if df.empty:
                self.logger.warning(f"文件为空: {file_path}")
                return {
                    'record_count': 0,
                    'processing_time': time.time() - start_time,
                    'processed_data': []
                }

            self.logger.debug(f"文件读取成功，行数: {len(df)}, 列数: {len(df.columns)}")

            # 🔧 修复：添加数据转换逻辑，与流式处理保持一致
            if target_table in ['pump_data', 'main_pipe_data']:
                # 先进行基础预处理
                raw_data = self._preprocess_data(df, station_id, device_name, param_name, batch_id, file_path)

                # 🔧 关键修复：调用数据转换器进行时间对齐和参数合并
                self.logger.info(f"[TRANSFORM] 开始数据转换: {len(raw_data)}条原始记录 -> {target_table}")

                # 获取数据转换器实例
                from core.singleton_data_transformer import get_data_transformer
                transformer = get_data_transformer()

                # 执行数据转换（时间对齐 + 参数合并）
                processed_data = transformer.transform_data(raw_data, target_table)

                self.logger.info(f"[TRANSFORM] 数据转换完成: {len(raw_data)}条原始记录 -> {len(processed_data)}条合并记录")

                # 🔧 使用分区感知UPSERT插入数据，支持跨文件参数合并
                if processed_data:
                    affected_rows = self.db_manager.execute_batch_upsert_partitioned(target_table, processed_data)
                    self.logger.info(f"[PARTITION-UPSERT] 传统处理路径 {target_table} 表分区UPSERT完成: {len(processed_data)} 条记录，影响 {affected_rows} 行")

                    # 更新统计信息
                    self.stats['db_records'] += len(processed_data)
                    self.stats['insert_success'] += 1

                    processing_time = time.time() - start_time

                    return {
                        'success': True,
                        'record_count': len(processed_data),
                        'processing_time': processing_time,
                        'total_records': len(processed_data),
                        'db_insert_success': 1,
                        'db_insert_failed': 0,
                        'db_records_inserted': len(processed_data),
                        'sample_data': processed_data[:5] if len(processed_data) > 5 else processed_data,
                        'target_table': target_table
                    }
            else:
                # 原始数据表，使用传统预处理
                processed_data = self._preprocess_data(df, station_id, device_name, param_name, batch_id, file_path)

            processing_time = time.time() - start_time

            return {
                'record_count': len(processed_data),
                'processing_time': processing_time,
                'processed_data': processed_data,
                'target_table': target_table,  # 🔧 添加目标表信息
                'is_streaming': False  # 标记为传统处理
            }
            
        except Exception as e:
            self.logger.error(f"处理文件失败: {file_path}, 错误: {e}")
            raise
    
    @monitor_performance("read_csv_with_encoding")
    def _read_csv_with_encoding(self, file_path: Path) -> pd.DataFrame:
        """尝试不同编码读取CSV文件，支持数据采样"""
        for encoding in self.supported_encodings:
            try:
                self.logger.debug(f"尝试使用编码 {encoding} 读取文件")

                # 如果是完整加载，直接读取
                if self.load_percentage >= 1.0:
                    df = pd.read_csv(
                        file_path,
                        encoding=encoding,
                        low_memory=False,
                        na_values=['', 'NULL', 'null', 'NaN', 'nan']
                    )
                else:
                    # 部分加载模式
                    df = self._read_csv_with_sampling(file_path, encoding)

                self.logger.debug(f"成功使用编码 {encoding} 读取文件，行数: {len(df)}")

                if self.load_percentage < 1.0:
                    self.logger.info(f"[SAMPLING] 采样完成: 原始数据 -> 采样数据 ({len(df)} 行, {self.load_percentage * 100:.1f}%)")

                return df

            except UnicodeDecodeError:
                self.logger.debug(f"编码 {encoding} 读取失败，尝试下一个")
                continue
            except Exception as e:
                self.logger.warning(f"使用编码 {encoding} 读取文件时发生错误: {e}")
                continue

        raise ValueError(f"无法使用任何支持的编码读取文件: {file_path}")

    def _read_csv_with_sampling(self, file_path: Path, encoding: str) -> pd.DataFrame:
        """使用采样方式读取CSV文件"""
        try:
            if self.sampling_method == 'head':
                # 从头部采样 - 保持时间连续性
                return self._read_csv_head_sampling(file_path, encoding)
            elif self.sampling_method == 'random':
                # 随机采样 - 更好的数据分布
                return self._read_csv_random_sampling(file_path, encoding)
            elif self.sampling_method == 'tail':
                # 从尾部采样
                return self._read_csv_tail_sampling(file_path, encoding)
            else:
                # 默认使用头部采样
                self.logger.warning(f"未知的采样方式: {self.sampling_method}，使用头部采样")
                return self._read_csv_head_sampling(file_path, encoding)

        except Exception as e:
            self.logger.error(f"采样读取失败: {e}")
            raise

    def _read_csv_head_sampling(self, file_path: Path, encoding: str) -> pd.DataFrame:
        """从文件头部采样读取"""
        try:
            # 先读取少量行来确定总行数
            sample_df = pd.read_csv(file_path, encoding=encoding, nrows=1000)

            # 估算总行数（通过文件大小）
            file_size = file_path.stat().st_size
            avg_line_size = file_size / 1000 if len(sample_df) > 0 else 100
            estimated_total_rows = int(file_size / avg_line_size)

            # 计算需要读取的行数
            target_rows = max(1, int(estimated_total_rows * self.load_percentage))

            self.logger.debug(f"[HEAD SAMPLING] 预估总行数: {estimated_total_rows:,}, 目标行数: {target_rows:,}")

            # 读取指定行数
            df = pd.read_csv(
                file_path,
                encoding=encoding,
                nrows=target_rows,
                low_memory=False,
                na_values=['', 'NULL', 'null', 'NaN', 'nan']
            )

            return df

        except Exception as e:
            self.logger.error(f"头部采样失败: {e}")
            raise

    def _read_csv_random_sampling(self, file_path: Path, encoding: str) -> pd.DataFrame:
        """随机采样读取"""
        try:
            # 先读取全部数据（对于大文件可能需要优化）
            df = pd.read_csv(
                file_path,
                encoding=encoding,
                low_memory=False,
                na_values=['', 'NULL', 'null', 'NaN', 'nan']
            )

            total_rows = len(df)
            target_rows = max(1, int(total_rows * self.load_percentage))

            self.logger.debug(f"[RANDOM SAMPLING] 总行数: {total_rows:,}, 目标行数: {target_rows:,}")

            if target_rows >= total_rows:
                return df

            # 随机采样
            sampled_df = df.sample(n=target_rows, random_state=42).sort_index()

            return sampled_df

        except Exception as e:
            self.logger.error(f"随机采样失败: {e}")
            raise

    def _read_csv_tail_sampling(self, file_path: Path, encoding: str) -> pd.DataFrame:
        """从文件尾部采样读取"""
        try:
            # 读取全部数据
            df = pd.read_csv(
                file_path,
                encoding=encoding,
                low_memory=False,
                na_values=['', 'NULL', 'null', 'NaN', 'nan']
            )

            total_rows = len(df)
            target_rows = max(1, int(total_rows * self.load_percentage))

            self.logger.debug(f"[TAIL SAMPLING] 总行数: {total_rows:,}, 目标行数: {target_rows:,}")

            if target_rows >= total_rows:
                return df

            # 从尾部取数据
            tail_df = df.tail(target_rows)

            return tail_df

        except Exception as e:
            self.logger.error(f"尾部采样失败: {e}")
            raise
    
    def _process_data_chunk(self, chunk_data: Dict[str, List], start_idx: int, end_idx: int,
                           thread_id: int) -> List[Dict[str, Any]]:
        """处理数据块 - 多线程辅助方法"""
        try:
            chunk_records = []
            chunk_size = end_idx - start_idx

            self.logger.debug(f"[CHUNK] 线程 {thread_id} 开始处理数据块: {start_idx}-{end_idx} ({chunk_size}条)")

            for i in range(start_idx, end_idx):
                record = {
                    'station_id': chunk_data['station_id'][i],
                    'device_name': chunk_data['device_name'][i],
                    'param_name': chunk_data['param_name'][i],
                    'tag_name': chunk_data['tag_name'][i],
                    'data_time': chunk_data['data_time'][i],
                    'data_quality': chunk_data['data_quality'][i],
                    'data_value': chunk_data['data_value'][i],
                    'file_source': chunk_data['file_source'][i],
                    'batch_id': chunk_data['batch_id'][i],
                    'row_index': chunk_data['row_index'][i]
                }
                chunk_records.append(record)

            self.logger.debug(f"线程 {thread_id} 完成数据块处理: {len(chunk_records)}条记录")
            return chunk_records

        except Exception as e:
            self.logger.error(f"线程 {thread_id} 处理数据块失败: {e}")
            raise

    @monitor_performance("preprocess_data")
    def _preprocess_data(self, df: pd.DataFrame, station_id: str, device_name: str,
                        param_name: str, batch_id: str, file_path: str) -> List[Dict[str, Any]]:
        """数据预处理 - 多线程高性能处理"""
        try:
            total_rows = len(df)
            self.logger.info(f"[PREPROCESS] 开始多线程预处理 {total_rows} 行数据")
            self.logger.info(f"[PREPROCESS] 设备名称: {device_name}, 参数名称: {param_name}")

            # 向量化处理 - 避免逐行循环
            processed_data = {
                'station_id': [station_id] * total_rows,
                'device_name': [device_name] * total_rows,
                'param_name': [param_name] * total_rows,  # 从文件名提取参数
                'tag_name': df.get('TagName', 'unknown').astype(str).tolist(),
                'data_time': df.get('DataTime', '2025-07-27 23:50:00').astype(str).tolist(),
                'data_quality': df.get('DataQuality', 192).fillna(192).astype(int).tolist(),
                'data_value': df.get('DataValue', 0.0).fillna(0.0).astype(float).tolist(),
                'file_source': [str(file_path)] * total_rows,
                'batch_id': [batch_id] * total_rows,
                'row_index': list(range(total_rows))
            }

            # 确定线程数和块大小
            max_threads = min(4, max(1, total_rows // 50000))  # 每5万行一个线程，最多4个线程
            chunk_size = math.ceil(total_rows / max_threads)

            self.logger.info(f"[THREAD] 多线程配置: {max_threads}个线程, 每块{chunk_size}行")

            # 多线程处理数据转换
            processed_records = []
            with ThreadPoolExecutor(max_workers=max_threads, thread_name_prefix="DataProcessor") as executor:
                # 提交任务
                futures = []
                for i in range(max_threads):
                    start_idx = i * chunk_size
                    end_idx = min((i + 1) * chunk_size, total_rows)

                    if start_idx < total_rows:
                        future = executor.submit(
                            self._process_data_chunk,
                            processed_data,
                            start_idx,
                            end_idx,
                            i + 1
                        )
                        futures.append(future)

                # 收集结果
                for future in as_completed(futures):
                    try:
                        chunk_result = future.result()
                        processed_records.extend(chunk_result)
                    except Exception as e:
                        self.logger.error(f"获取线程结果失败: {e}")
                        raise

            self.logger.info(f"多线程预处理完成，处理记录: {len(processed_records)}")
            return processed_records

        except Exception as e:
            self.logger.error(f"数据预处理失败: {e}")
            import traceback
            traceback.print_exc()
            raise






    

    
    def _parse_datetime(self, datetime_value) -> str:
        """解析日期时间"""
        if pd.isna(datetime_value):
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            # 尝试解析不同的日期时间格式
            if isinstance(datetime_value, str):
                # 常见格式处理
                dt = pd.to_datetime(datetime_value)
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                return str(datetime_value)
        except:
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def _parse_numeric_value(self, value) -> float:
        """解析数值"""
        if pd.isna(value):
            return 0.0
        
        try:
            return float(value)
        except:
            return 0.0
    

    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            **self.stats,
            'handler_id': self.handler_id,
            'is_active': self.is_active
        }

    def cleanup_resources(self):
        """清理所有资源"""
        self.logger.info(f"开始清理FileProcessorHandler资源: {self.handler_id}")

        try:
            # 1. 停止处理器
            self.is_active = False

            # 2. 清理数据库连接
            if hasattr(self, 'db_manager') and self.db_manager:
                self.logger.info("清理数据库管理器资源")
                self.db_manager.cleanup_resources()
                self.db_manager = None

            # 3. 清理配置管理器
            if hasattr(self, 'config_manager') and self.config_manager:
                self.logger.info("清理配置管理器资源")
                self.config_manager = None

            # 4. 清理流式处理器
            if hasattr(self, 'streaming_processor') and self.streaming_processor:
                self.logger.info("清理流式处理器资源")
                if hasattr(self.streaming_processor, 'cleanup_resources'):
                    self.streaming_processor.cleanup_resources()
                self.streaming_processor = None

            # 5. 重置统计信息
            self.stats = {
                'files_processed': 0,
                'files_failed': 0,
                'total_records': 0,
                'processing_time': 0.0,
                'insert_success': 0,
                'insert_failed': 0,
                'db_records': 0
            }

            self.logger.info(f"FileProcessorHandler资源清理完成: {self.handler_id}")

        except Exception as e:
            self.logger.error(f"FileProcessorHandler资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        self.cleanup_resources()

        if exc_type is not None:
            self.logger.error(f"FileProcessorHandler退出时发生异常: {exc_type.__name__}: {exc_val}")

        return False  # 不抑制异常

    def _get_file_mapping_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件映射信息"""
        try:
            if self.config_manager:
                return self.config_manager.get_file_mapping_info(file_path)
            else:
                # 如果配置管理器不可用，返回默认映射
                file_name = Path(file_path).stem
                return {
                    'device_name': file_name,
                    'param_name': 'default',
                    'table_name': 'raw_data_by_device'
                }
        except Exception as e:
            self.logger.warning(f"获取文件映射信息失败: {e}")
            # 返回默认映射
            file_name = Path(file_path).stem
            return {
                'device_name': file_name,
                'param_name': 'default',
                'table_name': 'raw_data_by_device'
            }


if __name__ == "__main__":
    # 测试文件处理器
    logger = get_logger("FileProcessorTest")
    
    logger.info("开始测试文件处理器")
    
    # 创建消息总线和文件处理器
    bus = get_message_bus()
    bus.start()
    
    processor = FileProcessorHandler("test_processor")
    bus.subscribe(MessageType.FILE_PROCESS_REQUEST, processor)
    
    # 创建测试消息
    test_message = Message(
        type=MessageType.FILE_PROCESS_REQUEST,
        payload={
            'file_path': 'data/2天/test.csv',
            'station_id': 'test_station',
            'batch_id': 'test_batch_001'
        },
        sender='test_sender'
    )
    
    # 发布消息
    bus.publish(test_message)
    
    # 等待处理
    time.sleep(2)
    
    # 获取统计信息
    stats = processor.get_stats()
    logger.info(f"文件处理器统计: {stats}")
    
    # 停止消息总线
    bus.stop()
    
    logger.info("文件处理器测试完成")
