# 统一异常处理策略配置
# 定义系统中不同类型操作的异常处理规则

# 全局异常处理配置
global:
  # 默认重试策略
  default_retry:
    max_attempts: 3
    base_delay: 1.0
    backoff_factor: 2.0
    
  # 异常严重程度阈值
  severity_thresholds:
    critical_error_count: 10      # 严重错误数量阈值
    high_error_rate: 0.1          # 高错误率阈值 (10%)
    medium_error_rate: 0.05       # 中等错误率阈值 (5%)
    
  # 日志记录配置
  logging:
    include_stack_trace: true     # 是否包含堆栈跟踪
    max_context_length: 500       # 上下文信息最大长度
    error_aggregation_window: 300 # 错误聚合窗口(秒)

# 按操作类型的异常处理策略
operation_strategies:
  
  # 数据库操作
  database:
    # 数据库连接
    connection:
      retry:
        max_attempts: 5
        base_delay: 2.0
        backoff_factor: 2.0
      timeout: 30
      critical_on_failure: true
      
    # 数据库查询
    query:
      retry:
        max_attempts: 3
        base_delay: 1.0
        backoff_factor: 1.5
      timeout: 60
      log_slow_queries: true
      slow_query_threshold: 5.0
      
    # 批量插入
    batch_insert:
      retry:
        max_attempts: 2
        base_delay: 0.5
        backoff_factor: 2.0
      timeout: 120
      rollback_on_failure: true
      
  # 文件操作
  file_processing:
    # CSV文件读取
    csv_read:
      retry:
        max_attempts: 2
        base_delay: 1.0
        backoff_factor: 1.5
      encoding_fallback: true
      max_file_size_mb: 500
      
    # 文件写入
    file_write:
      retry:
        max_attempts: 3
        base_delay: 0.5
        backoff_factor: 2.0
      backup_on_failure: true
      
  # 网络操作
  network:
    # HTTP请求
    http_request:
      retry:
        max_attempts: 3
        base_delay: 2.0
        backoff_factor: 2.0
      timeout: 30
      
    # 数据传输
    data_transfer:
      retry:
        max_attempts: 5
        base_delay: 1.0
        backoff_factor: 1.5
      chunk_size: 8192
      
  # 消息处理
  message_processing:
    # 消息发布
    publish:
      retry:
        max_attempts: 2
        base_delay: 0.1
        backoff_factor: 2.0
      queue_timeout: 5.0
      
    # 消息处理
    process:
      retry:
        max_attempts: 1  # 消息处理通常不重试
        base_delay: 0.0
      dead_letter_queue: true
      
  # 配置管理
  configuration:
    # 配置加载
    load:
      retry:
        max_attempts: 3
        base_delay: 1.0
        backoff_factor: 1.5
      validate_on_load: true
      
    # 配置验证
    validate:
      retry:
        max_attempts: 1  # 验证失败通常不重试
        base_delay: 0.0
      strict_mode: true

# 异常类型映射
exception_mappings:
  # 数据库异常
  database_errors:
    - "pymysql.err.OperationalError"
    - "sqlalchemy.exc.OperationalError"
    - "sqlalchemy.exc.DatabaseError"
    - "ConnectionError"
    
  # 文件I/O异常
  file_errors:
    - "FileNotFoundError"
    - "PermissionError"
    - "OSError"
    - "IOError"
    - "UnicodeDecodeError"
    
  # 网络异常
  network_errors:
    - "requests.exceptions.ConnectionError"
    - "requests.exceptions.Timeout"
    - "urllib3.exceptions.ConnectionError"
    - "socket.error"
    
  # 验证异常
  validation_errors:
    - "ValueError"
    - "TypeError"
    - "KeyError"
    - "AttributeError"
    
  # 业务逻辑异常
  business_errors:
    - "BusinessLogicError"
    - "DataValidationError"
    - "ConfigurationError"
    
  # 系统异常
  system_errors:
    - "MemoryError"
    - "SystemError"
    - "RuntimeError"

# 监控和告警配置
monitoring:
  # 错误率监控
  error_rate_monitoring:
    enabled: true
    check_interval: 60        # 检查间隔(秒)
    alert_threshold: 0.05     # 告警阈值(5%)
    
  # 异常趋势分析
  trend_analysis:
    enabled: true
    window_size: 3600         # 分析窗口(秒)
    trend_threshold: 2.0      # 趋势阈值(倍数)
    
  # 性能影响监控
  performance_impact:
    enabled: true
    latency_threshold: 5.0    # 延迟阈值(秒)
    throughput_threshold: 0.8 # 吞吐量阈值(80%)

# 恢复策略
recovery_strategies:
  # 自动恢复
  auto_recovery:
    enabled: true
    max_recovery_attempts: 3
    recovery_delay: 10.0
    
  # 降级策略
  degradation:
    enabled: true
    fallback_mode: "safe"
    reduced_functionality: true
    
  # 熔断器
  circuit_breaker:
    enabled: true
    failure_threshold: 5      # 失败阈值
    recovery_timeout: 60      # 恢复超时(秒)
    half_open_max_calls: 3    # 半开状态最大调用数

# 日志和报告
reporting:
  # 异常报告
  exception_reports:
    enabled: true
    report_interval: 3600     # 报告间隔(秒)
    include_statistics: true
    include_trends: true
    
  # 性能报告
  performance_reports:
    enabled: true
    report_interval: 1800     # 报告间隔(秒)
    include_latency: true
    include_throughput: true
    
  # 导出配置
  export:
    format: "json"            # 导出格式: json, csv, xml
    destination: "logs/exception_reports"
    retention_days: 30
