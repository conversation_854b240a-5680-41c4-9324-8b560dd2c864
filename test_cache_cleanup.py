"""
缓存清理机制测试脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_device_cache_cleanup():
    """测试设备ID缓存清理"""
    print("=" * 50)
    print("测试设备ID缓存清理")
    print("=" * 50)
    
    try:
        from src.utils.device_id_cache import DeviceIDCache
        
        # 创建缓存实例
        cache = DeviceIDCache(max_cache_size=10, memory_limit_mb=1.0)
        print("[OK] 设备ID缓存创建成功")
        
        # 添加一些测试数据
        for i in range(5):
            device_name = f"device_{i:03d}"
            device_id = i + 1
            cache.set_device_id(device_name, device_id)
        
        print(f"[OK] 添加测试数据: {len(cache._cache)} 个设备")
        
        # 获取清理前统计
        stats_before = cache.get_cache_stats()
        print(f"[OK] 清理前统计: 缓存大小={stats_before['cache_size']}")
        
        # 测试上下文管理器
        with DeviceIDCache(max_cache_size=5) as test_cache:
            test_cache.set_device_id("test_device", 999)
            print(f"[OK] 上下文管理器测试: 缓存大小={len(test_cache._cache)}")
        
        print("[OK] 上下文管理器自动清理完成")
        
        # 测试手动清理
        cleared_count = cache.clear_cache()
        print(f"[OK] 手动清理完成: 清理 {cleared_count} 个条目")
        
        # 获取清理后统计
        stats_after = cache.get_cache_stats()
        print(f"[OK] 清理后统计: 缓存大小={stats_after['cache_size']}")
        
        print("[PASS] 设备ID缓存清理测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 设备ID缓存清理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_cache_cleanup():
    """测试时间对齐缓存清理"""
    print("\n" + "=" * 50)
    print("测试时间对齐缓存清理")
    print("=" * 50)
    
    try:
        from src.utils.time_alignment_cache import get_time_alignment_cache
        
        # 创建缓存实例
        cache = get_time_alignment_cache(
            target_format='standard',
            max_cache_size=10,
            memory_limit_mb=1.0
        )
        print("[OK] 时间对齐缓存创建成功")
        
        # 添加一些测试数据
        test_times = [
            "2025-01-01 10:00:00",
            "2025-01-01 10:01:00", 
            "2025-01-01 10:02:00",
            "2025-01-01 10:03:00",
            "2025-01-01 10:04:00"
        ]
        
        for time_str in test_times:
            aligned_time = cache.align_time(time_str)
            print(f"[OK] 时间对齐: {time_str} -> {aligned_time}")
        
        # 获取清理前统计
        stats_before = cache.get_cache_stats()
        print(f"[OK] 清理前统计: 时间缓存={stats_before['time_cache_size']}, 格式缓存={stats_before['format_cache_size']}")
        
        # 测试上下文管理器
        with get_time_alignment_cache(max_cache_size=3) as test_cache:
            test_cache.align_time("2025-01-01 12:00:00")
            test_stats = test_cache.get_cache_stats()
            print(f"[OK] 上下文管理器测试: 时间缓存={test_stats['time_cache_size']}")
        
        print("[OK] 上下文管理器自动清理完成")
        
        # 测试手动清理
        time_count, format_count = cache.clear_cache()
        print(f"[OK] 手动清理完成: 时间缓存 {time_count} 个, 格式缓存 {format_count} 个")
        
        # 获取清理后统计
        stats_after = cache.get_cache_stats()
        print(f"[OK] 清理后统计: 时间缓存={stats_after['time_cache_size']}, 格式缓存={stats_after['format_cache_size']}")
        
        print("[PASS] 时间对齐缓存清理测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 时间对齐缓存清理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_cache_cleanup():
    """测试查询缓存清理"""
    print("\n" + "=" * 50)
    print("测试查询缓存清理")
    print("=" * 50)
    
    try:
        from src.utils.query_cache import QueryCacheManager
        
        # 创建缓存实例（不需要实际Redis连接）
        cache = QueryCacheManager()
        print("[OK] 查询缓存管理器创建成功")
        
        # 测试清理方法（即使Redis未连接也应该正常工作）
        if hasattr(cache, 'cleanup_resources'):
            print("[OK] 查询缓存管理器有cleanup_resources方法")
            cache.cleanup_resources()
            print("[OK] cleanup_resources方法调用成功")
        else:
            print("[FAIL] 查询缓存管理器缺少cleanup_resources方法")
            return False
        
        # 测试上下文管理器
        if hasattr(cache, '__enter__') and hasattr(cache, '__exit__'):
            print("[OK] 查询缓存管理器支持上下文管理器")
            
            with QueryCacheManager() as test_cache:
                print("[OK] 上下文管理器进入成功")
                # 测试清理方法
                cleared_count = test_cache.clear_cache()
                print(f"[OK] 清理操作完成: 清理 {cleared_count} 个键")
            
            print("[OK] 上下文管理器自动清理完成")
        else:
            print("[FAIL] 查询缓存管理器不支持上下文管理器")
            return False
        
        print("[PASS] 查询缓存清理测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 查询缓存清理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_manager():
    """测试统一缓存管理器"""
    print("\n" + "=" * 50)
    print("测试统一缓存管理器")
    print("=" * 50)
    
    try:
        from src.utils.cache_manager import get_cache_manager, register_cache
        from src.utils.device_id_cache import DeviceIDCache
        
        # 获取缓存管理器
        cache_manager = get_cache_manager()
        print("[OK] 获取统一缓存管理器成功")
        
        # 创建测试缓存
        device_cache = DeviceIDCache(max_cache_size=5)
        device_cache.set_device_id("test_device_1", 1)
        device_cache.set_device_id("test_device_2", 2)
        
        # 注册缓存
        register_cache("test_device_cache", device_cache, "device_id")
        print("[OK] 注册测试缓存成功")
        
        # 获取缓存信息
        cache_info = cache_manager.get_cache_info("test_device_cache")
        print(f"[OK] 缓存信息: {cache_info}")
        
        # 获取所有缓存信息
        all_info = cache_manager.get_all_cache_info()
        print(f"[OK] 所有缓存信息: {len(all_info)} 个缓存")
        
        # 检查内存使用
        memory_info = cache_manager.check_memory_usage()
        print(f"[OK] 内存使用检查: 进程内存={memory_info.get('process_memory_mb', 0):.1f}MB")
        
        # 清理指定缓存
        cleared_items = cache_manager.clear_cache("test_device_cache")
        print(f"[OK] 清理指定缓存: 清理 {cleared_items} 个项目")
        
        # 清理所有缓存
        results = cache_manager.clear_all_caches()
        print(f"[OK] 清理所有缓存: {results}")
        
        # 获取全局统计
        global_stats = cache_manager.get_global_stats()
        print(f"[OK] 全局统计: 总缓存数={global_stats['total_caches']}")
        
        print("[PASS] 统一缓存管理器测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 统一缓存管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_resource_manager_integration():
    """测试资源管理器集成缓存清理"""
    print("\n" + "=" * 50)
    print("测试资源管理器集成缓存清理")
    print("=" * 50)
    
    try:
        from src.utils.resource_manager import ResourceManager
        from src.utils.cache_manager import register_cache
        from src.utils.device_id_cache import DeviceIDCache
        
        # 创建测试缓存并注册
        test_cache = DeviceIDCache(max_cache_size=3)
        test_cache.set_device_id("integration_test", 999)
        register_cache("integration_test_cache", test_cache, "device_id")
        
        print("[OK] 创建并注册测试缓存")
        
        # 测试资源管理器的缓存清理
        with ResourceManager() as rm:
            print("[OK] 进入资源管理器上下文")
            
            # 创建一些临时资源
            temp_file = rm.create_temp_file(suffix='.cache_test')
            print(f"[OK] 创建临时文件: {temp_file}")
            
            # 检查缓存是否存在
            from src.utils.cache_manager import get_cache_manager
            cache_manager = get_cache_manager()
            cache_info = cache_manager.get_cache_info("integration_test_cache")
            if cache_info:
                print(f"[OK] 缓存存在: 大小={cache_info.get('current_size', 0)}")
            
        print("[OK] 退出资源管理器上下文（应该自动清理缓存）")
        
        # 验证缓存是否被清理
        cache_info_after = cache_manager.get_cache_info("integration_test_cache")
        if cache_info_after:
            print(f"[OK] 清理后缓存信息: 大小={cache_info_after.get('current_size', 0)}")
        
        print("[PASS] 资源管理器集成缓存清理测试通过")
        return True
        
    except Exception as e:
        print(f"[FAIL] 资源管理器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始缓存清理机制测试")
    print("时间:", __import__('datetime').datetime.now())
    
    results = []
    
    # 测试各种缓存清理
    results.append(test_device_cache_cleanup())
    results.append(test_time_cache_cleanup())
    results.append(test_query_cache_cleanup())
    results.append(test_cache_manager())
    results.append(test_resource_manager_integration())
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("[SUCCESS] 所有测试通过！缓存清理机制完善成功！")
        return True
    else:
        print("[WARNING] 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
