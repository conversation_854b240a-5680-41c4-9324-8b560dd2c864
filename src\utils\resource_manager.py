"""
资源管理器 - 统一管理系统资源的清理和释放
"""

import os
import gc
import threading
import tempfile
import shutil

from typing import List, Dict, Any, Set
from concurrent.futures import ThreadPoolExecutor

from src.utils.logger import get_logger
from src.utils.performance import monitor_performance
from src.utils.cache_manager import get_cache_manager


class ResourceManager:
    """系统资源管理器"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        
        # 资源注册表
        self.registered_resources: List[Any] = []
        self.temp_files: Set[str] = set()
        self.temp_dirs: Set[str] = set()
        self.thread_pools: List[ThreadPoolExecutor] = []
        self.file_handles: List[Any] = []
        
        # 清理状态
        self.is_cleaning = False
        self.cleanup_lock = threading.RLock()
        
        self.logger.info("资源管理器初始化完成")
    
    def register_resource(self, resource: Any, cleanup_method: str = 'cleanup_resources'):
        """注册需要清理的资源"""
        # 使用参数避免警告（cleanup_method为将来功能保留）
        _ = cleanup_method
        with self.cleanup_lock:
            if resource not in self.registered_resources:
                self.registered_resources.append(resource)
                self.logger.debug(f"注册资源: {type(resource).__name__}")
    
    def register_temp_file(self, file_path: str):
        """注册临时文件"""
        with self.cleanup_lock:
            self.temp_files.add(file_path)
            self.logger.debug(f"注册临时文件: {file_path}")
    
    def register_temp_dir(self, dir_path: str):
        """注册临时目录"""
        with self.cleanup_lock:
            self.temp_dirs.add(dir_path)
            self.logger.debug(f"注册临时目录: {dir_path}")
    
    def register_thread_pool(self, executor: ThreadPoolExecutor):
        """注册线程池"""
        with self.cleanup_lock:
            if executor not in self.thread_pools:
                self.thread_pools.append(executor)
                self.logger.debug(f"注册线程池: {executor}")
    
    def register_file_handle(self, file_handle: Any):
        """注册文件句柄"""
        with self.cleanup_lock:
            if file_handle not in self.file_handles:
                self.file_handles.append(file_handle)
                self.logger.debug(f"注册文件句柄: {file_handle}")
    
    def create_temp_file(self, suffix: str = '', prefix: str = 'temp_', dir: str = None) -> str:
        """创建临时文件并自动注册"""
        try:
            fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
            os.close(fd)  # 关闭文件描述符
            
            self.register_temp_file(temp_path)
            self.logger.debug(f"创建临时文件: {temp_path}")
            
            return temp_path
            
        except Exception as e:
            self.logger.error(f"创建临时文件失败: {e}")
            raise
    
    def create_temp_dir(self, suffix: str = '', prefix: str = 'temp_', dir: str = None) -> str:
        """创建临时目录并自动注册"""
        try:
            temp_dir = tempfile.mkdtemp(suffix=suffix, prefix=prefix, dir=dir)
            
            self.register_temp_dir(temp_dir)
            self.logger.debug(f"创建临时目录: {temp_dir}")
            
            return temp_dir
            
        except Exception as e:
            self.logger.error(f"创建临时目录失败: {e}")
            raise
    
    @monitor_performance("cleanup_all_resources")
    def cleanup_all_resources(self):
        """清理所有注册的资源"""
        if self.is_cleaning:
            self.logger.warning("资源清理已在进行中，跳过重复清理")
            return
        
        with self.cleanup_lock:
            self.is_cleaning = True
            
            try:
                self.logger.info("开始清理所有系统资源")
                
                # 1. 清理所有缓存
                self._cleanup_all_caches()

                # 2. 清理注册的资源对象
                self._cleanup_registered_resources()

                # 3. 关闭线程池
                self._cleanup_thread_pools()

                # 4. 关闭文件句柄
                self._cleanup_file_handles()

                # 5. 清理临时文件
                self._cleanup_temp_files()

                # 6. 清理临时目录
                self._cleanup_temp_dirs()

                # 7. 强制垃圾回收
                self._force_garbage_collection()
                
                self.logger.info("所有系统资源清理完成")
                
            except Exception as e:
                self.logger.error(f"资源清理过程中发生错误: {e}")
                import traceback
                self.logger.error(f"清理错误详情: {traceback.format_exc()}")
            
            finally:
                self.is_cleaning = False

    def _cleanup_all_caches(self):
        """清理所有系统缓存"""
        try:
            self.logger.info("开始清理所有系统缓存")
            cache_manager = get_cache_manager()
            results = cache_manager.clear_all_caches()

            total_cleared = sum(results.values()) if results else 0
            self.logger.info(f"系统缓存清理完成: 清理 {len(results)} 个缓存, 总计 {total_cleared} 个项目")

        except Exception as e:
            self.logger.error(f"清理系统缓存失败: {e}")
            import traceback
            self.logger.error(f"缓存清理错误详情: {traceback.format_exc()}")

    def _cleanup_registered_resources(self):
        """清理注册的资源对象"""
        self.logger.info(f"清理注册资源: {len(self.registered_resources)} 个对象")
        
        for resource in self.registered_resources[:]:  # 使用副本避免修改时的问题
            try:
                resource_name = type(resource).__name__
                self.logger.debug(f"清理资源: {resource_name}")
                
                # 尝试调用cleanup_resources方法
                if hasattr(resource, 'cleanup_resources'):
                    resource.cleanup_resources()
                    self.logger.debug(f"资源清理成功: {resource_name}")
                elif hasattr(resource, 'close'):
                    resource.close()
                    self.logger.debug(f"资源关闭成功: {resource_name}")
                else:
                    self.logger.warning(f"资源无清理方法: {resource_name}")
                
            except Exception as e:
                self.logger.error(f"清理资源失败: {type(resource).__name__}, 错误: {e}")
        
        # 清空注册表
        self.registered_resources.clear()
    
    def _cleanup_thread_pools(self):
        """关闭所有线程池"""
        self.logger.info(f"关闭线程池: {len(self.thread_pools)} 个")
        
        for executor in self.thread_pools[:]:
            try:
                self.logger.debug(f"关闭线程池: {executor}")
                
                # 尝试优雅关闭
                try:
                    executor.shutdown(wait=True, timeout=10.0)
                    self.logger.debug("线程池优雅关闭成功")
                except TypeError:
                    # 不支持timeout参数的版本
                    executor.shutdown(wait=True)
                    self.logger.debug("线程池关闭成功（基本方法）")
                
            except Exception as e:
                self.logger.error(f"关闭线程池失败: {e}")
                try:
                    # 强制关闭
                    executor.shutdown(wait=False)
                    self.logger.warning("线程池强制关闭")
                except Exception as force_error:
                    self.logger.error(f"强制关闭线程池也失败: {force_error}")
        
        # 清空线程池列表
        self.thread_pools.clear()
    
    def _cleanup_file_handles(self):
        """关闭所有文件句柄"""
        self.logger.info(f"关闭文件句柄: {len(self.file_handles)} 个")
        
        for handle in self.file_handles[:]:
            try:
                if hasattr(handle, 'close'):
                    handle.close()
                    self.logger.debug(f"文件句柄关闭成功: {handle}")
                
            except Exception as e:
                self.logger.error(f"关闭文件句柄失败: {e}")
        
        # 清空文件句柄列表
        self.file_handles.clear()
    
    def _cleanup_temp_files(self):
        """清理临时文件"""
        self.logger.info(f"清理临时文件: {len(self.temp_files)} 个")
        
        for file_path in self.temp_files.copy():
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    self.logger.debug(f"临时文件删除成功: {file_path}")
                else:
                    self.logger.debug(f"临时文件不存在: {file_path}")
                
                self.temp_files.discard(file_path)
                
            except Exception as e:
                self.logger.error(f"删除临时文件失败: {file_path}, 错误: {e}")
    
    def _cleanup_temp_dirs(self):
        """清理临时目录"""
        self.logger.info(f"清理临时目录: {len(self.temp_dirs)} 个")
        
        for dir_path in self.temp_dirs.copy():
            try:
                if os.path.exists(dir_path):
                    shutil.rmtree(dir_path)
                    self.logger.debug(f"临时目录删除成功: {dir_path}")
                else:
                    self.logger.debug(f"临时目录不存在: {dir_path}")
                
                self.temp_dirs.discard(dir_path)
                
            except Exception as e:
                self.logger.error(f"删除临时目录失败: {dir_path}, 错误: {e}")
    
    def _force_garbage_collection(self):
        """强制垃圾回收"""
        self.logger.info("执行强制垃圾回收")
        
        try:
            # 执行多轮垃圾回收
            total_collected = 0
            for i in range(3):
                collected = gc.collect()
                total_collected += collected
                self.logger.debug(f"垃圾回收第{i+1}轮: 回收对象 {collected} 个")
            
            self.logger.info(f"垃圾回收完成，总计回收对象: {total_collected} 个")
            
        except Exception as e:
            self.logger.error(f"垃圾回收失败: {e}")
    
    def get_resource_stats(self) -> Dict[str, Any]:
        """获取资源统计信息"""
        with self.cleanup_lock:
            return {
                'registered_resources': len(self.registered_resources),
                'temp_files': len(self.temp_files),
                'temp_dirs': len(self.temp_dirs),
                'thread_pools': len(self.thread_pools),
                'file_handles': len(self.file_handles),
                'is_cleaning': self.is_cleaning
            }
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理所有资源"""
        # 使用参数避免警告
        _ = exc_type, exc_val, exc_tb
        self.cleanup_all_resources()
        
        if exc_type is not None:
            self.logger.error(f"ResourceManager退出时发生异常: {exc_type.__name__}: {exc_val}")
        
        return False  # 不抑制异常


# 全局资源管理器实例
_global_resource_manager = None


def get_resource_manager() -> ResourceManager:
    """获取全局资源管理器实例"""
    global _global_resource_manager
    
    if _global_resource_manager is None:
        _global_resource_manager = ResourceManager()
    
    return _global_resource_manager


def cleanup_all_system_resources():
    """清理所有系统资源的便捷函数"""
    manager = get_resource_manager()
    manager.cleanup_all_resources()
