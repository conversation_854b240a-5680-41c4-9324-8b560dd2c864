# -*- coding: utf-8 -*-
"""
Redis查询缓存系统 - 绝不使用内存缓存
"""

import json
import hashlib

from typing import Optional, List, Dict, Any
from src.utils.logger import get_logger

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

class QueryCacheManager:
    """查询缓存管理器 - 强制使用Redis，绝不使用内存缓存"""
    
    def __init__(self, redis_host: str = 'localhost', redis_port: int = 6379):
        self.logger = get_logger(__name__)

        # 初始化统计计数器
        self.cache_hits = 0
        self.cache_misses = 0
        self.cache_writes = 0
        self.cache_errors = 0

        self.logger.info("[PROCESS] === Redis查询缓存管理器初始化 ===")
        self.logger.info(f"[PROCESS] Redis服务器: {redis_host}:{redis_port}")

        if not REDIS_AVAILABLE:
            self.logger.warning("[PROCESS] Redis未安装，缓存功能禁用")
            self.cache_enabled = False
            return

        try:
            self.logger.debug("[PROCESS] 创建Redis连接...")
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                db=0,
                decode_responses=True,
                socket_connect_timeout=2,
                socket_timeout=2,
                retry_on_timeout=False
            )

            # 测试连接
            self.logger.debug("[PROCESS] 测试Redis连接...")
            ping_result = self.redis_client.ping()

            self.cache_enabled = True
            self.logger.info("[PROCESS] Redis缓存连接成功")
            self.logger.info(f"[PROCESS] 连接测试结果: {ping_result}")

            # 获取Redis服务器信息
            try:
                info = self.redis_client.info()
                redis_version = info.get('redis_version', 'unknown')
                used_memory = info.get('used_memory_human', 'unknown')
                connected_clients = info.get('connected_clients', 0)

                self.logger.info(f"[PROCESS] Redis版本: {redis_version}")
                self.logger.info(f"[PROCESS] 内存使用: {used_memory}")
                self.logger.info(f"[PROCESS] 连接客户端: {connected_clients}")
            except Exception as info_error:
                self.logger.debug(f"[PROCESS] 获取Redis信息失败: {info_error}")

        except Exception as e:
            self.logger.warning(f"[PROCESS] Redis连接失败，缓存功能禁用: {e}")
            self.logger.warning("[PROCESS] 提示：可以启动Redis服务来启用缓存功能")
            self.cache_enabled = False

        self.default_ttl = 300  # 5分钟默认缓存
        self.logger.info(f"[PROCESS] 默认缓存TTL: {self.default_ttl}秒")
        self.logger.info("[PROCESS] === Redis查询缓存管理器初始化完成 ===")
        
    def get_cached_query(self, query_key: str) -> Optional[List[Dict]]:
        """获取缓存的查询结果 - 仅使用Redis"""
        if not self.cache_enabled:
            self.logger.debug("[PROCESS] 缓存未启用，跳过缓存查询")
            return None

        try:
            import time
            start_time = time.time()

            self.logger.debug(f"[PROCESS] 查询缓存: {query_key}")
            cached_data = self.redis_client.get(query_key)

            query_time = time.time() - start_time

            if cached_data:
                self.cache_hits += 1
                data_size = len(cached_data)
                result = json.loads(cached_data)
                result_count = len(result) if isinstance(result, list) else 1

                self.logger.info(f"[PROCESS] 缓存命中: {query_key}")
                self.logger.debug(f"[PROCESS]   查询耗时: {query_time:.4f}秒")
                self.logger.debug(f"[PROCESS]   数据大小: {data_size} 字节")
                self.logger.debug(f"[PROCESS]   记录数量: {result_count}")

                return result
            else:
                self.cache_misses += 1
                self.logger.debug(f"[PROCESS] 缓存未命中: {query_key}")
                self.logger.debug(f"[PROCESS]   查询耗时: {query_time:.4f}秒")
                return None

        except Exception as e:
            self.cache_errors += 1
            self.logger.error(f"[PROCESS] 获取Redis缓存失败: {query_key}, 错误: {e}")
            import traceback
            self.logger.error(f"[PROCESS] 缓存错误详情: {traceback.format_exc()}")
            return None
    
    def cache_query_result(self, query_key: str, result: List[Dict], ttl: int = None):
        """缓存查询结果 - 仅使用Redis"""
        if not self.cache_enabled:
            self.logger.debug("[PROCESS] 缓存未启用，跳过缓存写入")
            return

        try:
            import time
            start_time = time.time()

            ttl = ttl or self.default_ttl
            result_count = len(result) if isinstance(result, list) else 1

            self.logger.debug(f"[PROCESS] 写入缓存: {query_key}")
            self.logger.debug(f"[PROCESS]   记录数量: {result_count}")
            self.logger.debug(f"[PROCESS]   TTL: {ttl}秒")

            # 序列化数据
            serialized_data = json.dumps(result, default=str)
            data_size = len(serialized_data)

            # 只使用Redis缓存，绝不使用内存
            self.redis_client.setex(query_key, ttl, serialized_data)

            write_time = time.time() - start_time
            self.cache_writes += 1

            self.logger.info(f"[PROCESS] 缓存写入成功: {query_key}")
            self.logger.debug(f"[PROCESS]   写入耗时: {write_time:.4f}秒")
            self.logger.debug(f"[PROCESS]   数据大小: {data_size} 字节")
            self.logger.debug(f"[PROCESS]   压缩比: {data_size/result_count:.1f} 字节/记录")

        except Exception as e:
            self.cache_errors += 1
            self.logger.error(f"[PROCESS] Redis缓存写入失败: {query_key}, 错误: {e}")
            import traceback
            self.logger.error(f"[PROCESS] 缓存写入错误详情: {traceback.format_exc()}")
    
    def generate_query_key(self, table: str, conditions: Dict, 
                          time_range: tuple = None) -> str:
        """生成查询缓存键"""
        key_parts = [table]
        
        # 添加查询条件
        if conditions:
            conditions_str = json.dumps(conditions, sort_keys=True)
            key_parts.append(hashlib.md5(conditions_str.encode()).hexdigest()[:8])
        
        # 添加时间范围
        if time_range:
            time_str = f"{time_range[0]}_{time_range[1]}"
            key_parts.append(hashlib.md5(time_str.encode()).hexdigest()[:8])
        
        return ":".join(key_parts)
    
    def clear_cache(self, pattern: str = None):
        """清理Redis缓存"""
        if not self.cache_enabled:
            self.logger.debug("缓存未启用，跳过清理")
            return 0

        try:
            deleted_count = 0
            if pattern:
                keys = self.redis_client.keys(pattern)
                if keys:
                    deleted_count = self.redis_client.delete(*keys)
                    self.logger.info(f"Redis缓存清理完成: {pattern}, 删除 {deleted_count} 个键")
            else:
                # 获取清理前的键数量
                info = self.redis_client.info('keyspace')
                db_info = info.get('db0', {})
                keys_before = db_info.get('keys', 0) if isinstance(db_info, dict) else 0

                self.redis_client.flushdb()
                deleted_count = keys_before
                self.logger.info(f"Redis缓存全部清理完成: 删除 {deleted_count} 个键")

            return deleted_count

        except Exception as e:
            self.logger.error(f"清理Redis缓存失败: {e}")
            return 0

    def cleanup_resources(self):
        """清理所有资源"""
        self.logger.info("开始清理QueryCacheManager资源")

        try:
            # 清理所有缓存
            deleted_count = self.clear_cache()

            # 关闭Redis连接
            if self.cache_enabled and self.redis_client:
                try:
                    self.redis_client.close()
                    self.logger.info("Redis连接已关闭")
                except Exception as e:
                    self.logger.warning(f"关闭Redis连接失败: {e}")

            # 重置统计信息
            self.cache_hits = 0
            self.cache_misses = 0
            self.cache_writes = 0

            self.logger.info(f"QueryCacheManager资源清理完成: 清理{deleted_count}个缓存键")

        except Exception as e:
            self.logger.error(f"QueryCacheManager资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        # 使用参数避免警告
        _ = exc_type, exc_val, exc_tb
        self.cleanup_resources()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取Redis缓存统计信息"""
        self.logger.debug("[PROCESS] 获取缓存统计信息")

        if not self.cache_enabled:
            stats = {"status": "disabled", "message": "Redis缓存未启用"}
            self.logger.debug("[PROCESS] 缓存未启用，返回禁用状态")
            return stats

        try:
            import time
            start_time = time.time()

            info = self.redis_client.info()
            query_time = time.time() - start_time

            # Redis服务器统计
            redis_hits = info.get('keyspace_hits', 0)
            redis_misses = info.get('keyspace_misses', 0)
            redis_hit_rate = self._calculate_hit_rate(info)

            # 本地统计
            local_total = self.cache_hits + self.cache_misses
            local_hit_rate = (self.cache_hits / local_total * 100) if local_total > 0 else 0.0

            stats = {
                "status": "active",
                "query_time": f"{query_time:.4f}s",

                # Redis服务器统计
                "redis_stats": {
                    "used_memory": info.get('used_memory_human', 'unknown'),
                    "connected_clients": info.get('connected_clients', 0),
                    "total_commands_processed": info.get('total_commands_processed', 0),
                    "keyspace_hits": redis_hits,
                    "keyspace_misses": redis_misses,
                    "hit_rate": redis_hit_rate
                },

                # 本地应用统计
                "local_stats": {
                    "cache_hits": self.cache_hits,
                    "cache_misses": self.cache_misses,
                    "cache_writes": self.cache_writes,
                    "cache_errors": self.cache_errors,
                    "hit_rate": local_hit_rate,
                    "total_operations": local_total + self.cache_writes
                }
            }

            self.logger.info("[PROCESS] === 缓存统计信息 ===")
            self.logger.info(f"[PROCESS] Redis命中率: {redis_hit_rate:.1f}%")
            self.logger.info(f"[PROCESS] 本地命中率: {local_hit_rate:.1f}%")
            self.logger.info(f"[PROCESS] 内存使用: {stats['redis_stats']['used_memory']}")
            self.logger.info(f"[PROCESS] 连接客户端: {stats['redis_stats']['connected_clients']}")
            self.logger.info(f"[PROCESS] 本地操作: 命中={self.cache_hits}, 未命中={self.cache_misses}, 写入={self.cache_writes}, 错误={self.cache_errors}")

            return stats

        except Exception as e:
            self.cache_errors += 1
            error_stats = {"status": "error", "message": str(e)}
            self.logger.error(f"[PROCESS] 获取缓存统计失败: {e}")
            import traceback
            self.logger.error(f"[PROCESS] 统计错误详情: {traceback.format_exc()}")
            return error_stats
    
    def _calculate_hit_rate(self, info: Dict) -> float:
        """计算缓存命中率"""
        hits = info.get('keyspace_hits', 0)
        misses = info.get('keyspace_misses', 0)
        total = hits + misses
        hit_rate = (hits / total * 100) if total > 0 else 0.0

        self.logger.debug(f"[PROCESS] 命中率计算: 命中={hits}, 未命中={misses}, 总计={total}, 命中率={hit_rate:.2f}%")

        return hit_rate

    def log_performance_report(self):
        """输出缓存性能报告"""
        try:
            self.logger.info("[PROCESS] === Redis缓存性能报告 ===")

            if not self.cache_enabled:
                self.logger.info("[PROCESS] 缓存状态: 禁用")
                return

            # 获取统计信息
            stats = self.get_cache_stats()

            if stats["status"] == "active":
                redis_stats = stats["redis_stats"]
                local_stats = stats["local_stats"]

                self.logger.info("[PROCESS] Redis服务器状态:")
                self.logger.info(f"[PROCESS]   内存使用: {redis_stats['used_memory']}")
                self.logger.info(f"[PROCESS]   连接客户端: {redis_stats['connected_clients']}")
                self.logger.info(f"[PROCESS]   总命令数: {redis_stats['total_commands_processed']}")
                self.logger.info(f"[PROCESS]   服务器命中率: {redis_stats['hit_rate']:.1f}%")

                self.logger.info("[PROCESS] 本地应用统计:")
                self.logger.info(f"[PROCESS]   缓存命中: {local_stats['cache_hits']}")
                self.logger.info(f"[PROCESS]   缓存未命中: {local_stats['cache_misses']}")
                self.logger.info(f"[PROCESS]   缓存写入: {local_stats['cache_writes']}")
                self.logger.info(f"[PROCESS]   缓存错误: {local_stats['cache_errors']}")
                self.logger.info(f"[PROCESS]   本地命中率: {local_stats['hit_rate']:.1f}%")
                self.logger.info(f"[PROCESS]   总操作数: {local_stats['total_operations']}")

                # 性能评估
                if local_stats['hit_rate'] >= 80:
                    self.logger.info("[PROCESS] 性能评估: 优秀 (命中率 >= 80%)")
                elif local_stats['hit_rate'] >= 60:
                    self.logger.info("[PROCESS] 性能评估: 良好 (命中率 >= 60%)")
                elif local_stats['hit_rate'] >= 40:
                    self.logger.info("[PROCESS] 性能评估: 一般 (命中率 >= 40%)")
                else:
                    self.logger.warning("[PROCESS] 性能评估: 需要优化 (命中率 < 40%)")

            else:
                self.logger.error(f"[PROCESS] 缓存状态异常: {stats.get('message', '未知错误')}")

            self.logger.info("[PROCESS] === 性能报告结束 ===")

        except Exception as e:
            self.logger.error(f"[PROCESS] 生成性能报告失败: {e}")
            import traceback
            self.logger.error(f"[PROCESS] 报告错误详情: {traceback.format_exc()}")

# 全局查询缓存管理器
query_cache_manager = QueryCacheManager()
