# -*- coding: utf-8 -*-
"""
数据库管理模块
负责MySQL数据库连接、操作和管理
"""

import os
import sys
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import threading

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.utils.logger import get_logger
from src.utils.performance import monitor_performance
from src.utils.exception_handler import handle_exceptions, retry_on_exception

# 数据库相关导入
try:
    import sqlalchemy
    from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, DateTime, DECIMAL, TEXT, TIMESTAMP
    from sqlalchemy.orm import sessionmaker, Session
    from sqlalchemy.pool import QueuePool
    # 数据库异常类型根据需要导入
    import pymysql
    pymysql.install_as_MySQLdb()
except ImportError as e:
    print(f"数据库依赖包导入失败: {e}")
    raise


class DatabaseManager:
    """数据库管理器 - 管理MySQL连接和操作"""
    
    def __init__(self, config_file: str = "config/database_config.yaml"):
        self.logger = get_logger(__name__)
        self.config_file = Path(config_file)
        
        # 数据库连接相关
        self.engine = None
        self.session_factory = None
        self.metadata = None
        
        # 配置信息
        self.db_config = {}
        self.performance_config = {}
        
        # 连接状态
        self.is_connected = False
        self.connection_lock = threading.Lock()
        
        self.logger.info(f"数据库管理器初始化: {config_file}")
        
        # 加载配置并初始化连接
        self._load_database_config()
        self._initialize_database_connection()
    
    @monitor_performance("load_database_config")
    def _load_database_config(self):
        """加载数据库配置"""
        try:
            self.logger.info(f"加载数据库配置文件: {self.config_file}")
            
            if not self.config_file.exists():
                raise FileNotFoundError(f"数据库配置文件不存在: {self.config_file}")
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            self.db_config = config.get('database', {})
            self.performance_config = config.get('performance', {})
            
            # 验证必要的配置项
            required_keys = ['host', 'port', 'username', 'password', 'database']
            missing_keys = [key for key in required_keys if key not in self.db_config]
            
            if missing_keys:
                raise ValueError(f"数据库配置缺少必要项: {missing_keys}")
            
            self.logger.info("数据库配置加载成功")
            self.logger.info(f"数据库主机: {self.db_config['host']}:{self.db_config['port']}")
            self.logger.info(f"数据库名称: {self.db_config['database']}")
            
        except Exception as e:
            self.logger.error(f"数据库配置加载失败: {e}")
            raise
    
    @monitor_performance("initialize_database_connection")
    def _initialize_database_connection(self):
        """初始化数据库连接"""
        try:
            self.logger.info("初始化数据库连接")

            # 构建连接字符串
            connection_string = self._build_connection_string()

            # 创建数据库引擎
            self.logger.info("创建数据库引擎...")
            pool_size = self.db_config.get('pool_size', 20)
            max_overflow = self.db_config.get('max_overflow', 30)
            pool_timeout = self.db_config.get('pool_timeout', 30)
            pool_recycle = self.db_config.get('pool_recycle', 3600)

            self.engine = create_engine(
                connection_string,
                poolclass=QueuePool,
                pool_size=pool_size,
                max_overflow=max_overflow,
                pool_timeout=pool_timeout,
                pool_recycle=pool_recycle,
                echo=self.db_config.get('echo', False),
                echo_pool=False,
                pool_pre_ping=True,  # 连接前检查连接有效性
                connect_args={
                    'charset': self.db_config.get('charset', 'utf8mb4'),
                    'autocommit': False
                }
            )

            self.logger.info("数据库引擎配置:")
            self.logger.info(f"   连接池大小: {pool_size}")
            self.logger.info(f"   最大溢出: {max_overflow}")
            self.logger.info(f"   连接超时: {pool_timeout}秒")
            self.logger.info(f"   连接回收: {pool_recycle}秒")

            # 创建会话工厂
            self.session_factory = sessionmaker(bind=self.engine)
            self.logger.debug("[LIST] 会话工厂已创建")

            # 创建元数据对象
            self.metadata = MetaData()
            self.logger.debug("[META] 元数据对象已创建")

            # 测试连接
            self._test_connection()

            self.is_connected = True
            self.logger.info("数据库连接初始化成功")
            
        except Exception as e:
            self.logger.error(f"数据库连接初始化失败: {e}")
            self.is_connected = False
            raise
    
    def _build_connection_string(self) -> str:
        """构建数据库连接字符串"""
        connection_string = (
            f"mysql+pymysql://"
            f"{self.db_config['username']}:{self.db_config['password']}"
            f"@{self.db_config['host']}:{self.db_config['port']}"
            f"/{self.db_config['database']}"
            f"?charset={self.db_config.get('charset', 'utf8mb4')}"
        )
        
        # 不在日志中记录密码
        safe_connection_string = connection_string.replace(
            f":{self.db_config['password']}@", ":****@"
        )
        self.logger.debug(f"数据库连接字符串: {safe_connection_string}")
        
        return connection_string
    
    @handle_exceptions(context={'operation': 'test_database_connection'})
    @retry_on_exception(max_attempts=3, delay=2.0)
    @monitor_performance("test_connection")
    def _test_connection(self):
        """测试数据库连接"""
        try:
            self.logger.info("测试数据库连接")
            
            with self.engine.connect() as connection:
                # 执行简单查询测试连接
                result = connection.execute(text("SELECT 1 as test"))
                test_value = result.fetchone()[0]
                
                if test_value != 1:
                    raise Exception("数据库连接测试失败")
                
                # 获取数据库版本信息
                version_result = connection.execute(text("SELECT VERSION() as version"))
                db_version = version_result.fetchone()[0]
                
                self.logger.info(f"数据库连接测试成功")
                self.logger.info(f"MySQL版本: {db_version}")
                
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {e}")
            raise
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        if not self.is_connected:
            raise Exception("数据库未连接")
        
        return self.session_factory()
    
    @monitor_performance("execute_sql")
    def execute_sql(self, sql: str, params: Optional[Union[List, Dict]] = None) -> Any:
        """执行SQL语句"""
        if not self.is_connected:
            raise Exception("数据库未连接")

        try:
            with self.engine.connect() as connection:
                if params:
                    # SQLAlchemy 2.0 参数传递方式
                    if isinstance(params, list):
                        # 将列表转换为字典格式
                        param_dict = {f'param_{i}': param for i, param in enumerate(params)}
                        # 修改SQL中的占位符
                        modified_sql = sql
                        for i in range(len(params)):
                            modified_sql = modified_sql.replace('%s', f':param_{i}', 1)
                        result = connection.execute(text(modified_sql), param_dict)
                    else:
                        result = connection.execute(text(sql), params)
                else:
                    result = connection.execute(text(sql))

                connection.commit()

                # 只在DEBUG级别记录SQL执行详情
                if self.logger.isEnabledFor(logging.DEBUG):
                    self.logger.debug(f"SQL执行成功: {sql[:100]}...")
                return result

        except Exception as e:
            self.logger.error(f"SQL执行失败: {e}")
            self.logger.error(f"SQL语句: {sql}")
            if params:
                self.logger.error(f"参数: {params}")
            raise

    def _group_records_by_month(self, records: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按月份分组记录，优化分区表插入性能"""
        month_groups = {}

        for record in records:
            data_time = record.get('data_time')
            if data_time:
                try:
                    # 提取年月 (YYYYMM)
                    if isinstance(data_time, str):
                        # 处理各种时间格式
                        if len(data_time) >= 7:
                            month_key = data_time[:7].replace('-', '')  # 2025-05 -> 202505
                        else:
                            month_key = 'unknown'
                    else:
                        month_key = data_time.strftime('%Y%m')

                    if month_key not in month_groups:
                        month_groups[month_key] = []
                    month_groups[month_key].append(record)

                except Exception as e:
                    # 处理时间解析错误
                    if 'unknown' not in month_groups:
                        month_groups['unknown'] = []
                    month_groups['unknown'].append(record)

        return month_groups

    def execute_batch_upsert_partitioned(self, table_name: str, records: List[Dict[str, Any]]) -> int:
        """针对分区表优化的批量UPSERT操作"""
        if not records:
            return 0

        # 检查是否为分区表
        partitioned_tables = ['main_pipe_data', 'pump_data_aligned', 'main_pipe_data_aligned', 'raw_data_temp']

        if table_name in partitioned_tables:
            # 按月份分组记录，利用分区裁剪
            partitioned_records = self._group_records_by_month(records)

            total_affected = 0
            for month_key, month_records in partitioned_records.items():
                self.logger.info(f"处理分区 {month_key}: {len(month_records)} 条记录")
                affected = self.execute_batch_upsert(table_name, month_records)
                total_affected += affected

            self.logger.info(f"分区表 {table_name} UPSERT完成: 总计 {total_affected} 行受影响")
            return total_affected
        else:
            # 使用原有UPSERT方法
            return self.execute_batch_upsert(table_name, records)

    def execute_batch_upsert(self, table_name: str, records: List[Dict[str, Any]]) -> int:
        """执行批量UPSERT操作，支持跨文件参数合并"""
        if not self.is_connected:
            raise Exception("数据库未连接")

        if not records:
            return 0

        batch_size = len(records)
        thread_name = threading.current_thread().name

        try:
            with self.engine.connect() as connection:
                trans = connection.begin()

                try:
                    if table_name == 'pump_data':
                        sql = """
                        INSERT INTO pump_data
                        (device_id, pump_name, station_id, data_time, frequency, power, kwh, power_factor,
                         voltage_a, voltage_b, voltage_c, current_a, current_b, current_c,
                         outlet_pressure, outlet_flow, head, inlet_pressure, outlet_temperature,
                         pump_status, is_normal, created_at, updated_at)
                        VALUES (:device_id, :pump_name, :station_id, :data_time,
                               :frequency, :power, :kwh, :power_factor,
                               :voltage_a, :voltage_b, :voltage_c,
                               :current_a, :current_b, :current_c,
                               :outlet_pressure, :outlet_flow, :head,
                               :inlet_pressure, :outlet_temperature,
                               :pump_status, :is_normal, :created_at, :updated_at)
                        ON DUPLICATE KEY UPDATE
                            frequency = COALESCE(VALUES(frequency), frequency),
                            power = COALESCE(VALUES(power), power),
                            kwh = COALESCE(VALUES(kwh), kwh),
                            power_factor = COALESCE(VALUES(power_factor), power_factor),
                            voltage_a = COALESCE(VALUES(voltage_a), voltage_a),
                            voltage_b = COALESCE(VALUES(voltage_b), voltage_b),
                            voltage_c = COALESCE(VALUES(voltage_c), voltage_c),
                            current_a = COALESCE(VALUES(current_a), current_a),
                            current_b = COALESCE(VALUES(current_b), current_b),
                            current_c = COALESCE(VALUES(current_c), current_c),
                            outlet_pressure = COALESCE(VALUES(outlet_pressure), outlet_pressure),
                            outlet_flow = COALESCE(VALUES(outlet_flow), outlet_flow),
                            head = COALESCE(VALUES(head), head),
                            inlet_pressure = COALESCE(VALUES(inlet_pressure), inlet_pressure),
                            outlet_temperature = COALESCE(VALUES(outlet_temperature), outlet_temperature),
                            updated_at = CURRENT_TIMESTAMP
                        """
                    elif table_name == 'main_pipe_data':
                        sql = """
                        INSERT INTO main_pipe_data
                        (device_id, main_pipe_name, station_id, data_time, pressure, flow_rate, cumulative_flow,
                         reserved_decimal_1, reserved_decimal_2, reserved_decimal_3, reserved_decimal_4,
                         reserved_decimal_5, reserved_decimal_6, reserved_decimal_7, normal, created_at, updated_at)
                        VALUES (:device_id, :main_pipe_name, :station_id, :data_time,
                               :pressure, :flow_rate, :cumulative_flow,
                               :reserved_decimal_1, :reserved_decimal_2, :reserved_decimal_3, :reserved_decimal_4,
                               :reserved_decimal_5, :reserved_decimal_6, :reserved_decimal_7,
                               :normal, :created_at, :updated_at)
                        ON DUPLICATE KEY UPDATE
                            pressure = COALESCE(VALUES(pressure), pressure),
                            flow_rate = COALESCE(VALUES(flow_rate), flow_rate),
                            cumulative_flow = COALESCE(VALUES(cumulative_flow), cumulative_flow),
                            reserved_decimal_1 = COALESCE(VALUES(reserved_decimal_1), reserved_decimal_1),
                            reserved_decimal_2 = COALESCE(VALUES(reserved_decimal_2), reserved_decimal_2),
                            reserved_decimal_3 = COALESCE(VALUES(reserved_decimal_3), reserved_decimal_3),
                            reserved_decimal_4 = COALESCE(VALUES(reserved_decimal_4), reserved_decimal_4),
                            reserved_decimal_5 = COALESCE(VALUES(reserved_decimal_5), reserved_decimal_5),
                            reserved_decimal_6 = COALESCE(VALUES(reserved_decimal_6), reserved_decimal_6),
                            reserved_decimal_7 = COALESCE(VALUES(reserved_decimal_7), reserved_decimal_7),
                            updated_at = CURRENT_TIMESTAMP
                        """
                    else:
                        raise ValueError(f"不支持的表名: {table_name}")

                    # 执行批量UPSERT
                    result = connection.execute(text(sql), records)
                    trans.commit()

                    affected_rows = result.rowcount
                    self.logger.info(f"线程 {thread_name} UPSERT成功: {table_name} 表，{batch_size} 条记录，影响 {affected_rows} 行")

                    return affected_rows

                except Exception as e:
                    trans.rollback()
                    self.logger.error(f"线程 {thread_name} UPSERT失败: {table_name} 表，{batch_size} 条记录")
                    self.logger.error(f"错误详情: {e}")
                    raise

        except Exception as e:
            self.logger.error(f"线程 {thread_name} UPSERT操作异常: {e}")
            raise

    def execute_batch_insert(self, sql: str, batch_params: List[tuple]) -> int:
        """执行高性能批量插入，优化事务和连接管理"""
        if not self.is_connected:
            raise Exception("数据库未连接")

        batch_size = len(batch_params)
        thread_name = threading.current_thread().name

        try:
            # 使用连接池获取连接，启用事务优化
            with self.engine.connect() as connection:
                # 开始事务
                trans = connection.begin()

                try:
                    # SQLAlchemy 2.0兼容的批量插入方法
                    # 直接使用原始SQL和参数，不进行复杂转换

                    # 验证参数格式
                    if not batch_params or not isinstance(batch_params[0], (tuple, list)):
                        raise ValueError("批量参数格式错误：期望tuple或list的列表")

                    # 使用cursor进行批量插入（更兼容的方式）
                    raw_connection = connection.connection
                    cursor = raw_connection.cursor()

                    try:
                        # 执行批量插入
                        cursor.executemany(sql, batch_params)

                        # 获取影响的行数
                        affected_rows = cursor.rowcount

                        # 验证插入结果
                        if affected_rows != batch_size:
                            self.logger.warning(f"[WARN] 插入行数不匹配: 期望{batch_size}, 实际{affected_rows}")

                        # 提交事务
                        trans.commit()

                        # 记录成功日志
                        self.logger.debug(f"[FAST] 线程 {thread_name} 高性能批量插入成功: {affected_rows} 条记录")
                        return affected_rows

                    finally:
                        cursor.close()

                except Exception as e:
                    # 回滚事务
                    trans.rollback()
                    raise e

        except Exception as e:
            self.logger.error(f"[ERROR] 线程 {thread_name} 批量插入失败: {e}")
            self.logger.error(f"SQL: {sql[:200]}...")
            self.logger.error(f"批次大小: {batch_size}")
            raise
    
    @handle_exceptions(context={'operation': 'execute_database_query'})
    @retry_on_exception(max_attempts=2, delay=1.0)
    @monitor_performance("execute_query")
    def execute_query(self, sql: str, params: Optional[Union[List, Dict]] = None) -> List[Dict]:
        """执行查询并返回结果"""
        if not self.is_connected:
            raise Exception("数据库未连接")

        # 获取当前线程信息
        thread_name = threading.current_thread().name
        query_type = sql.strip().split()[0].upper()

        self.logger.debug(f"[DEBUG] 线程 {thread_name} 开始执行 {query_type} 查询: {sql[:50]}...")

        try:
            # 检查是否是需要事务的语句
            transaction_statements = ['INSERT', 'UPDATE', 'DELETE']
            needs_transaction = query_type in transaction_statements

            if needs_transaction:
                # 使用事务模式
                self.logger.debug(f"[PROCESS] 线程 {thread_name} 使用事务模式执行 {query_type}")
                with self.engine.begin() as connection:
                    start_time = datetime.now()

                    if params:
                        param_count = len(params) if isinstance(params, (list, dict)) else 0
                        self.logger.debug(f"[LIST] 查询参数: {param_count} 个参数")

                        # SQLAlchemy 2.0 参数传递方式
                        if isinstance(params, list):
                            # 将列表转换为字典格式
                            param_dict = {f'param_{i}': param for i, param in enumerate(params)}
                            # 修改SQL中的占位符
                            modified_sql = sql
                            for i in range(len(params)):
                                modified_sql = modified_sql.replace('%s', f':param_{i}', 1)
                            result = connection.execute(text(modified_sql), param_dict)
                        else:
                            result = connection.execute(text(sql), params)
                    else:
                        result = connection.execute(text(sql))

                    end_time = datetime.now()
                    execution_time = (end_time - start_time).total_seconds()
                    self.logger.info(f"[PROCESS] 线程 {thread_name} {query_type} 事务执行成功并已提交, 耗时 {execution_time:.3f}秒")
                    return []
            else:
                # 使用普通连接模式（SELECT等查询）
                self.logger.debug(f"[DEBUG] 线程 {thread_name} 使用查询模式执行 {query_type}")
                with self.engine.connect() as connection:
                    start_time = datetime.now()

                    if params:
                        param_count = len(params) if isinstance(params, (list, dict)) else 0
                        self.logger.debug(f"[LIST] 查询参数: {param_count} 个参数")

                        # SQLAlchemy 2.0 参数传递方式
                        if isinstance(params, list):
                            # 将列表转换为字典格式
                            param_dict = {f'param_{i}': param for i, param in enumerate(params)}
                            # 修改SQL中的占位符
                            modified_sql = sql
                            for i in range(len(params)):
                                modified_sql = modified_sql.replace('%s', f':param_{i}', 1)
                            result = connection.execute(text(modified_sql), param_dict)
                        else:
                            result = connection.execute(text(sql), params)
                    else:
                        result = connection.execute(text(sql))

                    # 检查是否是DDL语句（不返回结果集）
                    ddl_statements = ['CREATE', 'DROP', 'ALTER', 'TRUNCATE']
                    if query_type in ddl_statements:
                        end_time = datetime.now()
                        execution_time = (end_time - start_time).total_seconds()
                        self.logger.info(f"线程 {thread_name} {query_type} 语句执行成功, 耗时 {execution_time:.3f}秒")
                        return []

                    # 对于SELECT等查询语句，获取列名和数据
                    try:
                        columns = result.keys()

                        # 转换为字典列表
                        rows = []
                        for row in result:
                            row_dict = {}
                            for i, column in enumerate(columns):
                                row_dict[column] = row[i]
                            rows.append(row_dict)

                        end_time = datetime.now()
                        execution_time = (end_time - start_time).total_seconds()

                        self.logger.info(f"线程 {thread_name} {query_type} 查询成功: 返回 {len(rows)} 行, 耗时 {execution_time:.3f}秒")
                        return rows
                    except Exception as e:
                        # 如果获取结果集失败，可能是DDL语句
                        end_time = datetime.now()
                        execution_time = (end_time - start_time).total_seconds()
                        self.logger.info(f"线程 {thread_name} {query_type} 语句执行成功, 耗时 {execution_time:.3f}秒")
                        return []

        except Exception as e:
            self.logger.error(f"[ERROR] 线程 {thread_name} {query_type} 查询失败: {sql[:50]}..., 错误: {e}")
            self.logger.error(f"[DEBUG] 完整SQL语句: {sql}")
            if params:
                self.logger.error(f"[DEBUG] 查询参数: {params}")
            raise
    
    @monitor_performance("check_table_exists")
    def check_table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            sql = """
            SELECT COUNT(*) as count
            FROM information_schema.tables 
            WHERE table_schema = %s AND table_name = %s
            """
            
            result = self.execute_query(sql, [self.db_config['database'], table_name])
            exists = result[0]['count'] > 0
            
            self.logger.debug(f"表 {table_name} 存在性检查: {exists}")
            return exists
            
        except Exception as e:
            self.logger.error(f"检查表存在性失败: {e}")
            return False
    
    @monitor_performance("get_table_info")
    def get_table_info(self, table_name: str) -> Dict[str, Any]:
        """获取表信息"""
        try:
            # 获取表结构
            columns_sql = """
            SELECT 
                COLUMN_NAME as column_name,
                DATA_TYPE as data_type,
                IS_NULLABLE as is_nullable,
                COLUMN_DEFAULT as column_default,
                COLUMN_COMMENT as column_comment
            FROM information_schema.columns 
            WHERE table_schema = %s AND table_name = %s
            ORDER BY ORDINAL_POSITION
            """
            
            columns = self.execute_query(columns_sql, [self.db_config['database'], table_name])
            
            # 获取表统计信息
            stats_sql = f"SELECT COUNT(*) as row_count FROM `{table_name}`"
            stats = self.execute_query(stats_sql)
            
            table_info = {
                'table_name': table_name,
                'columns': columns,
                'row_count': stats[0]['row_count'] if stats else 0,
                'exists': len(columns) > 0
            }
            
            self.logger.debug(f"获取表信息: {table_name}, 列数: {len(columns)}, 行数: {table_info['row_count']}")
            return table_info
            
        except Exception as e:
            self.logger.error(f"获取表信息失败: {e}")
            return {'table_name': table_name, 'exists': False, 'error': str(e)}
    
    def get_connection_status(self) -> Dict[str, Any]:
        """获取连接状态信息"""
        status = {
            'is_connected': self.is_connected,
            'engine_info': str(self.engine) if self.engine else None,
            'pool_status': None
        }
        
        if self.engine and self.is_connected:
            try:
                pool = self.engine.pool
                status['pool_status'] = {
                    'size': pool.size(),
                    'checked_in': pool.checkedin(),
                    'checked_out': pool.checkedout(),
                    'overflow': pool.overflow(),
                    'invalid': pool.invalid()
                }
            except Exception as e:
                status['pool_error'] = str(e)
        
        return status

    def get_connection(self):
        """获取数据库连接（用于多线程环境）"""
        import threading
        thread_name = threading.current_thread().name

        try:
            if not self.engine:
                raise Exception("数据库引擎未初始化")

            # 记录连接池状态（获取连接前）
            pool = self.engine.pool
            active_before = pool.checkedout()
            idle_before = pool.checkedin()
            total_before = pool.size()

            self.logger.debug(f"[POOL] [{thread_name}] 请求数据库连接")
            self.logger.debug(f"[POOL] [{thread_name}] 连接池状态(获取前): 活跃={active_before}, 空闲={idle_before}, 总计={total_before}")

            # 获取连接
            connection = self.engine.connect()

            # 记录连接池状态（获取连接后）
            active_after = pool.checkedout()
            idle_after = pool.checkedin()
            total_after = pool.size()

            self.logger.debug(f"[POOL] [{thread_name}] 数据库连接获取成功")
            self.logger.debug(f"[POOL] [{thread_name}] 连接池状态(获取后): 活跃={active_after}, 空闲={idle_after}, 总计={total_after}")

            return connection

        except Exception as e:
            self.logger.error(f"[POOL] [{thread_name}] 获取数据库连接失败: {e}")
            # 记录失败时的连接池状态
            try:
                pool = self.engine.pool
                active = pool.checkedout()
                idle = pool.checkedin()
                total = pool.size()
                overflow = pool.overflow()
                self.logger.error(f"[POOL] [{thread_name}] 失败时连接池状态: 活跃={active}, 空闲={idle}, 总计={total}, 溢出={overflow}")
            except:
                pass
            raise

    def close_connection(self):
        """关闭数据库连接"""
        import threading
        thread_name = threading.current_thread().name

        try:
            if self.engine:
                # 记录关闭前的连接池状态
                pool = self.engine.pool
                active_before = pool.checkedout()
                idle_before = pool.checkedin()
                total_before = pool.size()
                overflow_before = pool.overflow()

                self.logger.info(f"[POOL] [{thread_name}] 开始关闭数据库连接池")
                self.logger.info(f"[POOL] [{thread_name}] 关闭前连接池状态: 活跃={active_before}, 空闲={idle_before}, 总计={total_before}, 溢出={overflow_before}")

                # 关闭连接池
                self.engine.dispose()

                self.logger.info(f"[POOL] [{thread_name}] 数据库连接池已关闭")
                self.logger.info(f"[POOL] [{thread_name}] 所有连接已释放，连接池已销毁")
            else:
                self.logger.debug(f"[POOL] [{thread_name}] 数据库引擎未初始化，无需关闭")

            self.is_connected = False

        except Exception as e:
            self.logger.error(f"[POOL] [{thread_name}] 关闭数据库连接失败: {e}")
            import traceback
            self.logger.error(f"[POOL] [{thread_name}] 关闭错误详情: {traceback.format_exc()}")

    def cleanup_resources(self):
        """清理所有数据库资源"""
        import threading
        thread_name = threading.current_thread().name

        # 确保logger存在
        if not hasattr(self, 'logger') or self.logger is None:
            from src.utils.logger import get_logger
            self.logger = get_logger(self.__class__.__name__)

        self.logger.info(f"[CLEANUP] [{thread_name}] 开始清理数据库资源")

        try:
            # 1. 关闭所有活跃连接
            if self.engine:
                pool = self.engine.pool
                active_connections = pool.checkedout()

                if active_connections > 0:
                    self.logger.warning(f"[CLEANUP] [{thread_name}] 发现 {active_connections} 个活跃连接，强制关闭")

                    # 强制关闭所有连接
                    pool.dispose()
                    self.logger.info(f"[CLEANUP] [{thread_name}] 强制关闭完成")

            # 2. 关闭数据库连接池
            self.close_connection()

            # 3. 清理引擎和会话工厂
            self.engine = None
            self.session_factory = None
            self.metadata = None

            # 4. 重置状态
            self.is_connected = False

            self.logger.info(f"[CLEANUP] [{thread_name}] 数据库资源清理完成")

        except Exception as e:
            self.logger.error(f"[CLEANUP] [{thread_name}] 数据库资源清理失败: {e}")
            import traceback
            self.logger.error(f"[CLEANUP] [{thread_name}] 清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        self.cleanup_resources()

        if exc_type is not None:
            self.logger.error(f"数据库管理器退出时发生异常: {exc_type.__name__}: {exc_val}")

        return False  # 不抑制异常
    
    def get_connection_pool_status(self) -> dict:
        """获取连接池状态信息"""
        import threading
        thread_name = threading.current_thread().name

        try:
            if not self.engine:
                return {"error": "数据库引擎未初始化"}

            pool = self.engine.pool
            active_connections = pool.checkedout()
            idle_connections = pool.checkedin()
            total_connections = pool.size()
            overflow_connections = pool.overflow()
            max_connections = pool._pool.maxsize if hasattr(pool, '_pool') else 0

            # 计算使用率
            usage_rate = (active_connections / total_connections) if total_connections > 0 else 0.0

            status = {
                "active_connections": active_connections,
                "idle_connections": idle_connections,
                "total_connections": total_connections,
                "overflow_connections": overflow_connections,
                "max_connections": max_connections,
                "usage_rate": usage_rate,
                "pool_size": self.db_config.get('pool_size', 20),
                "max_overflow": self.db_config.get('max_overflow', 30)
            }

            self.logger.debug(f"[POOL] [{thread_name}] 连接池状态查询: {status}")
            return status

        except Exception as e:
            self.logger.error(f"[POOL] [{thread_name}] 获取连接池状态失败: {e}")
            return {"error": str(e)}

    def log_connection_pool_status(self):
        """记录连接池状态到日志"""
        import threading
        thread_name = threading.current_thread().name

        status = self.get_connection_pool_status()
        if "error" not in status:
            self.logger.info(f"[POOL] [{thread_name}] === 连接池状态报告 ===")
            self.logger.info(f"[POOL] [{thread_name}] 活跃连接: {status['active_connections']}")
            self.logger.info(f"[POOL] [{thread_name}] 空闲连接: {status['idle_connections']}")
            self.logger.info(f"[POOL] [{thread_name}] 总连接数: {status['total_connections']}")
            self.logger.info(f"[POOL] [{thread_name}] 溢出连接: {status['overflow_connections']}")
            self.logger.info(f"[POOL] [{thread_name}] 使用率: {status['usage_rate']:.1%}")
            self.logger.info(f"[POOL] [{thread_name}] 配置: 池大小={status['pool_size']}, 最大溢出={status['max_overflow']}")
        else:
            self.logger.error(f"[POOL] [{thread_name}] 连接池状态查询失败: {status['error']}")

    def __del__(self):
        """析构函数，确保连接被关闭"""
        self.close_connection()


if __name__ == "__main__":
    # 测试数据库管理器
    test_logger = get_logger("database_test")
    
    test_logger.info("数据库管理器测试开始")
    
    try:
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 测试连接状态
        status = db_manager.get_connection_status()
        test_logger.info(f"连接状态: {status}")
        
        # 测试简单查询
        result = db_manager.execute_query("SELECT NOW() as current_datetime, VERSION() as db_version")
        test_logger.info(f"查询结果: {result}")
        
        # 测试表存在性检查
        tables_to_check = ['pump_stations', 'pumps', 'main_pipe_data', 'pump_data']
        for table_name in tables_to_check:
            exists = db_manager.check_table_exists(table_name)
            test_logger.info(f"表 {table_name} 存在: {exists}")
            
            if exists:
                table_info = db_manager.get_table_info(table_name)
                test_logger.info(f"表 {table_name} 信息: 列数={len(table_info.get('columns', []))}, 行数={table_info.get('row_count', 0)}")
        
        test_logger.info("数据库管理器测试完成")
        
    except Exception as e:
        test_logger.error(f"数据库管理器测试失败: {e}")
        # 不抛出异常，因为可能是数据库还未创建
        test_logger.warning("这可能是因为数据库还未创建，这是正常的")
