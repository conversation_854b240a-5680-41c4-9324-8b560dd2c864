# -*- coding: utf-8 -*-
"""
连接池监控系统 - 防止连接泄漏和过载
"""

import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from src.utils.logger import get_logger

@dataclass
class ConnectionPoolStats:
    """连接池统计信息"""
    active_connections: int
    idle_connections: int
    total_connections: int
    overflow_connections: int
    usage_rate: float
    timestamp: datetime

class ConnectionPoolMonitor:
    """连接池监控器"""
    
    def __init__(self, db_manager, alert_threshold: float = 0.8):
        self.db_manager = db_manager
        self.alert_threshold = alert_threshold
        self.logger = get_logger(__name__)
        
        # 监控数据
        self.stats_history: List[ConnectionPoolStats] = []
        self.max_history_size = 1440  # 24小时的分钟数
        
        # 告警状态
        self.last_alert_time = None
        self.alert_cooldown = 300  # 5分钟告警冷却
        
        # 监控线程
        self.monitoring_thread = None
        self.is_monitoring = False
        self.monitor_interval = 60  # 1分钟监控间隔
        
    def start_monitoring(self):
        """启动连接池监控"""
        if self.is_monitoring:
            self.logger.warning("[POOL] 连接池监控已在运行中，跳过启动")
            return

        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            name="ConnectionPoolMonitor",
            daemon=True
        )
        self.monitoring_thread.start()

        self.logger.info("[POOL] 连接池监控已启动")
        self.logger.info(f"[POOL] 监控线程: {self.monitoring_thread.name} (ID: {self.monitoring_thread.ident})")
        self.logger.info(f"[POOL] 监控配置: 间隔={self.monitor_interval}秒, 告警阈值={self.alert_threshold:.1%}")
        self.logger.info(f"[POOL] 历史数据: 最大保存={self.max_history_size}条, 告警冷却={self.alert_cooldown}秒")
    
    def stop_monitoring(self):
        """停止连接池监控"""
        if not self.is_monitoring:
            self.logger.debug("[POOL] 连接池监控未运行，无需停止")
            return

        self.logger.info("[POOL] 正在停止连接池监控...")
        self.is_monitoring = False

        if self.monitoring_thread:
            thread_name = self.monitoring_thread.name
            self.logger.info(f"[POOL] 等待监控线程 {thread_name} 结束...")
            self.monitoring_thread.join(timeout=5)

            if self.monitoring_thread.is_alive():
                self.logger.warning(f"[POOL] 监控线程 {thread_name} 未能在5秒内结束")
            else:
                self.logger.info(f"[POOL] 监控线程 {thread_name} 已正常结束")

        # 记录监控统计
        total_stats = len(self.stats_history)
        self.logger.info(f"[POOL] 连接池监控已停止，共收集了 {total_stats} 条统计数据")

        if total_stats > 0:
            last_stats = self.stats_history[-1]
            self.logger.info(f"[POOL] 最后记录的连接池状态: 活跃={last_stats.active_connections}, "
                           f"空闲={last_stats.idle_connections}, 使用率={last_stats.usage_rate:.1%}")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                stats = self.collect_pool_stats()
                if stats:
                    self.stats_history.append(stats)
                    
                    # 限制历史数据大小
                    if len(self.stats_history) > self.max_history_size:
                        self.stats_history.pop(0)
                    
                    # 检查告警条件
                    self.check_alerts(stats)
                    
                    # 记录统计信息
                    self.logger.debug(f"[POOL] 连接池状态: 活跃={stats.active_connections}, "
                                    f"空闲={stats.idle_connections}, "
                                    f"使用率={stats.usage_rate:.1%}")

                    # 每10次监控记录一次详细信息
                    if len(self.stats_history) % 10 == 0:
                        self.logger.info(f"[POOL] 连接池监控报告 (第{len(self.stats_history)}次):")
                        self.logger.info(f"[POOL]   活跃连接: {stats.active_connections}")
                        self.logger.info(f"[POOL]   空闲连接: {stats.idle_connections}")
                        self.logger.info(f"[POOL]   总连接数: {stats.total_connections}")
                        self.logger.info(f"[POOL]   溢出连接: {stats.overflow_connections}")
                        self.logger.info(f"[POOL]   使用率: {stats.usage_rate:.1%}")

            except Exception as e:
                self.logger.error(f"[POOL] 连接池监控异常: {e}")
                import traceback
                self.logger.error(f"[POOL] 监控异常详情: {traceback.format_exc()}")

            time.sleep(self.monitor_interval)
    
    def collect_pool_stats(self) -> Optional[ConnectionPoolStats]:
        """收集连接池统计信息"""
        try:
            if not self.db_manager.engine:
                self.logger.warning("[POOL] 数据库引擎未初始化，无法收集连接池统计")
                return None

            engine = self.db_manager.engine
            pool = engine.pool

            self.logger.debug("[POOL] 开始收集连接池统计信息...")

            # 获取连接池状态
            active_connections = pool.checkedout()
            idle_connections = pool.checkedin()
            total_connections = pool.size()
            overflow_connections = pool.overflow()

            self.logger.debug(f"[POOL] 原始统计数据: 活跃={active_connections}, 空闲={idle_connections}, "
                            f"总计={total_connections}, 溢出={overflow_connections}")

            # 计算使用率
            if total_connections > 0:
                usage_rate = active_connections / total_connections
            else:
                usage_rate = 0.0

            stats = ConnectionPoolStats(
                active_connections=active_connections,
                idle_connections=idle_connections,
                total_connections=total_connections,
                overflow_connections=overflow_connections,
                usage_rate=usage_rate,
                timestamp=datetime.now()
            )

            self.logger.debug(f"[POOL] 连接池统计收集完成，使用率: {usage_rate:.1%}")
            return stats

        except Exception as e:
            self.logger.error(f"[POOL] 收集连接池统计失败: {e}")
            import traceback
            self.logger.error(f"[POOL] 收集统计错误详情: {traceback.format_exc()}")
            return None
    
    def check_alerts(self, stats: ConnectionPoolStats):
        """检查告警条件"""
        current_time = datetime.now()
        
        # 检查告警冷却时间
        if (self.last_alert_time and 
            (current_time - self.last_alert_time).total_seconds() < self.alert_cooldown):
            return
        
        # 使用率告警
        if stats.usage_rate > self.alert_threshold:
            self.logger.warning(f"连接池使用率告警: {stats.usage_rate:.1%} "
                              f"(阈值: {self.alert_threshold:.1%})")
            self.last_alert_time = current_time

        # 溢出连接告警
        if stats.overflow_connections > 0:
            self.logger.warning(f"连接池溢出告警: {stats.overflow_connections} 个溢出连接")
            self.last_alert_time = current_time
        
        # 连接泄漏检测
        self.detect_connection_leaks(stats)
    
    def detect_connection_leaks(self, current_stats: ConnectionPoolStats):
        """检测连接泄漏"""
        if len(self.stats_history) < 10:  # 需要足够的历史数据
            return
        
        # 检查最近10分钟的连接使用趋势
        recent_stats = self.stats_history[-10:]
        active_connections = [s.active_connections for s in recent_stats]
        
        # 如果活跃连接数持续增长且没有下降，可能存在泄漏
        if len(set(active_connections)) == 1 and active_connections[0] > 0:
            # 连接数保持不变且大于0，检查是否长时间无变化
            if (current_stats.timestamp - recent_stats[0].timestamp).total_seconds() > 600:
                self.logger.warning(f"疑似连接泄漏: 活跃连接数 {current_stats.active_connections} "
                                  f"保持不变超过10分钟")
    
    def get_pool_health_report(self) -> Dict[str, Any]:
        """获取连接池健康报告"""
        if not self.stats_history:
            return {"status": "no_data", "message": "暂无监控数据"}
        
        latest_stats = self.stats_history[-1]
        
        # 计算平均使用率
        if len(self.stats_history) >= 60:  # 最近1小时
            recent_usage_rates = [s.usage_rate for s in self.stats_history[-60:]]
            avg_usage_rate = sum(recent_usage_rates) / len(recent_usage_rates)
        else:
            avg_usage_rate = latest_stats.usage_rate
        
        # 健康状态评估
        if latest_stats.usage_rate > 0.9:
            health_status = "critical"
        elif latest_stats.usage_rate > 0.8:
            health_status = "warning"
        elif latest_stats.usage_rate > 0.6:
            health_status = "normal"
        else:
            health_status = "healthy"
        
        return {
            "status": health_status,
            "current_usage_rate": latest_stats.usage_rate,
            "average_usage_rate": avg_usage_rate,
            "active_connections": latest_stats.active_connections,
            "total_connections": latest_stats.total_connections,
            "overflow_connections": latest_stats.overflow_connections,
            "last_update": latest_stats.timestamp.isoformat()
        }
