# pump_data表重建记录

**操作时间**: 2025-08-01  
**操作原因**: 用户误删了pump_data表，需要重新创建  
**操作人员**: AI助手  

## 📋 问题描述

用户报告：
> "数据库中的表pump_data被我不小心删除了，重新创建。"

## 🔍 分析过程

### 1. 信息收集
- 查看了项目中的个人记录文档（`我的个人记录.md`）
- 分析了CSV映射配置文件（`config/data_mapping.json`）
- 检查了代码中的表结构定义和使用情况
- 查看了之前生成的数据库分析报告

### 2. 表结构分析
根据分析，pump_data表应该包含以下字段：

#### 基础字段
- `id`: 主键，自增ID
- `device_id`: 设备ID（1-12，对应11台泵）
- `station_id`: 泵站名称
- `data_time`: 数据采集时间戳

#### 电气参数字段（来自CSV映射）
- `frequency`: 频率（Hz）- 仅变频泵有此参数
- `power`: 有功功率（kW）
- `kwh`: 正向有功电度（kWh）
- `power_factor`: 功率因数
- `voltage_a/b/c`: 三相电压（V）
- `current_a/b/c`: 三相电流（A）

#### 状态字段
- `pump_status`: 泵状态
- `created_at`: 创建时间

## 📝 生成的文件

### 1. 完整版SQL脚本
**文件**: `cmdout/重建pump_data表.sql`
- 包含所有可能的字段（59个字段）
- 包含振动传感器、温度传感器等扩展字段
- 适用于未来功能扩展

### 2. 简化版SQL脚本
**文件**: `cmdout/重建pump_data表_简化版.sql`
- 只包含当前项目实际使用的字段（16个字段）
- 基于CSV映射文件的实际参数
- 推荐用于当前项目

### 3. 操作指南
**文件**: `cmdout/pump_data表重建操作指南.md`
- 详细的操作步骤
- 多种执行方案
- 验证和测试方法
- 注意事项和后续操作

## 🔧 推荐的表结构

```sql
CREATE TABLE pump_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    device_id INT NOT NULL,
    station_id VARCHAR(50) NOT NULL,
    data_time DATETIME NOT NULL,
    frequency DECIMAL(10,4),
    power DECIMAL(10,4),
    kwh DECIMAL(15,4),
    power_factor DECIMAL(6,4),
    voltage_a DECIMAL(10,4),
    voltage_b DECIMAL(10,4),
    voltage_c DECIMAL(10,4),
    current_a DECIMAL(10,4),
    current_b DECIMAL(10,4),
    current_c DECIMAL(10,4),
    pump_status VARCHAR(20) DEFAULT 'running',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 📊 关键设计决策

### 1. 字段选择
- **简化优先**: 只包含CSV映射中实际存在的参数
- **扩展性**: 保留了基础的电气和状态参数
- **兼容性**: 与现有代码中的字段名保持一致

### 2. 数据类型
- **DECIMAL**: 用于精确的数值计算（电压、电流、功率等）
- **VARCHAR(50)**: 泵站名称，支持中文
- **DATETIME**: 时间戳，支持秒级精度
- **BIGINT**: 主键，支持大数据量

### 3. 索引策略
- `device_id + data_time`: 主要查询组合
- `station_id + data_time`: 按泵站查询
- `data_time`: 时间范围查询

### 4. 字符集和引擎
- **utf8mb4**: 支持中文和特殊字符
- **InnoDB**: 支持事务和外键约束

## ✅ 验证要点

### 1. 表结构验证
- 字段数量和类型正确
- 索引创建成功
- 字符集设置正确

### 2. 数据插入验证
- 变频泵数据（有frequency值）
- 软启动泵数据（frequency为NULL）
- 时间戳格式正确

### 3. 关联查询验证
- 与devices表的关联
- 与pump_stations表的关联
- 数据完整性检查

## 🔄 后续建议

### 1. 立即操作
1. 执行简化版SQL脚本重建表
2. 运行验证查询确认表结构
3. 测试数据插入功能

### 2. 程序验证
1. 重新启动数据处理程序
2. 处理少量CSV文件测试
3. 检查日志确认无错误

### 3. 监控和优化
1. 监控数据插入性能
2. 根据实际使用情况调整索引
3. 考虑分区策略（大数据量时）

## 📋 相关文件清单

- `cmdout/重建pump_data表.sql` - 完整版建表脚本
- `cmdout/重建pump_data表_简化版.sql` - 简化版建表脚本（推荐）
- `cmdout/pump_data表重建操作指南.md` - 详细操作指南
- `modify/pump_data表重建记录.md` - 本记录文件

---

**操作状态**: 已完成SQL脚本生成和操作指南编写，等待用户执行重建操作。
