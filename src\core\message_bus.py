# -*- coding: utf-8 -*-
"""
消息总线模块
实现发布-订阅模式，解耦系统各组件
遵循SOLID、DRY、SRP原则
"""

import os
import sys
import uuid
import time
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import queue
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.utils.logger import get_logger
from src.utils.performance import monitor_performance
from src.utils.exception_handler import handle_exceptions


class MessageType(Enum):
    """消息类型枚举"""
    # 数据处理消息
    FILE_PROCESS_REQUEST = "file_process_request"
    FILE_PROCESS_COMPLETED = "file_process_completed"
    FILE_PROCESS_FAILED = "file_process_failed"
    
    # 数据库操作消息
    DATABASE_INSERT_REQUEST = "database_insert_request"
    DATABASE_INSERT_COMPLETED = "database_insert_completed"
    DATABASE_INSERT_FAILED = "database_insert_failed"
    
    # 系统控制消息
    SYSTEM_START = "system_start"
    SYSTEM_STOP = "system_stop"
    SYSTEM_PAUSE = "system_pause"
    SYSTEM_RESUME = "system_resume"
    
    # 状态通知消息
    PROGRESS_UPDATE = "progress_update"
    ERROR_NOTIFICATION = "error_notification"
    PERFORMANCE_METRICS = "performance_metrics"


@dataclass
class Message:
    """消息数据结构"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: MessageType = MessageType.SYSTEM_START
    payload: Dict[str, Any] = field(default_factory=dict)
    sender: str = "unknown"
    timestamp: datetime = field(default_factory=datetime.now)
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    priority: int = 0  # 0=最高优先级, 9=最低优先级
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'type': self.type.value,
            'payload': self.payload,
            'sender': self.sender,
            'timestamp': self.timestamp.isoformat(),
            'correlation_id': self.correlation_id,
            'reply_to': self.reply_to,
            'priority': self.priority
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """从字典创建消息"""
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            type=MessageType(data.get('type', 'system_start')),
            payload=data.get('payload', {}),
            sender=data.get('sender', 'unknown'),
            timestamp=datetime.fromisoformat(data.get('timestamp', datetime.now().isoformat())),
            correlation_id=data.get('correlation_id'),
            reply_to=data.get('reply_to'),
            priority=data.get('priority', 0)
        )


class MessageHandler:
    """消息处理器基类"""
    
    def __init__(self, handler_id: str):
        self.handler_id = handler_id
        self.logger = get_logger(f"MessageHandler.{handler_id}")
        self.is_active = True
        
    @monitor_performance("handle_message")
    def handle(self, message: Message) -> Optional[Message]:
        """处理消息的抽象方法"""
        self.logger.info(f"处理器 {self.handler_id} 开始处理消息: {message.type.value}")
        
        try:
            if not self.is_active:
                self.logger.warning(f"处理器 {self.handler_id} 未激活，跳过消息处理")
                return None
                
            result = self._process_message(message)
            
            self.logger.info(f"处理器 {self.handler_id} 成功处理消息: {message.type.value}")
            return result
            
        except Exception as e:
            self.logger.error(f"处理器 {self.handler_id} 处理消息失败: {e}")
            
            # 创建错误通知消息
            error_message = Message(
                type=MessageType.ERROR_NOTIFICATION,
                payload={
                    'original_message_id': message.id,
                    'handler_id': self.handler_id,
                    'error': str(e),
                    'error_type': type(e).__name__
                },
                sender=self.handler_id,
                correlation_id=message.correlation_id
            )
            
            return error_message
    
    def _process_message(self, message: Message) -> Optional[Message]:
        """子类需要实现的具体处理逻辑"""
        # 使用message参数避免警告
        self.logger.debug(f"处理消息: {message.type}")
        raise NotImplementedError("子类必须实现 _process_message 方法")
    
    def activate(self):
        """激活处理器"""
        self.is_active = True
        self.logger.info(f"处理器 {self.handler_id} 已激活")
    
    def deactivate(self):
        """停用处理器"""
        self.is_active = False
        self.logger.info(f"处理器 {self.handler_id} 已停用")


class MessageBus:
    """消息总线 - 核心解耦组件"""
    
    def __init__(self, max_queue_size: int = 10000, max_workers: int = None):
        self.logger = get_logger("MessageBus")
        
        # 消息队列 - 按优先级排序
        self.message_queue = queue.PriorityQueue(maxsize=max_queue_size)
        
        # 订阅者管理
        self.subscribers: Dict[MessageType, List[MessageHandler]] = {}
        
        # 消息历史记录
        self.message_history: List[Message] = []
        self.max_history_size = 1000
        
        # 性能统计
        self.stats = {
            'messages_sent': 0,
            'messages_processed': 0,
            'messages_failed': 0,
            'handlers_count': 0
        }
        
        # 线程控制
        self.is_running = False
        self.worker_thread = None
        self.lock = threading.RLock()

        # 线程池配置
        self.max_workers = max_workers or 4  # 默认4个工作线程
        self.executor = None
        
        self.logger.info("消息总线初始化完成")
    
    @monitor_performance("subscribe")
    def subscribe(self, message_type: MessageType, handler: MessageHandler):
        """订阅消息类型"""
        with self.lock:
            if message_type not in self.subscribers:
                self.subscribers[message_type] = []
            
            self.subscribers[message_type].append(handler)
            self.stats['handlers_count'] += 1
            
            self.logger.info(f"处理器 {handler.handler_id} 订阅消息类型: {message_type.value}")
    
    @monitor_performance("unsubscribe")
    def unsubscribe(self, message_type: MessageType, handler: MessageHandler):
        """取消订阅"""
        with self.lock:
            if message_type in self.subscribers:
                if handler in self.subscribers[message_type]:
                    self.subscribers[message_type].remove(handler)
                    self.stats['handlers_count'] -= 1

                    self.logger.info(f"处理器 {handler.handler_id} 取消订阅消息类型: {message_type.value}")

    def clear_all_subscriptions(self):
        """清理所有订阅"""
        with self.lock:
            total_handlers = self.stats['handlers_count']
            self.subscribers.clear()
            self.stats['handlers_count'] = 0

            self.logger.info(f"🧹 清理所有订阅，共移除 {total_handlers} 个处理器")

    @handle_exceptions(context={'operation': 'publish_message'})
    @monitor_performance("publish")
    def publish(self, message: Message):
        """发布消息"""
        try:
            # 将消息放入优先级队列 - 修复Message对象比较问题
            self.message_queue.put((message.priority, time.time(), message.id, message), timeout=1.0)
            
            with self.lock:
                self.stats['messages_sent'] += 1
                
                # 记录消息历史
                self.message_history.append(message)
                if len(self.message_history) > self.max_history_size:
                    self.message_history.pop(0)
            
            self.logger.info(f"[SEND] 消息已发布: {message.type.value}, ID: {message.id}, 优先级: {message.priority}")
            if hasattr(message.payload, 'get'):
                target_table = message.payload.get('target_table', 'N/A')
                self.logger.info(f"[LIST] 消息详情: target_table={target_table}, payload_keys={list(message.payload.keys()) if hasattr(message.payload, 'keys') else 'N/A'}")
            
        except queue.Full:
            self.logger.error(f"消息队列已满，无法发布消息: {message.type.value}")
            raise
        except Exception as e:
            self.logger.error(f"发布消息失败: {e}")
            raise
    
    def start(self):
        """启动消息总线"""
        if self.is_running:
            self.logger.warning("消息总线已在运行中")
            return
        
        self.is_running = True

        # 启动线程池
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers, thread_name_prefix="MessageBus")
        self.logger.info(f"[FAST] 线程池已启动: {self.max_workers}个工作线程")
        self.logger.info(f"[THREAD] 线程池配置: max_workers={self.max_workers}, thread_name_prefix='MessageBus'")

        # 启动消息分发线程
        self.worker_thread = threading.Thread(target=self._message_worker, daemon=True, name="MessageBus-Dispatcher")
        self.worker_thread.start()
        self.logger.info(f"📡 消息分发线程已启动: {self.worker_thread.name}")
        self.logger.info(f"[THREAD] 分发线程详情: thread_id={self.worker_thread.ident}, daemon={self.worker_thread.daemon}")

        self.logger.info(f"[OK] 消息总线完全启动 - 线程池: {self.max_workers}线程, 队列容量: {self.message_queue.maxsize}")
        self.logger.info(f"[THREAD] 总线线程统计: 分发线程=1, 工作线程池={self.max_workers}, 总计={self.max_workers + 1}个线程")
    
    def stop(self):
        """停止消息总线"""
        if not self.is_running:
            self.logger.warning("消息总线未在运行")
            return
        
        self.logger.info("🛑 开始停止消息总线...")
        self.is_running = False

        # 关闭线程池
        if self.executor:
            self.logger.info("⏳ 正在关闭线程池...")
            self.logger.info(f"[THREAD] 关闭前线程池状态: max_workers={self.max_workers}")
            try:
                # 尝试使用timeout参数（Python 3.9+）
                try:
                    self.executor.shutdown(wait=True, timeout=10.0)
                    self.logger.info("[THREAD] 线程池优雅关闭成功（使用timeout参数）")
                except TypeError:
                    # 如果不支持timeout参数，使用基本的shutdown
                    self.logger.debug("[THREAD] 使用基本shutdown方法（不支持timeout参数）")
                    self.executor.shutdown(wait=True)
                    self.logger.info("[THREAD] 线程池优雅关闭成功（基本方法）")
                self.logger.info("[OK] 线程池已关闭")
            except Exception as e:
                self.logger.error(f"[ERROR] 关闭线程池时发生错误: {e}")
                self.logger.warning("[THREAD] 尝试强制关闭线程池...")
                try:
                    # 强制关闭
                    self.executor.shutdown(wait=False)
                    self.logger.warning("[THREAD] 线程池强制关闭完成")
                except Exception as force_error:
                    self.logger.error(f"[THREAD] 强制关闭线程池也失败: {force_error}")
            finally:
                self.executor = None
                self.logger.info("[THREAD] 线程池对象已清理")

        # 等待消息分发线程结束
        if self.worker_thread and self.worker_thread.is_alive():
            self.logger.info("⏳ 等待消息分发线程结束...")
            self.worker_thread.join(timeout=5.0)
            if self.worker_thread.is_alive():
                self.logger.warning("[WARN] 消息分发线程未能在5秒内结束")
            else:
                self.logger.info("[OK] 消息分发线程已结束")

        # 输出最终统计
        stats = self.get_stats()
        self.logger.info(f"[DATA] 消息总线统计: 处理成功 {stats['messages_processed']}, 失败 {stats['messages_failed']}, 队列剩余 {stats['queue_size']}")
        self.logger.info("🏁 消息总线已完全停止")
    
    def _message_worker(self):
        """消息处理工作线程"""
        thread_name = threading.current_thread().name
        self.logger.info(f"[FAST] 消息处理工作线程已启动: {thread_name}")

        message_count = 0
        while self.is_running:
            try:
                # 从队列获取消息（带超时） - 修复Message对象比较问题
                priority, timestamp, message_id, message = self.message_queue.get(timeout=1.0)
                message_count += 1

                self.logger.debug(f"📨 {thread_name} 获取消息 #{message_count}: {message.type} (优先级: {priority}, 时间戳: {timestamp}, ID: {message_id})")

                start_time = datetime.now()
                self._process_message(message)
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                self.logger.info(f"[QUICK] {thread_name} 处理完成 #{message_count}: {message.type}, 耗时 {duration:.3f}秒")

                self.message_queue.task_done()

            except queue.Empty:
                # 超时是正常的，继续循环
                if message_count > 0 and message_count % 10 == 0:  # 每处理10个消息输出一次状态
                    queue_size = self.message_queue.qsize()
                    self.logger.debug(f"💤 {thread_name} 等待消息中... (已处理: {message_count}, 队列: {queue_size})")
                continue
            except Exception as e:
                self.logger.error(f"[ERROR] {thread_name} 消息处理发生错误: {e}")

        self.logger.info(f"🏁 消息处理工作线程已停止: {thread_name}, 总处理消息: {message_count}")
    
    @monitor_performance("process_message")
    def _process_message(self, message: Message):
        """处理单个消息"""
        try:
            # 获取订阅者
            handlers = self.subscribers.get(message.type, [])
            
            if not handlers:
                self.logger.warning(f"[WARN] 没有处理器订阅消息类型: {message.type.value}")
                self.logger.debug(f"[DEBUG] 当前已订阅的消息类型: {list(self.subscribers.keys())}")
                return

            self.logger.info(f"[TARGET] 开始分发消息: {message.type.value} -> {len(handlers)}个处理器")
            for handler in handlers:
                self.logger.debug(f"[LIST] 目标处理器: {handler.handler_id}")
            
            # 真正的并行处理所有订阅者
            if len(handlers) == 1:
                # 单个处理器直接处理，避免线程池开销
                handler = handlers[0]
                current_thread = threading.current_thread()
                self.logger.info(f"[PROCESS] 单线程处理消息 {message.type} -> 处理器 {handler.handler_id}")
                self.logger.debug(f"[THREAD] 处理线程: {current_thread.name} (ID: {current_thread.ident})")
                try:
                    result_message = handler.handle(message)

                    # 如果处理器返回了新消息，继续发布
                    if result_message:
                        self.logger.debug(f"[SEND] 处理器 {handler.handler_id} 返回新消息: {result_message.type}")
                        self.publish(result_message)

                    with self.lock:
                        self.stats['messages_processed'] += 1

                    self.logger.info(f"[OK] 处理器 {handler.handler_id} 成功处理消息 {message.type}")

                except Exception as e:
                    self.logger.error(f"[ERROR] 处理器 {handler.handler_id} 处理消息失败: {e}")

                    with self.lock:
                        self.stats['messages_failed'] += 1
            else:
                # 多个处理器并行处理
                current_thread = threading.current_thread()
                self.logger.info(f"[FAST] 并行处理消息 {message.type} -> {len(handlers)}个处理器")
                self.logger.debug(f"[THREAD] 分发线程: {current_thread.name} (ID: {current_thread.ident})")
                futures = []
                handler_names = []

                for handler in handlers:
                    self.logger.debug(f"[LIST] 提交任务到线程池: 处理器 {handler.handler_id}")
                    future = self.executor.submit(self._handle_message_async, handler, message)
                    futures.append((future, handler.handler_id))
                    handler_names.append(handler.handler_id)

                self.logger.info(f"[QUICK] 线程池任务已提交: {', '.join(handler_names)}")
                self.logger.debug(f"[THREAD] 已提交{len(futures)}个任务到线程池，等待执行")

                # 等待所有处理器完成
                completed_count = 0
                for future, handler_id in futures:
                    try:
                        result_message = future.result()
                        completed_count += 1

                        self.logger.info(f"[OK] 并行处理器 {handler_id} 完成 ({completed_count}/{len(futures)})")

                        # 如果处理器返回了新消息，继续发布
                        if result_message:
                            self.logger.debug(f"[SEND] 并行处理器 {handler_id} 返回新消息: {result_message.type}")
                            self.publish(result_message)

                        with self.lock:
                            self.stats['messages_processed'] += 1

                    except Exception as e:
                        self.logger.error(f"[ERROR] 并行处理器 {handler_id} 处理消息失败: {e}")

                        with self.lock:
                            self.stats['messages_failed'] += 1

                self.logger.info(f"[TARGET] 并行处理完成: {message.type}, 成功: {completed_count}/{len(futures)}")
        
        except Exception as e:
            self.logger.error(f"消息处理失败: {e}")
            
            with self.lock:
                self.stats['messages_failed'] += 1

    def _handle_message_async(self, handler: MessageHandler, message: Message):
        """异步处理单个消息处理器"""
        current_thread = threading.current_thread()
        thread_name = current_thread.name
        thread_id = current_thread.ident
        self.logger.debug(f"[THREAD] 线程 {thread_name} (ID: {thread_id}) 开始处理: 处理器 {handler.handler_id}, 消息 {message.type}")

        try:
            start_time = datetime.now()
            result = handler.handle(message)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            self.logger.info(f"[QUICK] 线程 {thread_name} (ID: {thread_id}) 完成处理: 处理器 {handler.handler_id}, 耗时 {duration:.3f}秒")
            return result

        except Exception as e:
            self.logger.error(f"[ERROR] 线程 {thread_name} (ID: {thread_id}) 处理失败: 处理器 {handler.handler_id}, 错误: {e}")
            import traceback
            self.logger.error(f"[THREAD] 线程 {thread_name} 错误详情: {traceback.format_exc()}")
            raise

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            return {
                **self.stats,
                'queue_size': self.message_queue.qsize(),
                'subscribers_count': len(self.subscribers),
                'is_running': self.is_running
            }
    
    def get_message_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取消息历史"""
        with self.lock:
            recent_messages = self.message_history[-limit:]
            return [msg.to_dict() for msg in recent_messages]

    def cleanup_resources(self):
        """清理所有消息总线资源"""
        self.logger.info("开始清理MessageBus资源")

        try:
            # 1. 停止消息总线
            if self.is_running:
                self.stop()

            # 2. 清理所有订阅
            self.clear_all_subscriptions()

            # 3. 清空消息队列
            queue_size = self.message_queue.qsize()
            if queue_size > 0:
                self.logger.info(f"清理消息队列中的 {queue_size} 条消息")
                while not self.message_queue.empty():
                    try:
                        self.message_queue.get_nowait()
                    except queue.Empty:
                        break

            # 4. 清理消息历史
            history_size = len(self.message_history)
            if history_size > 0:
                self.logger.info(f"清理消息历史中的 {history_size} 条记录")
                self.message_history.clear()

            # 5. 重置统计信息
            with self.lock:
                self.stats = {
                    'messages_sent': 0,
                    'messages_processed': 0,
                    'messages_failed': 0,
                    'handlers_count': 0
                }

            self.logger.info("MessageBus资源清理完成")

        except Exception as e:
            self.logger.error(f"MessageBus资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        self.cleanup_resources()

        if exc_type is not None:
            self.logger.error(f"MessageBus退出时发生异常: {exc_type.__name__}: {exc_val}")
            if exc_tb is not None:
                self.logger.debug("异常追踪信息已记录")

        return False  # 不抑制异常


# 全局消息总线实例
_global_message_bus = None


def get_message_bus(max_workers: int = 8) -> MessageBus:
    """获取全局消息总线实例"""
    global _global_message_bus

    if _global_message_bus is None:
        _global_message_bus = MessageBus(max_queue_size=50000, max_workers=max_workers)

    return _global_message_bus


def reset_message_bus():
    """重置全局消息总线实例 - 用于测试"""
    global _global_message_bus

    if _global_message_bus is not None:
        # 停止当前消息总线
        if _global_message_bus.is_running:
            _global_message_bus.stop()

        # 清理所有订阅
        _global_message_bus.clear_all_subscriptions()

        # 重置为None，下次调用get_message_bus时会创建新实例
        _global_message_bus = None


if __name__ == "__main__":
    # 测试消息总线
    logger = get_logger("MessageBusTest")
    
    logger.info("开始测试消息总线")
    
    # 创建消息总线
    bus = get_message_bus()
    bus.start()
    
    # 创建测试消息
    test_message = Message(
        type=MessageType.SYSTEM_START,
        payload={'test': 'data'},
        sender='test_sender'
    )
    
    # 发布消息
    bus.publish(test_message)
    
    # 等待处理
    time.sleep(1)
    
    # 获取统计信息
    stats = bus.get_stats()
    logger.info(f"消息总线统计: {stats}")
    
    # 停止消息总线
    bus.stop()
    
    logger.info("消息总线测试完成")
