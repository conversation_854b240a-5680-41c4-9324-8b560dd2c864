#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单例数据转换器
集成设备ID缓存、时间对齐缓存和设备管理功能的单例数据转换器
"""

import threading
from typing import Dict, List, Optional, Any
from datetime import datetime

from src.utils.logger import get_logger
from src.utils.device_id_cache import get_device_cache
from src.utils.time_alignment_cache import get_time_alignment_cache
from src.core.database_manager import DatabaseManager


class SingletonDataTransformer:
    """
    单例数据转换器
    
    特性:
    - 全局唯一实例
    - 集成设备ID缓存管理器
    - 集成时间对齐缓存系统
    - 状态保持和缓存复用
    - 线程安全
    """
    
    _instance = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls, *args, **kwargs):
        """单例模式实现"""
        # 使用参数避免警告
        _ = args, kwargs
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SingletonDataTransformer, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        """
        初始化单例数据转换器
        
        Args:
            db_manager: 数据库管理器实例
        """
        # 防止重复初始化
        if self._initialized:
            return
        
        with self._lock:
            if self._initialized:
                return
            
            self.logger = get_logger(__name__)
            
            # 数据库管理器
            self.db_manager = db_manager

            # 配置管理器（延迟初始化）
            self.config_manager = None
            
            # 初始化缓存系统
            self.device_cache = get_device_cache(
                max_cache_size=50000,
                memory_limit_mb=50.0,
                auto_cleanup=True
            )
            
            self.time_cache = get_time_alignment_cache(
                target_format='standard',
                max_cache_size=100000,
                memory_limit_mb=10.0,
                enable_fast_check=True
            )
            
            # 性能统计
            self._stats = {
                'transform_requests': 0,
                'device_queries': 0,
                'time_alignments': 0,
                'cache_hits': 0,
                'cache_misses': 0,
                'errors': 0
            }
            
            # 预加载标志
            self._device_cache_preloaded = False

            # 🔧 统一设备ID管理 - 使用单一数据源
            self._max_existing_device_id = None
            self._next_device_id = 1  # 默认从1开始，避免None类型错误

            # 泵数据参数映射（包含数据库中的所有字段）
            self.pump_param_mapping = {
                # 电气参数字段
                'frequency': 'frequency',
                'power': 'power',
                'kwh': 'kwh',
                'power_factor': 'power_factor',
                'voltage_a': 'voltage_a',
                'voltage_b': 'voltage_b',
                'voltage_c': 'voltage_c',
                'current_a': 'current_a',
                'current_b': 'current_b',
                'current_c': 'current_c',

                # 水力参数字段
                'outlet_pressure': 'outlet_pressure',
                'outlet_flow': 'outlet_flow',
                'head': 'head',
                'inlet_pressure': 'inlet_pressure',
                'outlet_temperature': 'outlet_temperature',

                # 中文别名映射（兼容性）
                '频率': 'frequency',
                '有功功率': 'power',
                'a相有功功率': 'power',
                'b相有功功率': 'power',
                'c相有功功率': 'power',
                '正向有功电度': 'kwh',
                '功率因数': 'power_factor',
                'a相电流': 'current_a',
                'b相电流': 'current_b',
                'c相电流': 'current_c',
                'a相电压': 'voltage_a',
                'b相电压': 'voltage_b',
                'c相电压': 'voltage_c',
                '出口压力': 'outlet_pressure',
                '出口流量': 'outlet_flow',
                '扬程': 'head',
                '入口压力': 'inlet_pressure',
                '出口温度': 'outlet_temperature'
            }

            # 管道数据参数映射（包含数据库中的所有字段）
            self.pipe_param_mapping = {
                # 水力参数字段
                'pressure': 'pressure',
                'flow_rate': 'flow_rate',
                'cumulative_flow': 'cumulative_flow',

                # 中文别名映射（兼容性）
                '压力': 'pressure',
                '流量': 'flow_rate',
                '累计流量': 'cumulative_flow',
                '温度': 'temperature',
                'ph值': 'ph_value',
                '浊度': 'turbidity'
            }

            self._initialized = True

            self.logger.info("单例数据转换器初始化完成")
            self.logger.info(f"  设备ID缓存: {self.device_cache}")
            self.logger.info(f"  时间对齐缓存: {self.time_cache}")

    def set_config_manager(self, config_manager):
        """设置配置管理器"""
        self.config_manager = config_manager
        self.logger.info("[TRANSFORMER] 配置管理器已设置")
    
    def set_database_manager(self, db_manager: DatabaseManager):
        """
        设置数据库管理器
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager
        self.logger.info("数据库管理器已设置")
    
    def preload_device_cache(self) -> int:
        """
        预加载设备缓存
        
        Returns:
            预加载的设备数量
        """
        if self._device_cache_preloaded:
            self.logger.info("设备缓存已预加载，跳过")
            return len(self.device_cache)
        
        if not self.db_manager:
            self.logger.warning("数据库管理器未设置，无法预加载设备缓存")
            return 0
        
        try:
            self.logger.info("开始预加载设备缓存...")
            
            # 查询所有设备
            query = "SELECT device_name, device_id FROM devices"
            results = self.db_manager.execute_query(query)
            
            if not results:
                self.logger.warning("数据库中没有设备数据")
                return 0
            
            # 构建设备映射
            device_mapping = {}
            for row in results:
                device_mapping[row['device_name']] = row['device_id']
            
            # 预加载到缓存
            loaded_count = self.device_cache.preload_devices(device_mapping)
            
            self._device_cache_preloaded = True
            
            self.logger.info(f"设备缓存预加载完成: {loaded_count}个设备")
            return loaded_count
            
        except Exception as e:
            self.logger.error(f"设备缓存预加载失败: {e}")
            return 0
    
    def get_device_id_from_database(self, device_name: str) -> Optional[int]:
        """
        从数据库查询设备ID并缓存结果

        Args:
            device_name: 设备名称

        Returns:
            设备ID，如果不存在返回None
        """
        if not device_name:
            return None

        self._stats['device_queries'] += 1
        self._stats['cache_misses'] += 1

        if not self.db_manager:
            self.logger.error("数据库管理器未设置，无法查询设备ID")
            return None

        try:
            self.logger.debug(f"从数据库查询设备ID: {device_name}")

            query = "SELECT device_id FROM devices WHERE device_name = %s"
            result = self.db_manager.execute_query(query, [device_name])

            if result:
                device_id = result[0]['device_id']

                # 缓存结果到统一缓存
                if self.device_cache:
                    self.device_cache.set_device_id(device_name, device_id)

                self.logger.debug(f"数据库查询成功: {device_name} -> {device_id}")
                return device_id
            else:
                self.logger.warning(f"设备不存在于数据库: {device_name}")
                return None

        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(f"查询设备ID失败: {device_name}, 错误: {e}")
            return None
    
    def align_timestamp(self, time_str: str) -> str:
        """
        对齐时间戳 (带缓存)
        
        Args:
            time_str: 原始时间字符串
            
        Returns:
            对齐后的时间字符串
        """
        if not time_str:
            return time_str
        
        self._stats['time_alignments'] += 1
        
        # 使用时间对齐缓存
        aligned_time = self.time_cache.align_time(time_str)
        
        self.logger.debug(f"时间对齐: {time_str} -> {aligned_time}")
        return aligned_time

    def _map_parameter(self, param_name: str, tag_name: str, target_type: str) -> Optional[str]:
        """
        统一的参数映射方法

        Args:
            param_name: 参数名称
            tag_name: 标签名称
            target_type: 目标类型 ('pump' 或 'pipe')

        Returns:
            映射后的字段名，如果未找到返回None
        """
        if not param_name or not tag_name:
            return None

        text = f"{param_name} {tag_name}".lower()

        # 根据目标类型选择映射表
        if target_type == 'pump':
            mapping_dict = self.pump_param_mapping
        elif target_type == 'pipe':
            mapping_dict = self.pipe_param_mapping
        else:
            self.logger.warning(f"未知的参数映射类型: {target_type}")
            return None

        # 执行映射查找
        for key, mapped in mapping_dict.items():
            if key in text:
                self.logger.debug(f"参数映射成功: {param_name}+{tag_name} -> {mapped} ({target_type})")
                return mapped

        self.logger.debug(f"参数映射失败: {param_name}+{tag_name} 未找到对应字段 ({target_type})")
        return None

    def _map_pump_parameter(self, param_name: str, tag_name: str) -> Optional[str]:
        """映射泵参数 (兼容性方法)"""
        return self._map_parameter(param_name, tag_name, 'pump')

    def _map_pipe_parameter(self, param_name: str, tag_name: str) -> Optional[str]:
        """映射管道参数 (兼容性方法)"""
        return self._map_parameter(param_name, tag_name, 'pipe')
    
    def _map_station_name_to_id(self, station_name: str) -> int:
        """将泵站名称映射为整数ID"""
        # 定义泵站名称到ID的映射
        station_mapping = {
            '二期供水泵房': 1,
            '二期取水泵房': 2,
            '一期供水泵房': 3,
            '一期取水泵房': 4,
            '三期供水泵房': 5,
            '三期取水泵房': 6
        }

        # 返回映射的ID，如果不存在则返回默认值
        station_id = station_mapping.get(station_name, 999)
        self.logger.debug(f"泵站名称映射: {station_name} -> {station_id}")
        return station_id
    
    def transform_to_pump_data(self, raw_data: List[Dict[str, Any]], target_table: str) -> List[Dict[str, Any]]:
        """
        将raw_data格式转换为pump_data格式
        
        Args:
            raw_data: 原始数据列表
            target_table: 目标表名
            
        Returns:
            转换后的数据列表
        """
        try:
            self._stats['transform_requests'] += 1
            self.logger.info(f"开始数据转换: 目标表={target_table}, 原始记录数={len(raw_data)}")

            if target_table == 'pump_data':
                result = self._transform_to_structured_format(raw_data, 'pump')
                self.logger.info(f"pump_data转换完成: {len(raw_data)}条原始记录 -> {len(result)}条泵记录")
                return result
            elif target_table == 'main_pipe_data':
                result = self._transform_to_structured_format(raw_data, 'pipe')
                self.logger.info(f"main_pipe_data转换完成: {len(raw_data)}条原始记录 -> {len(result)}条管道记录")
                return result
            elif target_table in ['raw_data_by_station', 'raw_data_by_device']:
                result = self._transform_to_raw_format(raw_data)
                self.logger.info(f"原格式转换完成: {len(raw_data)}条记录保持原格式")
                return result
            else:
                # 默认保持原格式
                result = self._transform_to_raw_format(raw_data)
                self.logger.info(f"默认格式转换完成: {len(raw_data)}条记录")
                return result

        except Exception as e:
            self._stats['errors'] += 1
            self.logger.error(f"数据转换失败: 目标表={target_table}, 错误={e}")
            import traceback
            self.logger.error(f"转换错误详情: {traceback.format_exc()}")
            raise

    def _transform_to_structured_format(self, raw_data: List[Dict[str, Any]], format_type: str) -> List[Dict[str, Any]]:
        """
        统一的结构化数据转换方法 (消除pump和pipe转换的重复逻辑)

        Args:
            raw_data: 原始数据列表
            format_type: 格式类型 ('pump' 或 'pipe')

        Returns:
            转换后的数据列表
        """
        self.logger.debug(f"开始{format_type}格式转换，输入记录数: {len(raw_data)}")
        structured_records = {}  # 使用字典按device_id和时间分组
        processed_count = 0

        # 🔧 优化：预先查询所有唯一设备名称的ID，避免重复查询
        unique_device_names = set(record.get('device_name', '') for record in raw_data)
        device_id_cache = {}

        self.logger.debug(f"预查询设备ID: {len(unique_device_names)}个唯一设备")
        for device_name in unique_device_names:
            if device_name:
                device_id = self.get_device_id_by_name(device_name)
                device_id_cache[device_name] = device_id if device_id is not None else 1
                self.logger.debug(f"设备ID预查询: {device_name} -> {device_id_cache[device_name]}")

        for record in raw_data:
            processed_count += 1
            if processed_count % 5000 == 0:  # 🔧 减少日志频率，提高性能
                self.logger.debug(f"{format_type}转换进度: {processed_count}/{len(raw_data)}")

            # 从字典中提取字段
            station_id = record.get('station_id', '')
            device_name = record.get('device_name', '')
            param_name = record.get('param_name', '')
            tag_name = record.get('tag_name', '')
            data_time = record.get('data_time', '')
            data_value = record.get('data_value', 0.0)

            # 🔧 使用预查询的设备ID缓存，避免重复数据库查询
            device_id = device_id_cache.get(device_name, 1)

            # 使用缓存对齐时间
            aligned_time = self.align_timestamp(data_time)

            # 创建记录键 (device_id + 时间)
            record_key = f"{device_id}_{aligned_time}"

            # 如果记录不存在，创建新记录
            if record_key not in structured_records:
                # 根据格式类型创建基础记录结构
                if format_type == 'pump':
                    base_record = self._create_pump_base_record(device_id, aligned_time, station_id)
                else:  # pipe
                    base_record = self._create_pipe_base_record(device_id, aligned_time, station_id)

                structured_records[record_key] = base_record

            # 根据参数名称填充对应字段（使用统一参数映射）
            structured_record = structured_records[record_key]
            mapped_field = self._map_parameter(param_name, tag_name, format_type)

            if mapped_field:
                structured_record[mapped_field] = data_value

        result = list(structured_records.values())
        self.logger.debug(f"{format_type}格式转换完成: {len(raw_data)}条原始记录 -> {len(result)}条{format_type}记录")
        return result

    def _create_pump_base_record(self, device_id: int, aligned_time: str, station_id: str) -> Dict[str, Any]:
        """创建泵数据基础记录结构"""
        return {
            'device_id': device_id,
            'data_time': aligned_time,
            'station_id': self._map_station_name_to_id(station_id),
            'flow_rate': 0.0,
            'pressure': 0.0,
            'power': 0.0,
            'efficiency': 0.0,
            'temperature': 0.0,
            'vibration': 0.0,
            'current': 0.0,
            'voltage': 0.0,
            'frequency': 0.0,
            'created_at': datetime.now()
        }

    def _create_pipe_base_record(self, device_id: int, aligned_time: str, station_id: str) -> Dict[str, Any]:
        """创建管道数据基础记录结构"""
        return {
            'device_id': device_id,
            'data_time': aligned_time,
            'station_id': self._map_station_name_to_id(station_id),
            'flow_rate': 0.0,
            'pressure': 0.0,
            'temperature': 0.0,
            'ph_value': 0.0,
            'turbidity': 0.0,
            'chlorine_residual': 0.0,
            'conductivity': 0.0,
            'dissolved_oxygen': 0.0,
            'created_at': datetime.now()
        }

    def _transform_to_pump_format(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """转换为pump_data格式"""
        self.logger.debug(f"开始pump_data格式转换，输入记录数: {len(raw_data)}")

        # 预查询设备ID缓存
        device_id_cache = self._prequery_device_ids(raw_data)

        # 处理原始数据并分组
        pump_records = self._process_raw_data_to_pump_records(raw_data, device_id_cache)

        # 更新泵状态
        self._update_pump_status_for_records(pump_records)

        result = list(pump_records.values())
        self.logger.debug(f"pump_data格式转换完成: {len(raw_data)}条原始记录 -> {len(result)}条泵记录")
        return result

    def _prequery_device_ids(self, raw_data: List[Dict[str, Any]]) -> Dict[str, int]:
        """预查询所有唯一设备名称的ID，避免重复查询"""
        unique_device_names = set(record.get('device_name', '') for record in raw_data)
        device_id_cache = {}

        self.logger.debug(f"预查询设备ID: {len(unique_device_names)}个唯一设备")
        for device_name in unique_device_names:
            if device_name:
                device_id = self.get_device_id_from_database(device_name)
                device_id_cache[device_name] = device_id if device_id is not None else 1
                self.logger.debug(f"设备ID预查询: {device_name} -> {device_id_cache[device_name]}")

        return device_id_cache

    def _process_raw_data_to_pump_records(self, raw_data: List[Dict[str, Any]],
                                        device_id_cache: Dict[str, int]) -> Dict[str, Dict[str, Any]]:
        """处理原始数据并按设备ID和时间分组"""
        pump_records = {}
        processed_count = 0

        for record in raw_data:
            processed_count += 1
            if processed_count % 5000 == 0:
                self.logger.debug(f"pump_data转换进度: {processed_count}/{len(raw_data)}")

            # 提取记录字段
            record_data = self._extract_record_fields(record)
            device_id = device_id_cache.get(record_data['device_name'], 1)
            aligned_time = self.align_timestamp(record_data['data_time'])

            # 创建或获取泵记录
            record_key = f"{device_id}_{aligned_time}"
            if record_key not in pump_records:
                pump_records[record_key] = self._create_pump_record(
                    device_id, record_data['device_name'], record_data['station_id'], aligned_time
                )

            # 映射参数到字段
            self._map_parameter_to_pump_record(
                pump_records[record_key],
                record_data['param_name'],
                record_data['tag_name'],
                record_data['data_value']
            )

        return pump_records

    def _extract_record_fields(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """提取记录中的关键字段"""
        data_quality = record.get('data_quality', 192)
        file_source = record.get('file_source', '')
        batch_id = record.get('batch_id', '')
        # 使用变量避免警告（这些字段为将来功能保留）
        _ = data_quality, file_source, batch_id

        return {
            'station_id': record.get('station_id', ''),
            'device_name': record.get('device_name', ''),
            'param_name': record.get('param_name', ''),
            'tag_name': record.get('tag_name', ''),
            'data_time': record.get('data_time', ''),
            'data_value': record.get('data_value', 0.0)
        }

    def _create_pump_record(self, device_id: int, device_name: str,
                          station_id: str, aligned_time: str) -> Dict[str, Any]:
        """创建新的泵记录"""
        station_id_int = self._map_station_name_to_id(station_id)
        return {
            'device_id': device_id,
            'pump_name': device_name,
            'station_id': station_id_int,
            'data_time': aligned_time,
            # 电气参数字段
            'frequency': None, 'power': None, 'kwh': None, 'power_factor': None,
            'voltage_a': None, 'voltage_b': None, 'voltage_c': None,
            'current_a': None, 'current_b': None, 'current_c': None,
            # 水力参数字段
            'outlet_pressure': None, 'outlet_flow': None, 'head': None,
            'inlet_pressure': None, 'outlet_temperature': None,
            # 状态字段
            'pump_status': 'stopped', 'is_normal': 1,
            'created_at': datetime.now(), 'updated_at': datetime.now()
        }

    def _map_parameter_to_pump_record(self, pump_record: Dict[str, Any],
                                    param_name: str, tag_name: str, data_value: float):
        """将参数映射到泵记录字段"""
        mapped_field = self._map_pump_parameter(param_name, tag_name)
        if mapped_field:
            pump_record[mapped_field] = data_value
            self.logger.debug(f"参数映射成功: {param_name}+{tag_name} -> {mapped_field} = {data_value}")
        else:
            self.logger.debug(f"参数映射失败: {param_name}+{tag_name} 未找到对应字段")

    def _update_pump_status_for_records(self, pump_records: Dict[str, Dict[str, Any]]):
        """为所有泵记录更新状态"""
        for pump_record in pump_records.values():
            pump_record['pump_status'] = self._determine_pump_status_from_record(pump_record)

    def _determine_pump_status_from_record(self, pump_record: Dict[str, Any]) -> str:
        """根据泵记录中的电流和频率判断运行状态

        Args:
            pump_record: 泵记录字典

        Returns:
            str: 'running' 或 'stopped'
        """
        try:
            # 获取频率值（变频泵）
            frequency = pump_record.get('frequency')
            if frequency is not None and frequency > 5.0:
                return 'running'
            elif frequency is not None and frequency <= 1.0:
                return 'stopped'

            # 获取三相电流值
            current_a = pump_record.get('current_a')
            current_b = pump_record.get('current_b')
            current_c = pump_record.get('current_c')

            # 检查是否有有效的电流值
            currents = []
            for current in [current_a, current_b, current_c]:
                if current is not None and current > 0:
                    currents.append(current)

            if currents:
                max_current = max(currents)
                if max_current > 5.0:  # 任一相电流大于5A认为运行
                    return 'running'
                elif max_current < 1.0:  # 所有相电流小于1A认为停止
                    return 'stopped'
                else:
                    return 'running'  # 1-5A之间认为运行（可能是轻载）

            # 如果既没有频率也没有电流，检查功率
            power = pump_record.get('power')
            if power is not None and power > 1.0:
                return 'running'

            # 默认情况：如果无法判断，认为停止
            return 'stopped'

        except Exception as e:
            self.logger.warning(f"判断水泵状态失败: {e}, 默认为停止状态")
            return 'stopped'

    def _transform_to_pipe_format(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """转换为main_pipe_data格式"""
        self.logger.debug(f"开始main_pipe_data格式转换，输入记录数: {len(raw_data)}")
        pipe_records = {}  # 使用字典按device_id和时间分组
        processed_count = 0

        for record in raw_data:
            processed_count += 1
            if processed_count % 1000 == 0:
                self.logger.debug(f"main_pipe_data转换进度: {processed_count}/{len(raw_data)}")
            
            # 从字典中提取字段
            station_id = record.get('station_id', '')
            device_name = record.get('device_name', '')
            param_name = record.get('param_name', '')
            tag_name = record.get('tag_name', '')
            data_time = record.get('data_time', '')
            data_quality = record.get('data_quality', 192)  # 数据质量标识，保留用于将来验证
            data_value = record.get('data_value', 0.0)
            file_source = record.get('file_source', '')  # 文件来源，保留用于追踪
            batch_id = record.get('batch_id', '')  # 批次ID，保留用于批处理

            # 使用变量避免警告（这些字段为将来功能保留）
            _ = data_quality, file_source, batch_id

            # 使用缓存查询device_id
            device_id = self.get_device_id_from_database(device_name)
            if device_id is None:
                self.logger.warning(f"设备ID查询失败，使用默认值: {device_name}")
                device_id = 1  # 默认设备ID

            # 使用缓存对齐时间
            aligned_time = self.align_timestamp(data_time)

            # 创建记录键 (device_id + 时间)
            record_key = f"{device_id}_{aligned_time}"

            # 如果记录不存在，创建新记录
            if record_key not in pipe_records:
                # 将station_id转换为数字类型（数据库中是INT类型）
                station_id_int = self._map_station_name_to_id(station_id)
                pipe_records[record_key] = {
                    'device_id': device_id,
                    'main_pipe_name': device_name,  # 添加main_pipe_name字段
                    'station_id': station_id_int,  # 转换为INT类型
                    'data_time': aligned_time,
                    # 水力参数字段
                    'pressure': None,
                    'flow_rate': None,
                    'cumulative_flow': None,
                    # 预留字段
                    'reserved_decimal_1': None,
                    'reserved_decimal_2': None,
                    'reserved_decimal_3': None,
                    'reserved_decimal_4': None,
                    'reserved_decimal_5': None,
                    'reserved_decimal_6': None,
                    'reserved_decimal_7': None,
                    # 状态字段
                    'normal': 1,
                    'created_at': datetime.now(),
                    'updated_at': None
                }

            # 根据参数名称填充对应字段（使用参数映射）
            pipe_record = pipe_records[record_key]

            # 使用参数映射方法获取数据库字段名
            mapped_field = self._map_pipe_parameter(param_name, tag_name)
            if mapped_field:
                pipe_record[mapped_field] = data_value
                self.logger.debug(f"管道参数映射成功: {param_name}+{tag_name} -> {mapped_field} = {data_value}")
            else:
                self.logger.debug(f"管道参数映射失败: {param_name}+{tag_name} 未找到对应字段")

        result = list(pipe_records.values())
        self.logger.debug(f"main_pipe_data格式转换完成: {len(raw_data)}条原始记录 -> {len(result)}条管道记录")
        return result

    def get_insert_sql(self, target_table: str) -> str:
        """获取目标表的INSERT SQL语句"""
        if target_table == 'pump_data':
            return """
            INSERT INTO pump_data
            (device_id, pump_name, station_id, data_time, frequency, power, kwh, power_factor,
             voltage_a, voltage_b, voltage_c, current_a, current_b, current_c,
             outlet_pressure, outlet_flow, head, inlet_pressure, outlet_temperature,
             pump_status, is_normal, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
        elif target_table == 'main_pipe_data':
            return """
            INSERT INTO main_pipe_data
            (device_id, main_pipe_name, station_id, data_time, pressure, flow_rate, cumulative_flow,
             reserved_decimal_1, reserved_decimal_2, reserved_decimal_3, reserved_decimal_4,
             reserved_decimal_5, reserved_decimal_6, reserved_decimal_7, normal, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
        elif target_table == 'raw_data_by_device':
            return """
            INSERT INTO raw_data_by_device
            (station_id, device_name, param_name, tag_name, data_time,
             data_quality, data_value, file_source, batch_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
        else:
            raise ValueError(f"不支持的目标表: {target_table}")
    
    def _transform_to_raw_format(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """转换为raw_data格式（保持原格式）"""
        result = []

        for record in raw_data:
            # 对齐时间戳
            data_time = record.get('data_time', '')
            aligned_time = self.align_timestamp(data_time)
            
            # 从字典中提取字段，保持原格式但添加created_at
            result.append({
                'station_id': record.get('station_id', ''),
                'device_name': record.get('device_name', ''),
                'param_name': record.get('param_name', ''),
                'tag_name': record.get('tag_name', ''),
                'data_time': aligned_time,
                'data_quality': record.get('data_quality', 192),
                'data_value': record.get('data_value', 0.0),
                'file_source': record.get('file_source', ''),
                'batch_id': record.get('batch_id', ''),
                'created_at': datetime.now()
            })
        
        self.logger.info(f"保持raw_data格式: {len(raw_data)}条记录")
        return result
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息
        
        Returns:
            性能统计信息字典
        """
        device_stats = self.device_cache.get_cache_stats()
        time_stats = self.time_cache.get_cache_stats()
        
        return {
            'transformer_stats': self._stats.copy(),
            'device_cache_stats': device_stats,
            'time_cache_stats': time_stats,
            'overall_cache_hit_rate': (self._stats['cache_hits'] / max(self._stats['device_queries'], 1) * 100),
            'device_cache_preloaded': self._device_cache_preloaded
        }
    
    def optimize_memory(self) -> Dict[str, Any]:
        """
        优化内存使用
        
        Returns:
            优化结果统计
        """
        device_result = self.device_cache.optimize_memory()
        time_result = self.time_cache.optimize_memory()
        
        result = {
            'device_cache_optimization': device_result,
            'time_cache_optimization': time_result,
            'total_memory_saved_mb': device_result['memory_saved_mb'] + time_result['memory_saved_mb']
        }
        
        self.logger.info(f"内存优化完成: 总共节省{result['total_memory_saved_mb']:.2f}MB")
        return result
    
    def clear_all_caches(self) -> Dict[str, int]:
        """
        清空所有缓存

        Returns:
            清理统计信息
        """
        device_count = self.device_cache.clear_cache()
        time_count, format_count = self.time_cache.clear_cache()

        # 重置预加载标志
        self._device_cache_preloaded = False

        # 重置性能统计中的缓存相关项
        with self._lock:
            self._stats['cache_hits'] = 0
            self._stats['cache_misses'] = 0

        result = {
            'device_cache_cleared': device_count,
            'time_cache_cleared': time_count,
            'format_cache_cleared': format_count
        }

        self.logger.info(f"所有缓存已清空: {result}")
        return result

    def cleanup_resources(self):
        """清理所有资源"""
        self.logger.info("开始清理SingletonDataTransformer资源")

        try:
            # 清理所有缓存
            cache_stats = self.clear_all_caches()

            # 清理数据库管理器
            if hasattr(self, 'db_manager') and self.db_manager:
                self.logger.info("清理数据库管理器资源")
                if hasattr(self.db_manager, 'cleanup_resources'):
                    self.db_manager.cleanup_resources()
                self.db_manager = None

            # 清理配置管理器
            if hasattr(self, 'config_manager') and self.config_manager:
                self.logger.info("清理配置管理器资源")
                if hasattr(self.config_manager, 'cleanup_resources'):
                    self.config_manager.cleanup_resources()
                self.config_manager = None

            # 重置所有状态
            with self._lock:
                self._initialized = False
                self._device_cache_preloaded = False
                self._max_existing_device_id = None
                self._next_device_id = 1

                # 重置统计信息
                self._stats = {
                    'transform_requests': 0,
                    'device_queries': 0,
                    'time_alignments': 0,
                    'cache_hits': 0,
                    'cache_misses': 0,
                    'errors': 0
                }

            self.logger.info(f"SingletonDataTransformer资源清理完成: {cache_stats}")

        except Exception as e:
            self.logger.error(f"SingletonDataTransformer资源清理失败: {e}")
            import traceback
            self.logger.error(f"清理错误详情: {traceback.format_exc()}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，自动清理资源"""
        # 使用参数避免警告
        _ = exc_type, exc_val, exc_tb
        self.cleanup_resources()
    
    def __repr__(self) -> str:
        """字符串表示"""
        stats = self.get_performance_stats()
        return (f"SingletonDataTransformer("
                f"device_cache={len(self.device_cache)}, "
                f"time_cache={len(self.time_cache)}, "
                f"hit_rate={stats['overall_cache_hit_rate']:.1f}%)")

    # 🔧 集成设备管理功能
    def _get_max_existing_device_id(self) -> int:
        """
        获取数据库中现有的最大设备ID

        Returns:
            int: 最大设备ID，如果没有记录则返回0
        """
        if self._max_existing_device_id is not None:
            return self._max_existing_device_id

        try:
            if not self.db_manager:
                self.logger.warning("[DEVICE] 数据库管理器未设置，使用默认设备ID")
                self._max_existing_device_id = 0
                return 0

            # 检查devices表是否存在
            check_table_query = """
                SELECT COUNT(*) as count
                FROM information_schema.tables
                WHERE table_schema = DATABASE()
                AND table_name = 'devices'
            """
            result = self.db_manager.execute_query(check_table_query)

            if result and result[0]['count'] > 0:
                # 表存在，获取最大ID
                max_id_query = "SELECT COALESCE(MAX(device_id), 0) as max_id FROM devices"
                result = self.db_manager.execute_query(max_id_query)
                max_id = result[0]['max_id'] if result else 0
                self.logger.info(f"[DEVICE] 从数据库获取最大设备ID: {max_id}")
                self._max_existing_device_id = max_id
                self._next_device_id = max_id + 1
                return max_id
            else:
                # 表不存在，从0开始
                self.logger.info("[DEVICE] devices表不存在，从ID 1开始")
                self._max_existing_device_id = 0
                self._next_device_id = 1
                return 0
        except Exception as e:
            self.logger.error(f"[DEVICE] 获取最大设备ID失败: {e}")
            self._max_existing_device_id = 0
            self._next_device_id = 1
            return 0

    def generate_device_id(self, device_name: str) -> int:
        """
        生成唯一的设备ID，保证不与现有ID重复

        Args:
            device_name: 设备名称

        Returns:
            int: 生成的设备ID
        """
        self.logger.debug(f"[DEVICE] 请求生成设备ID: {device_name}")

        # 先检查设备是否已存在
        existing_id = self.get_device_id_by_name(device_name)
        if existing_id is not None:
            self.logger.debug(f"[DEVICE] 设备已存在，返回现有ID: {device_name} -> {existing_id}")
            return existing_id

        # 确保初始化了设备ID生成器
        if self._next_device_id is None:
            self._get_max_existing_device_id()

        # 生成新的唯一ID
        device_id = self._next_device_id
        self._next_device_id += 1

        # 将新设备ID缓存到设备缓存中
        if self.device_cache:
            self.device_cache.set_device_id(device_name, device_id)

        self.logger.info(f"[DEVICE] 为设备 '{device_name}' 生成新ID: {device_id}")

        return device_id

    def get_device_id_by_name(self, device_name: str) -> Optional[int]:
        """
        根据设备名称获取设备ID（统一使用设备缓存作为单一数据源）

        Args:
            device_name: 设备名称

        Returns:
            Optional[int]: 设备ID，如果不存在则返回None
        """
        if not device_name:
            self.logger.warning("[DEVICE] 设备名称为空")
            return None

        # 统一使用设备缓存作为单一数据源
        if self.device_cache:
            device_id = self.device_cache.get_device_id(device_name)
            if device_id is not None:
                self.logger.debug(f"[DEVICE] 设备缓存命中: {device_name} -> {device_id}")
                return device_id

        # 缓存未命中，从数据库查询并缓存
        return self.get_device_id_from_database(device_name)

    def load_device_mappings(self):
        """从数据库加载现有的设备映射到统一缓存"""
        try:
            if not self.db_manager:
                self.logger.warning("[DEVICE] 数据库管理器未设置，跳过设备映射加载")
                return

            self.logger.info("[DEVICE] 开始从数据库加载设备映射到统一缓存...")

            query = "SELECT device_id, device_name FROM devices"
            results = self.db_manager.execute_query(query)

            # 构建设备映射字典
            device_mapping = {}
            for row in results:
                device_name = row['device_name']
                device_id = row['device_id']
                device_mapping[device_name] = device_id

            # 预加载到设备缓存（统一数据源）
            if self.device_cache and device_mapping:
                loaded_count = self.device_cache.preload_devices(device_mapping)
                self.logger.info(f"[DEVICE] 从数据库加载了 {loaded_count} 个设备映射到统一缓存")
            else:
                self.logger.info(f"[DEVICE] 从数据库查询到 {len(device_mapping)} 个设备映射")
            # 获取缓存统计信息
            if self.device_cache:
                cache_stats = self.device_cache.get_cache_stats()
                cache_size = cache_stats.get('cache_size', 0)
                self.logger.info(f"[DEVICE] 当前缓存设备数: {cache_size}")
            else:
                self.logger.info(f"[DEVICE] 设备缓存未初始化")

        except Exception as e:
            self.logger.error(f"[DEVICE] 加载设备映射失败: {e}")

    def initialize_stations_and_devices(self) -> bool:
        """
        初始化泵站和设备信息
        如果数据库为空，则根据配置文件创建泵站和设备记录

        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("[DEVICE] 开始初始化泵站和设备信息...")

            # 检查数据库中的设备数量
            device_count_query = "SELECT COUNT(*) as count FROM devices"
            device_count_result = self.db_manager.execute_query(device_count_query)
            device_count = device_count_result[0]['count'] if device_count_result else 0

            if device_count == 0:
                # 数据库为空，需要创建基础数据
                self.logger.info("[DEVICE] 数据库中无设备记录，开始创建基础数据...")
                success = self._create_stations_and_devices_from_config()
                if not success:
                    self.logger.error("[DEVICE] 创建基础数据失败")
                    return False
                self.logger.info("[DEVICE] 基础数据创建完成")
            else:
                # 数据库中已有设备数据
                self.logger.info(f"[DEVICE] 数据库中已有 {device_count} 个设备记录")

            # 统一加载设备映射到缓存（无论是新创建还是已存在的数据）
            self.logger.info("[DEVICE] 加载设备映射到统一缓存...")
            self.load_device_mappings()

            # 初始化设备ID生成器
            self._get_max_existing_device_id()

            # 获取最终的设备数量进行确认
            final_device_count_query = "SELECT COUNT(*) as count FROM devices"
            final_device_count_result = self.db_manager.execute_query(final_device_count_query)
            final_device_count = final_device_count_result[0]['count'] if final_device_count_result else 0

            self.logger.info(f"[DEVICE] 泵站和设备信息初始化完成，共加载 {final_device_count} 个设备")
            return True

        except Exception as e:
            self.logger.error(f"[DEVICE] 初始化泵站和设备信息失败: {e}")
            import traceback
            self.logger.error(f"[DEVICE] 错误详情: {traceback.format_exc()}")
            return False

    def _create_stations_and_devices_from_config(self) -> bool:
        """根据配置文件创建泵站和设备记录"""
        try:
            if not self.config_manager:
                self.logger.error("[DEVICE] 配置管理器未设置")
                return False

            self.logger.info("[DEVICE] 开始根据配置文件创建泵站和设备记录...")

            # 获取配置数据
            config_data = self.config_manager.station_configs
            if not config_data:
                self.logger.error("[DEVICE] 无法获取配置数据")
                return False

            # 创建泵站记录
            station_id_mapping = self._create_pump_stations_from_config(config_data)
            if not station_id_mapping:
                return False

            # 创建设备记录
            device_count = self._create_devices_from_config(config_data, station_id_mapping)

            self.logger.info(f"[DEVICE] 基础数据创建完成，共创建 {len(station_id_mapping)} 个泵站，{device_count} 个设备")
            return True

        except Exception as e:
            self.logger.error(f"[DEVICE] 创建基础数据失败: {e}")
            import traceback
            self.logger.error(f"[DEVICE] 错误详情: {traceback.format_exc()}")
            return False

    def _create_pump_stations_from_config(self, config_data: Dict) -> Dict[str, int]:
        """从配置创建泵站记录"""
        station_id_mapping = {}
        station_id = 1

        for station_name in config_data.keys():
            try:
                # 插入泵站记录
                insert_sql = """
                INSERT INTO pump_stations (station_id, station_code, station_name, location, status, created_at, updated_at)
                VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                """
                station_code = f"ST{station_id:03d}"
                params = [station_id, station_code, station_name, station_name, 'active']

                self.db_manager.execute_query(insert_sql, params)
                station_id_mapping[station_name] = station_id
                self.logger.info(f"[DEVICE] 创建泵站: {station_name} (ID: {station_id})")
                station_id += 1

            except Exception as e:
                self.logger.error(f"[DEVICE] 创建泵站 {station_name} 失败: {e}")

        return station_id_mapping

    def _create_devices_from_config(self, config_data: Dict, station_id_mapping: Dict[str, int]) -> int:
        """从配置创建设备记录"""
        device_id = 1

        for station_name, devices in config_data.items():
            station_id_val = station_id_mapping.get(station_name, 1)

            for device_name, device_config in devices.items():
                try:
                    # 确定设备类型
                    device_type = self._determine_device_type(device_config)

                    # 插入设备记录
                    insert_sql = """
                    INSERT INTO devices (device_id, device_name, device_type, station_id, status, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, NOW(), NOW())
                    """
                    params = [device_id, device_name, device_type, station_id_val, 'active']

                    self.db_manager.execute_query(insert_sql, params)
                    self.logger.info(f"[DEVICE] 创建设备: {device_name} (ID: {device_id}, 类型: {device_type})")
                    device_id += 1

                except Exception as e:
                    self.logger.error(f"[DEVICE] 创建设备 {device_name} 失败: {e}")

        return device_id - 1

    def _determine_device_type(self, device_config: Dict) -> str:
        """确定设备类型"""
        raw_device_type = device_config.get('type', ['unknown'])[0]
        # 映射到数据库允许的枚举值
        if raw_device_type in ['Main_pipeline', 'pipeline']:
            return 'pipeline'
        elif raw_device_type == 'pump':
            return 'pump'
        else:
            return 'pump'  # 默认为pump


# 全局函数，用于获取单例实例
def get_data_transformer(db_manager: Optional[DatabaseManager] = None) -> SingletonDataTransformer:
    """
    获取单例数据转换器实例
    
    Args:
        db_manager: 数据库管理器实例
        
    Returns:
        单例数据转换器实例
    """
    transformer = SingletonDataTransformer(db_manager)
    
    # 如果提供了数据库管理器，设置它
    if db_manager is not None:
        transformer.set_database_manager(db_manager)
    
    return transformer


def reset_data_transformer():
    """重置单例数据转换器"""
    SingletonDataTransformer._instance = None
    SingletonDataTransformer._initialized = False
