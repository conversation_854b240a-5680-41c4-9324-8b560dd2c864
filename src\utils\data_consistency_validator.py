#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据一致性验证器
用于验证数据聚合过程中的数据完整性和一致性
"""

from typing import Dict, List, Any
from datetime import datetime
import traceback
import json
from pathlib import Path

from src.utils.logger import get_logger
from src.utils.performance import monitor_performance
from src.utils.exception_handler import handle_exceptions


class DataConsistencyValidator:
    """数据一致性验证器"""
    
    def __init__(self, config_file: str = None):
        """初始化验证器"""
        self.logger = get_logger(__name__)

        # 加载验证配置
        self.config = self._load_validation_config(config_file)

        # 验证统计
        self.validation_stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'data_loss_detected': 0,
            'inconsistency_detected': 0,
            'mapping_failures': 0,
            'time_alignment_failures': 0,
            'value_accuracy_failures': 0,
            'volume_check_failures': 0
        }

        self.logger.info("数据一致性验证器初始化完成")

    def _load_validation_config(self, config_file: str = None) -> Dict[str, Any]:
        """加载验证配置"""
        default_config = {
            'thresholds': {
                'data_retention_rate': 0.85,  # 数据保留率阈值
                'mapping_success_rate': 0.60,  # 参数映射成功率阈值
                'value_accuracy_rate': 0.95,   # 数值准确率阈值
                'time_alignment_tolerance': 0.05  # 时间对齐容忍度
            },
            'value_ranges': {
                'min_value': -1e6,
                'max_value': 1e6,
                'special_ranges': {
                    'pressure': {'min': 0, 'max': 100},
                    'flow': {'min': 0, 'max': 1000},
                    'frequency': {'min': 0, 'max': 100}
                }
            },
            'time_formats': [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y/%m/%d %H:%M:%S'
            ],
            'validation_rules': {
                'enable_strict_time_check': True,
                'enable_business_logic_check': True,
                'enable_historical_comparison': False
            }
        }

        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    # 合并配置
                    default_config.update(user_config)
                    self.logger.info(f"已加载验证配置文件: {config_file}")
            except Exception as e:
                self.logger.warning(f"加载配置文件失败，使用默认配置: {e}")

        return default_config
    
    @monitor_performance("data_consistency_validation")
    @handle_exceptions(context={'operation': 'data_validation'})
    def validate_aggregation_result(self,
                                  original_data: List[Dict[str, Any]],
                                  aggregated_data: List[Dict[str, Any]],
                                  aggregation_type: str) -> Dict[str, Any]:
        """
        验证数据聚合结果的一致性
        
        Args:
            original_data: 原始数据列表
            aggregated_data: 聚合后数据列表
            aggregation_type: 聚合类型 ('pump_data' 或 'main_pipe_data')
            
        Returns:
            验证结果字典
        """
        try:
            self.validation_stats['total_validations'] += 1
            self.logger.info(f"开始验证{aggregation_type}聚合结果一致性")
            
            validation_result = {
                'aggregation_type': aggregation_type,
                'validation_time': datetime.now(),
                'original_count': len(original_data),
                'aggregated_count': len(aggregated_data),
                'tests': {},
                'overall_status': 'UNKNOWN',
                'issues': [],
                'recommendations': []
            }
            
            # 测试1: 数据量合理性检查
            validation_result['tests']['data_volume'] = self._validate_data_volume(
                original_data, aggregated_data
            )
            
            # 测试2: 时间对齐一致性检查
            validation_result['tests']['time_alignment'] = self._validate_time_alignment(
                original_data, aggregated_data
            )
            
            # 测试3: 数据完整性检查
            validation_result['tests']['data_completeness'] = self._validate_data_completeness(
                original_data, aggregated_data, aggregation_type
            )
            
            # 测试4: 参数映射正确性检查
            validation_result['tests']['parameter_mapping'] = self._validate_parameter_mapping(
                original_data, aggregated_data, aggregation_type
            )
            
            # 测试5: 数值准确性检查
            validation_result['tests']['value_accuracy'] = self._validate_value_accuracy(
                original_data, aggregated_data, aggregation_type
            )
            
            # 计算总体状态
            validation_result['overall_status'] = self._calculate_overall_status(
                validation_result['tests']
            )
            
            # 生成建议
            validation_result['recommendations'] = self._generate_recommendations(
                validation_result
            )
            
            # 更新统计
            if validation_result['overall_status'] == 'PASSED':
                self.validation_stats['passed_validations'] += 1
            else:
                self.validation_stats['failed_validations'] += 1
            
            self.logger.info(f"{aggregation_type}聚合验证完成: {validation_result['overall_status']}")
            return validation_result
            
        except Exception as e:
            self.logger.error(f"数据一致性验证失败: {str(e)}")
            self.logger.error(f"验证错误详情: {traceback.format_exc()}")
            self.validation_stats['failed_validations'] += 1
            raise
    
    def _validate_data_volume(self, original_data: List[Dict], aggregated_data: List[Dict]) -> Dict[str, Any]:
        """验证数据量合理性"""
        try:
            original_count = len(original_data)
            aggregated_count = len(aggregated_data)
            
            # 聚合后数据量应该小于等于原始数据量
            volume_reasonable = aggregated_count <= original_count
            
            # 计算压缩率
            compression_ratio = (original_count - aggregated_count) / original_count if original_count > 0 else 0
            
            return {
                'status': 'PASSED' if volume_reasonable else 'FAILED',
                'original_count': original_count,
                'aggregated_count': aggregated_count,
                'compression_ratio': compression_ratio,
                'details': f"数据压缩率: {compression_ratio:.2%}"
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': '数据量验证过程中发生错误'
            }
    
    def _validate_time_alignment(self, original_data: List[Dict], aggregated_data: List[Dict]) -> Dict[str, Any]:
        """验证时间对齐一致性"""
        try:
            # 提取原始数据的时间戳（使用智能时间解析）
            original_times = set()
            for record in original_data:
                data_time = record.get('data_time', '')
                if data_time:
                    normalized_time = self._normalize_timestamp(data_time)
                    if normalized_time:
                        original_times.add(normalized_time)

            # 提取聚合数据的时间戳
            aggregated_times = set()
            for record in aggregated_data:
                data_time = record.get('data_time', '')
                if data_time:
                    normalized_time = self._normalize_timestamp(str(data_time))
                    if normalized_time:
                        aggregated_times.add(normalized_time)

            # 检查时间戳一致性（允许一定的容忍度）
            missing_times = original_times - aggregated_times
            extra_times = aggregated_times - original_times

            # 使用配置化的容忍度
            tolerance = self.config['thresholds']['time_alignment_tolerance']
            total_original_times = len(original_times)
            missing_ratio = len(missing_times) / total_original_times if total_original_times > 0 else 0

            alignment_correct = missing_ratio <= tolerance

            # 更新统计
            if not alignment_correct:
                self.validation_stats['time_alignment_failures'] += 1
            
            return {
                'status': 'PASSED' if alignment_correct else 'FAILED',
                'original_unique_times': len(original_times),
                'aggregated_unique_times': len(aggregated_times),
                'missing_times': list(missing_times)[:10],  # 只显示前10个
                'extra_times': list(extra_times)[:10],      # 只显示前10个
                'missing_ratio': missing_ratio,
                'tolerance': tolerance,
                'details': f"时间对齐一致性: 缺失率{missing_ratio:.2%} (容忍度: {tolerance:.2%})"
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': '时间对齐验证过程中发生错误'
            }
    
    def _validate_data_completeness(self, original_data: List[Dict], aggregated_data: List[Dict], aggregation_type: str) -> Dict[str, Any]:
        """验证数据完整性"""
        # 使用参数避免警告
        _ = aggregation_type
        try:
            # 统计原始数据中的非空值
            original_values = {}
            for record in original_data:
                param_name = record.get('param_name', '')
                data_value = record.get('data_value')
                if param_name and data_value is not None:
                    if param_name not in original_values:
                        original_values[param_name] = 0
                    original_values[param_name] += 1
            
            # 统计聚合数据中的非空值
            aggregated_values = {}
            for record in aggregated_data:
                for key, value in record.items():
                    if key not in ['device_id', 'data_time', 'file_source', 'batch_id', 'created_at'] and value is not None:
                        if key not in aggregated_values:
                            aggregated_values[key] = 0
                        aggregated_values[key] += 1
            
            # 计算数据保留率
            total_original = sum(original_values.values())
            total_aggregated = sum(aggregated_values.values())
            retention_rate = total_aggregated / total_original if total_original > 0 else 0

            # 使用配置化的阈值检查数据丢失
            retention_threshold = self.config['thresholds']['data_retention_rate']
            data_loss = retention_rate < retention_threshold

            # 更新统计
            if data_loss:
                self.validation_stats['data_loss_detected'] += 1
            
            return {
                'status': 'FAILED' if data_loss else 'PASSED',
                'original_value_count': total_original,
                'aggregated_value_count': total_aggregated,
                'retention_rate': retention_rate,
                'original_parameters': original_values,
                'aggregated_fields': aggregated_values,
                'details': f"数据保留率: {retention_rate:.2%}"
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': '数据完整性验证过程中发生错误'
            }
    
    def _validate_parameter_mapping(self, original_data: List[Dict], aggregated_data: List[Dict], aggregation_type: str) -> Dict[str, Any]:
        """验证参数映射正确性"""
        # 使用参数避免警告
        _ = aggregation_type
        try:
            # 提取原始数据中的参数名
            original_params = set()
            for record in original_data:
                param_name = record.get('param_name', '')
                if param_name:
                    original_params.add(param_name)
            
            # 检查聚合数据中有值的字段
            mapped_fields = set()
            for record in aggregated_data:
                for key, value in record.items():
                    if key not in ['device_id', 'data_time', 'file_source', 'batch_id', 'created_at'] and value is not None:
                        mapped_fields.add(key)
            
            # 计算映射成功率
            mapping_success_rate = len(mapped_fields) / len(original_params) if len(original_params) > 0 else 0

            # 使用配置化的阈值检查映射是否充分
            mapping_threshold = self.config['thresholds']['mapping_success_rate']
            mapping_sufficient = mapping_success_rate >= mapping_threshold

            # 更新统计
            if not mapping_sufficient:
                self.validation_stats['mapping_failures'] += 1
            
            return {
                'status': 'PASSED' if mapping_sufficient else 'FAILED',
                'original_parameters': list(original_params),
                'mapped_fields': list(mapped_fields),
                'mapping_success_rate': mapping_success_rate,
                'details': f"参数映射成功率: {mapping_success_rate:.2%}"
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': '参数映射验证过程中发生错误'
            }
    
    def _validate_value_accuracy(self, original_data: List[Dict], aggregated_data: List[Dict], aggregation_type: str) -> Dict[str, Any]:
        """验证数值准确性"""
        # 使用参数避免警告
        _ = original_data, aggregation_type
        try:
            # 使用配置化的范围检查和业务逻辑验证
            value_issues = []
            total_values = 0
            valid_values = 0

            # 获取配置的值范围
            value_ranges = self.config['value_ranges']
            min_value = value_ranges['min_value']
            max_value = value_ranges['max_value']
            special_ranges = value_ranges['special_ranges']

            for record in aggregated_data:
                for key, value in record.items():
                    if key not in ['device_id', 'data_time', 'file_source', 'batch_id', 'created_at'] and value is not None:
                        total_values += 1
                        try:
                            # 检查数值是否为有效数字
                            float_value = float(value)

                            # 使用业务逻辑范围检查
                            is_valid = self._check_value_range(key, float_value, min_value, max_value, special_ranges)

                            if is_valid:
                                valid_values += 1
                            else:
                                value_issues.append(f"{key}={value} 超出业务范围")

                        except (ValueError, TypeError):
                            value_issues.append(f"{key}={value} 不是有效数值")

            # 使用配置化的准确率阈值
            accuracy_threshold = self.config['thresholds']['value_accuracy_rate']
            accuracy_rate = valid_values / total_values if total_values > 0 else 1
            accuracy_good = accuracy_rate >= accuracy_threshold

            # 更新统计
            if not accuracy_good:
                self.validation_stats['value_accuracy_failures'] += 1
            
            return {
                'status': 'PASSED' if accuracy_good else 'FAILED',
                'total_values': total_values,
                'valid_values': valid_values,
                'accuracy_rate': accuracy_rate,
                'accuracy_threshold': accuracy_threshold,
                'value_issues': value_issues[:10],  # 只显示前10个问题
                'details': f"数值准确率: {accuracy_rate:.2%} (阈值: {accuracy_threshold:.2%})"
            }
            
        except Exception as e:
            return {
                'status': 'ERROR',
                'error': str(e),
                'details': '数值准确性验证过程中发生错误'
            }

    def _check_value_range(self, field_name: str, value: float, min_value: float, max_value: float, special_ranges: Dict[str, Dict]) -> bool:
        """检查数值是否在业务逻辑范围内"""
        try:
            # 检查特殊字段的业务范围
            for range_key, range_config in special_ranges.items():
                if range_key.lower() in field_name.lower():
                    return range_config['min'] <= value <= range_config['max']

            # 使用通用范围检查
            return min_value <= value <= max_value

        except Exception as e:
            self.logger.warning(f"范围检查失败: {field_name}={value}, 错误: {e}")
            return True  # 出错时认为有效，避免误报

    def _normalize_timestamp(self, timestamp_str: str) -> str:
        """标准化时间戳格式"""
        try:
            if not timestamp_str:
                return ""

            # 尝试多种时间格式解析
            for time_format in self.config['time_formats']:
                try:
                    dt = datetime.strptime(timestamp_str.strip(), time_format)
                    # 统一返回标准格式
                    return dt.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue

            # 如果所有格式都失败，尝试简单的字符串截取
            if len(timestamp_str) >= 19:
                return timestamp_str[:19]

            return timestamp_str

        except Exception as e:
            self.logger.warning(f"时间标准化失败: {timestamp_str}, 错误: {e}")
            return timestamp_str
    
    def _calculate_overall_status(self, tests: Dict[str, Dict]) -> str:
        """计算总体验证状态"""
        try:
            passed_count = 0
            total_count = 0
            
            for test_name, test_result in tests.items():
                total_count += 1
                if test_result.get('status') == 'PASSED':
                    passed_count += 1
            
            if passed_count == total_count:
                return 'PASSED'
            elif passed_count >= total_count * 0.8:  # 80%以上通过认为基本合格
                return 'WARNING'
            else:
                return 'FAILED'
                
        except Exception:
            return 'ERROR'
    
    def _generate_recommendations(self, validation_result: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        try:
            tests = validation_result.get('tests', {})
            
            # 基于测试结果生成建议
            if tests.get('data_completeness', {}).get('status') == 'FAILED':
                retention_rate = tests['data_completeness'].get('retention_rate', 0)
                recommendations.append(f"数据保留率仅为{retention_rate:.2%}，建议检查参数映射配置")
            
            if tests.get('parameter_mapping', {}).get('status') == 'FAILED':
                mapping_rate = tests['parameter_mapping'].get('mapping_success_rate', 0)
                recommendations.append(f"参数映射成功率仅为{mapping_rate:.2%}，建议完善参数映射规则")
            
            if tests.get('value_accuracy', {}).get('status') == 'FAILED':
                recommendations.append("发现数值准确性问题，建议检查数据类型转换和范围验证")
            
            if tests.get('time_alignment', {}).get('status') == 'FAILED':
                recommendations.append("时间对齐存在问题，建议检查时间格式处理逻辑")
            
            if not recommendations:
                recommendations.append("数据聚合质量良好，建议继续保持当前配置")
                
        except Exception as e:
            recommendations.append(f"生成建议时发生错误: {str(e)}")
        
        return recommendations
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """获取验证统计信息"""
        return {
            'validation_stats': self.validation_stats.copy(),
            'success_rate': (
                self.validation_stats['passed_validations'] / 
                max(self.validation_stats['total_validations'], 1)
            ),
            'last_updated': datetime.now()
        }
    
    def reset_statistics(self):
        """重置验证统计"""
        self.validation_stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'failed_validations': 0,
            'data_loss_detected': 0,
            'inconsistency_detected': 0,
            'mapping_failures': 0
        }
        self.logger.info("验证统计已重置")
