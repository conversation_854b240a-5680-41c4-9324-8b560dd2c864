# 修复文件映射逻辑问题

## 📋 **问题描述**

发现传统文件处理方式中存在严重的映射逻辑错误：
- 流式处理（第1个文件）：正确使用配置文件映射 ✅
- 传统处理（其他111个文件）：错误地重新解析文件路径 ❌

## 🔧 **修复内容**

### 1. 修复 `_process_file` 方法调用
**文件**: `src/handlers/file_processor.py:315`

```python
# 修复前（错误）
processed_data = self._preprocess_data(df, station_id, batch_id, file_path)

# 修复后（正确）
processed_data = self._preprocess_data(df, station_id, device_name, param_name, batch_id, file_path)
```

### 2. 修复 `_preprocess_data` 方法签名
**文件**: `src/handlers/file_processor.py:505-516`

```python
# 修复前（错误）
def _preprocess_data(self, df: pd.DataFrame, station_id: str,
                    batch_id: str, file_path: str) -> List[Dict[str, Any]]:
    # 使用ConfigManager获取文件映射信息
    mapping_info = self._get_file_mapping_info(file_path)
    device_name = mapping_info['device_name']
    param_name = mapping_info['param_name']

# 修复后（正确）
def _preprocess_data(self, df: pd.DataFrame, station_id: str, device_name: str, 
                    param_name: str, batch_id: str, file_path: str) -> List[Dict[str, Any]]:
    self.logger.info(f"[PREPROCESS] 设备名称: {device_name}, 参数名称: {param_name}")
```

### 3. 删除错误的文件名解析方法
**删除的方法**:
- `_extract_device_name()` - 错误的设备名称提取逻辑
- `_extract_param_name_from_filename()` - 错误的参数名称提取逻辑  
- `_extract_param_name()` - 错误的参数名称提取逻辑

## 🎯 **修复原理**

### 问题根源
传统处理方式忽略了已经正确获取的映射信息，而是在 `_preprocess_data` 方法内部重新解析文件路径，导致所有文件都被错误地映射到同一个设备。

### 正确的逻辑流程
1. **第254行**: 正确获取映射信息 `mapping_info = self._get_file_mapping_info(file_path)`
2. **第258-259行**: 正确提取设备和参数信息
   ```python
   device_name = mapping_info.get('device_name', 'unknown')  # 二期供水泵房2#泵
   param_name = mapping_info.get('param_name', 'unknown')    # frequency
   ```
3. **第315行**: 直接传递映射信息，不再重新解析

### 对比流式处理
流式处理是正确的，因为它直接传递了映射信息：
```python
result = self.streaming_processor.process_file_streaming(
    file_path, station_id, device_name, param_name, batch_id, target_table
)
```

## ✅ **预期效果**

修复后，所有文件都将正确映射到对应的设备：
- `_二期_供水泵房_2#加压泵__频率__反馈.csv` → `二期供水泵房2#泵`
- `_二期_供水泵房_3#加压泵__频率__反馈.csv` → `二期供水泵房3#泵`
- `_二期_供水泵房_4#加压泵__频率__反馈.csv` → `二期供水泵房4#泵`
- 等等...

数据库中将有正确的设备ID分布（1-12），而不是全部集中在device_id=1。

## 📝 **修改时间**
2025-08-02 10:30

## 🔍 **影响范围**
- 传统文件处理方式的映射逻辑
- 数据库中设备ID的正确分布
- 所有非流式处理文件的设备映射准确性
