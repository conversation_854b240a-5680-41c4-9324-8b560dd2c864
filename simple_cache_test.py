"""
简单缓存测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("开始简单缓存测试")
print(f"Python版本: {sys.version}")
print(f"工作目录: {os.getcwd()}")
print(f"项目根目录: {project_root}")

try:
    # 测试基本导入
    print("\n测试基本导入...")
    
    from src.utils.logger import get_logger
    print("[OK] 导入logger成功")
    
    from src.utils.device_id_cache import DeviceIDCache
    print("[OK] 导入DeviceIDCache成功")
    
    # 创建简单缓存
    print("\n创建设备ID缓存...")
    cache = DeviceIDCache(max_cache_size=5, memory_limit_mb=1.0)
    print("[OK] 创建DeviceIDCache成功")
    
    # 添加测试数据
    print("\n添加测试数据...")
    cache.set_device_id("test_device_1", 1)
    cache.set_device_id("test_device_2", 2)
    print(f"[OK] 添加测试数据成功，缓存大小: {len(cache._cache)}")
    
    # 测试清理
    print("\n测试缓存清理...")
    cleared_count = cache.clear_cache()
    print(f"[OK] 缓存清理成功，清理了 {cleared_count} 个条目")
    print(f"[OK] 清理后缓存大小: {len(cache._cache)}")
    
    # 测试上下文管理器
    print("\n测试上下文管理器...")
    if hasattr(cache, '__enter__') and hasattr(cache, '__exit__'):
        with DeviceIDCache(max_cache_size=3) as test_cache:
            test_cache.set_device_id("context_test", 999)
            print(f"[OK] 上下文管理器测试成功，缓存大小: {len(test_cache._cache)}")
        print("[OK] 上下文管理器自动清理完成")
    else:
        print("[FAIL] 缓存不支持上下文管理器")
    
    print("\n[SUCCESS] 简单缓存测试全部通过！")
    
except Exception as e:
    print(f"\n[FAIL] 测试失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
