# Augment代码索引配置
# 控制哪些文件和目录被索引，以及索引的深度

indexing:
  # 完全排除的目录（不索引任何内容）
  exclude_directories:
    - "venv"
    - ".venv" 
    - "env"
    - "__pycache__"
    - ".pytest_cache"
    - ".cache"
    - "logs"
    - "build"
    - "dist"
    - ".git"
    - ".vscode"
    - ".idea"
    - "node_modules"
  
  # 完全排除的文件类型
  exclude_file_types:
    - "*.pyc"
    - "*.pyo" 
    - "*.pyd"
    - "*.so"
    - "*.dll"
    - "*.exe"
    - "*.log"
    - "*.bak"
    - "*.backup"
    - "*.old"
    - "*.swp"
    - "*.swo"
    - "*~"
    - ".DS_Store"
    - "Thumbs.db"
    - "desktop.ini"
  
  # 只索引文件名，不索引内容的目录
  name_only_directories:
    - "data"
    - "backup"
    - "archive"
  
  # 只索引文件名，不索引内容的文件类型
  name_only_file_types:
    - "*.csv"
    - "*.xlsx"
    - "*.xls" 
    - "*.json"  # 在data目录下
    - "*.xml"   # 在data目录下
    - "*.txt"   # 在data目录下（数据文件）
    - "*.zip"
    - "*.tar.gz"
    - "*.rar"
    - "*.7z"
  
  # 优先索引的目录（代码目录）
  priority_directories:
    - "src"
    - "config"
    - "tests"
    - "scripts"
    - "utils"
  
  # 优先索引的文件类型
  priority_file_types:
    - "*.py"
    - "*.yaml"
    - "*.yml"
    - "*.md"
    - "*.txt"  # 配置和文档文件
    - "*.sql"
    - "*.sh"
    - "*.bat"
  
  # 索引深度限制
  max_depth:
    default: 10
    data: 2      # data目录只索引2层深度
    logs: 1      # logs目录只索引1层深度
  
  # 文件大小限制（MB）
  max_file_size:
    default: 10    # 默认最大10MB
    code_files: 5  # 代码文件最大5MB
    data_files: 0  # 数据文件不索引内容，大小不限
  
  # 特殊规则
  special_rules:
    # 配置文件总是索引内容
    always_index_content:
      - "config/*.yaml"
      - "config/*.yml" 
      - "*.md"
      - "requirements.txt"
      - "setup.py"
      - "pyproject.toml"
    
    # 敏感文件完全排除
    never_index:
      - "secrets.yaml"
      - ".env"
      - ".env.local"
      - "*.key"
      - "*.pem"
      - "*.p12"
      - "*.pfx"

# 索引性能配置
performance:
  # 并发索引线程数
  max_threads: 4
  
  # 批处理大小
  batch_size: 100
  
  # 索引更新频率（秒）
  update_interval: 300  # 5分钟
  
  # 内存使用限制（MB）
  max_memory: 1024  # 1GB

# 日志配置
logging:
  # 索引操作日志级别
  level: "INFO"
  
  # 是否记录被排除的文件
  log_excluded: false
  
  # 是否记录索引统计
  log_statistics: true
