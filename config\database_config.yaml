cache:
  default_expire: 300
  redis_db: 0
  redis_host: localhost
  redis_port: 6379
  redis_version: 3.0.504
data_loading:
  load_percentage: 0.10
  sampling_method: tail
  test_mode: true
database:
  charset: utf8mb4
  database: pump_optimization
  echo: false
  host: localhost
  max_overflow: 200
  password: '!Qq5707073'
  pool_pre_ping: true
  pool_recycle: 3600
  pool_size: 100
  pool_timeout: 60
  port: 3306
  username: root
logging:
  connection_log_level: INFO
  performance_log_level: INFO
  sql_log_level: WARNING
performance:
  batch_size: 5000
  chunk_size: 50000
  db_batch_size: 5000
  db_max_threads: 4
  max_workers: 8
  preprocess_threads: 4
  timeout: 600
