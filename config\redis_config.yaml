# Redis配置文件 - Windows Redis ********
redis:
  # 基础连接配置
  host: "localhost"
  port: 6379
  password: null  # 如果Redis设置了密码，在这里配置
  db: 0
  decode_responses: true
  socket_timeout: 5
  socket_connect_timeout: 5
  retry_on_timeout: true
  health_check_interval: 30
  
  # 连接池配置
  connection_pool:
    max_connections: 100
    retry_on_timeout: true
    socket_keepalive: true
    socket_keepalive_options: {}
  
  # 缓存配置
  cache:
    default_ttl: 3600  # 默认1小时过期
    max_ttl: 86400     # 最大24小时过期
    key_prefix: "pump_api:"
    
  # Windows Redis ******** 特定配置
  windows_redis:
    version: "********"
    service_name: "Redis"
    data_dir: "D:/phpstudypro/Extensions/redis3.0.504"
    log_file: "redis-server.log"
    
  # 集群配置（如果使用Redis集群）
  cluster:
    enabled: false
    nodes: []
    
  # 哨兵配置（如果使用Redis哨兵）
  sentinel:
    enabled: false
    service_name: "mymaster"
    sentinels: []

# 性能优化配置
performance:
  # 批量操作配置
  batch_size: 1000
  pipeline_size: 100
  
  # 内存优化
  memory_policy: "allkeys-lru"  # 内存不足时的淘汰策略
  max_memory: "1gb"             # 最大内存使用量
  
  # 持久化配置
  save_enabled: true
  save_intervals:
    - "900 1"    # 900秒内至少1个key变化时保存
    - "300 10"   # 300秒内至少10个key变化时保存
    - "60 10000" # 60秒内至少10000个key变化时保存

# 监控配置
monitoring:
  # 慢查询日志
  slowlog_enabled: true
  slowlog_max_len: 128
  slowlog_slower_than: 10000  # 微秒
  
  # 统计信息
  stats_enabled: true
  stats_interval: 60  # 秒
  
  # 健康检查
  health_check_enabled: true
  health_check_interval: 30  # 秒
