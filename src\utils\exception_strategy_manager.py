"""
异常处理策略管理器
负责加载和管理统一的异常处理策略配置
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from dataclasses import dataclass


from src.utils.logger import get_logger
from src.utils.exception_handler import ErrorCategory


@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    base_delay: float = 1.0
    backoff_factor: float = 2.0
    timeout: Optional[float] = None


@dataclass
class OperationStrategy:
    """操作策略配置"""
    retry: RetryConfig
    critical_on_failure: bool = False
    log_slow_operations: bool = False
    slow_operation_threshold: float = 5.0
    rollback_on_failure: bool = False
    backup_on_failure: bool = False


class ExceptionStrategyManager:
    """异常处理策略管理器"""
    
    def __init__(self, config_path: str = "config/exception_handling_strategy.yaml"):
        self.logger = get_logger(__name__)
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.operation_strategies: Dict[str, OperationStrategy] = {}
        self.exception_mappings: Dict[str, List[str]] = {}
        
        self._load_config()
        self._parse_strategies()
        self._parse_exception_mappings()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                self.logger.warning(f"异常处理策略配置文件不存在: {self.config_path}")
                self._create_default_config()
                return
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            self.logger.info(f"异常处理策略配置加载成功: {self.config_path}")
            
        except Exception as e:
            self.logger.error(f"加载异常处理策略配置失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self.config = {
            'global': {
                'default_retry': {
                    'max_attempts': 3,
                    'base_delay': 1.0,
                    'backoff_factor': 2.0
                }
            },
            'operation_strategies': {},
            'exception_mappings': {}
        }
        self.logger.info("使用默认异常处理策略配置")
    
    def _parse_strategies(self):
        """解析操作策略"""
        strategies = self.config.get('operation_strategies', {})
        
        for category, operations in strategies.items():
            for operation, config in operations.items():
                strategy_key = f"{category}.{operation}"
                
                # 解析重试配置
                retry_config_data = config.get('retry', {})
                retry_config = RetryConfig(
                    max_attempts=retry_config_data.get('max_attempts', 3),
                    base_delay=retry_config_data.get('base_delay', 1.0),
                    backoff_factor=retry_config_data.get('backoff_factor', 2.0),
                    timeout=retry_config_data.get('timeout')
                )
                
                # 创建操作策略
                strategy = OperationStrategy(
                    retry=retry_config,
                    critical_on_failure=config.get('critical_on_failure', False),
                    log_slow_operations=config.get('log_slow_queries', False) or 
                                       config.get('log_slow_operations', False),
                    slow_operation_threshold=config.get('slow_query_threshold', 5.0) or
                                           config.get('slow_operation_threshold', 5.0),
                    rollback_on_failure=config.get('rollback_on_failure', False),
                    backup_on_failure=config.get('backup_on_failure', False)
                )
                
                self.operation_strategies[strategy_key] = strategy
        
        self.logger.info(f"解析了 {len(self.operation_strategies)} 个操作策略")
    
    def _parse_exception_mappings(self):
        """解析异常类型映射"""
        mappings = self.config.get('exception_mappings', {})
        
        for category, exception_types in mappings.items():
            self.exception_mappings[category] = exception_types
        
        self.logger.info(f"解析了 {len(self.exception_mappings)} 个异常类型映射")
    
    def get_operation_strategy(self, operation_type: str) -> OperationStrategy:
        """获取操作策略"""
        strategy = self.operation_strategies.get(operation_type)
        
        if strategy is None:
            # 返回默认策略
            default_retry = self.config.get('global', {}).get('default_retry', {})
            strategy = OperationStrategy(
                retry=RetryConfig(
                    max_attempts=default_retry.get('max_attempts', 3),
                    base_delay=default_retry.get('base_delay', 1.0),
                    backoff_factor=default_retry.get('backoff_factor', 2.0)
                )
            )
            self.logger.debug(f"使用默认策略: {operation_type}")
        
        return strategy
    
    def get_retry_config(self, operation_type: str) -> RetryConfig:
        """获取重试配置"""
        strategy = self.get_operation_strategy(operation_type)
        return strategy.retry
    
    def categorize_exception(self, exception: Exception) -> ErrorCategory:
        """根据异常类型分类"""
        exception_type = type(exception).__name__
        full_exception_type = f"{type(exception).__module__}.{exception_type}"
        
        # 检查异常类型映射
        for category, exception_types in self.exception_mappings.items():
            if exception_type in exception_types or full_exception_type in exception_types:
                # 映射到ErrorCategory
                if category == 'database_errors':
                    return ErrorCategory.DATABASE
                elif category == 'file_errors':
                    return ErrorCategory.FILE_IO
                elif category == 'network_errors':
                    return ErrorCategory.NETWORK
                elif category == 'validation_errors':
                    return ErrorCategory.VALIDATION
                elif category == 'business_errors':
                    return ErrorCategory.BUSINESS
                elif category == 'system_errors':
                    return ErrorCategory.SYSTEM
        
        # 默认分类
        return ErrorCategory.SYSTEM
    
    def should_retry(self, operation_type: str, exception: Exception, attempt: int) -> bool:
        """判断是否应该重试"""
        strategy = self.get_operation_strategy(operation_type)
        
        # 检查重试次数
        if attempt >= strategy.retry.max_attempts:
            return False
        
        # 检查异常类型是否适合重试
        category = self.categorize_exception(exception)
        
        # 某些类型的异常不适合重试
        if category in [ErrorCategory.VALIDATION, ErrorCategory.BUSINESS]:
            return False
        
        return True
    
    def get_retry_delay(self, operation_type: str, attempt: int) -> float:
        """计算重试延迟"""
        strategy = self.get_operation_strategy(operation_type)
        return strategy.retry.base_delay * (strategy.retry.backoff_factor ** attempt)
    
    def is_critical_operation(self, operation_type: str) -> bool:
        """判断是否为关键操作"""
        strategy = self.get_operation_strategy(operation_type)
        return strategy.critical_on_failure
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.config.get('monitoring', {})
    
    def get_recovery_config(self) -> Dict[str, Any]:
        """获取恢复策略配置"""
        return self.config.get('recovery_strategies', {})
    
    def reload_config(self):
        """重新加载配置"""
        self.logger.info("重新加载异常处理策略配置")
        self.operation_strategies.clear()
        self.exception_mappings.clear()
        
        self._load_config()
        self._parse_strategies()
        self._parse_exception_mappings()


# 全局策略管理器实例
_global_strategy_manager: Optional[ExceptionStrategyManager] = None


def get_strategy_manager() -> ExceptionStrategyManager:
    """获取全局策略管理器实例"""
    global _global_strategy_manager
    
    if _global_strategy_manager is None:
        _global_strategy_manager = ExceptionStrategyManager()
    
    return _global_strategy_manager


def get_operation_retry_config(operation_type: str) -> RetryConfig:
    """便捷函数：获取操作重试配置"""
    return get_strategy_manager().get_retry_config(operation_type)


def should_retry_operation(operation_type: str, exception: Exception, attempt: int) -> bool:
    """便捷函数：判断操作是否应该重试"""
    return get_strategy_manager().should_retry(operation_type, exception, attempt)


def get_operation_retry_delay(operation_type: str, attempt: int) -> float:
    """便捷函数：获取操作重试延迟"""
    return get_strategy_manager().get_retry_delay(operation_type, attempt)
