"""
统一异常处理框架
提供统一的异常处理、分类、重试和监控功能
"""

import time
import traceback
import functools
from typing import Dict, List, Any, Callable
from datetime import datetime
from collections import defaultdict

from src.utils.logger import get_logger
from src.utils.exception_types import (
    ErrorSeverity, ErrorCategory, ExceptionInfo,
    get_exception_severity, get_exception_category
)


class ExceptionHandler:
    """统一异常处理器"""
    
    def __init__(self, logger_name: str = "ExceptionHandler"):
        self.logger = get_logger(logger_name)
        self.stats = defaultdict(int)
        self.error_history = []
        self.max_history_size = 1000
    
    def handle_exception(self, exception: Exception, context: Dict[str, Any] = None,
                        should_raise: bool = True) -> ExceptionInfo:
        """
        处理异常
        
        Args:
            exception: 异常对象
            context: 异常上下文信息
            should_raise: 是否重新抛出异常
            
        Returns:
            异常信息对象
        """
        # 创建异常信息对象
        exc_info = ExceptionInfo(
            exception=exception,
            category=get_exception_category(exception),
            severity=get_exception_severity(exception),
            timestamp=datetime.now(),
            context=context or {},
            message=str(exception),
            stack_trace=traceback.format_exc()
        )
        
        # 更新统计信息
        self._update_stats(exc_info)
        
        # 记录日志
        self._log_exception(exc_info)
        
        # 添加到历史记录
        self._add_to_history(exc_info)
        
        # 根据严重程度决定处理方式
        if exc_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"严重错误发生，系统可能无法正常运行: {exc_info.message}")
        
        if should_raise:
            raise exception
        
        return exc_info
    
    def _update_stats(self, exc_info: ExceptionInfo):
        """更新异常统计信息"""
        self.stats['total_exceptions'] += 1
        self.stats[f'severity_{exc_info.severity.value}'] += 1
        self.stats[f'category_{exc_info.category.value}'] += 1
        self.stats[f'type_{type(exc_info.exception).__name__}'] += 1
    
    def _log_exception(self, exc_info: ExceptionInfo):
        """记录异常日志"""
        log_message = f"[{exc_info.category.value.upper()}] {exc_info.message}"
        
        if exc_info.context:
            context_str = ", ".join([f"{k}={v}" for k, v in exc_info.context.items()])
            log_message += f" | Context: {context_str}"
        
        if exc_info.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif exc_info.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message)
        elif exc_info.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
    
    def _add_to_history(self, exc_info: ExceptionInfo):
        """添加到异常历史记录"""
        self.error_history.append(exc_info)
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取异常统计信息"""
        return dict(self.stats)
    
    def get_recent_exceptions(self, count: int = 10) -> List[ExceptionInfo]:
        """获取最近的异常记录"""
        return self.error_history[-count:]


# 全局异常处理器实例
_global_exception_handler = ExceptionHandler()


def handle_exceptions(context: Dict[str, Any] = None, should_raise: bool = True,
                     logger_name: str = None):
    """
    异常处理装饰器
    
    Args:
        context: 异常上下文信息
        should_raise: 是否重新抛出异常
        logger_name: 日志器名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            handler = ExceptionHandler(logger_name) if logger_name else _global_exception_handler
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                func_context = {
                    'function': func.__name__,
                    'module': func.__module__,
                    'args': str(args)[:200],  # 限制长度
                    'kwargs': str(kwargs)[:200]
                }
                
                if context:
                    func_context.update(context)
                
                handler.handle_exception(e, func_context, should_raise)
                
                # 如果不重新抛出异常，返回None
                return None
        
        return wrapper
    return decorator


def retry_on_exception(max_attempts: int = 3, delay: float = 1.0, 
                      backoff_factor: float = 2.0, 
                      retry_exceptions: tuple = (Exception,)):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 基础延迟时间
        backoff_factor: 退避因子
        retry_exceptions: 需要重试的异常类型
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(f"retry.{func.__name__}")
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except retry_exceptions as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"函数 {func.__name__} 重试 {max_attempts} 次后仍然失败: {e}")
                        raise
                    
                    wait_time = delay * (backoff_factor ** attempt)
                    logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}, "
                                 f"{wait_time:.2f}秒后重试")
                    time.sleep(wait_time)
        
        return wrapper
    return decorator


def get_exception_handler() -> ExceptionHandler:
    """获取全局异常处理器"""
    return _global_exception_handler
