#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
设备分组处理器
按设备分组处理多个参数文件，实现时间对齐和参数合并
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime
import logging
from collections import defaultdict

from src.utils.performance import monitor_performance
from src.utils.time_alignment_cache import get_time_alignment_cache


class DeviceGroupProcessor:
    """设备分组处理器 - 按设备处理多个参数文件"""
    
    def __init__(self, config_manager=None, logger=None):
        self.config_manager = config_manager
        self.logger = logger or logging.getLogger(__name__)
        self.supported_encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        self.load_percentage = 1.0  # 🔧 改为100%，不再采样

        # 🧠 内存优化配置
        self.memory_config = self._init_memory_config()

        # 🚀 初始化时间对齐缓存
        self.time_cache = get_time_alignment_cache(
            target_format='standard',
            max_cache_size=20000,  # 增大缓存以处理更多设备
            memory_limit_mb=200.0,  # 20MB缓存
            enable_fast_check=True
        )

        # 统计信息
        self.stats = {
            'devices_processed': 0,
            'files_processed': 0,
            'records_merged': 0,
            'time_points_processed': 0,
            'processing_time': 0,
            'memory_peak_mb': 0,
            'large_files_processed': 0,
            'time_cache_hits': 0,
            'time_cache_misses': 0,
            'time_fast_path_hits': 0
        }

    def _init_memory_config(self) -> Dict[str, Any]:
        """初始化内存优化配置"""
        try:
            import psutil

            # 获取系统内存信息
            memory = psutil.virtual_memory()
            available_gb = memory.available / 1024 / 1024 / 1024

            self.logger.info(f"[MEMORY] 系统可用内存: {available_gb:.1f}GB")

            # 根据可用内存动态配置
            if available_gb >= 16:  # 16GB以上
                config = {
                    'large_file_threshold_mb': 200,  # 200MB以上算大文件
                    'chunk_size_large': 50000,      # 大文件分块大小
                    'chunk_size_huge': 25000,       # 超大文件分块大小
                    'max_rows_per_file': 2000000,   # 单文件最大行数
                    'memory_warning_mb': 4000,      # 内存警告阈值
                    'memory_limit_mb': 8000         # 内存限制阈值
                }
                self.logger.info("[MEMORY] 高内存模式: 支持大文件处理")
            elif available_gb >= 8:  # 8-16GB
                config = {
                    'large_file_threshold_mb': 100,
                    'chunk_size_large': 25000,
                    'chunk_size_huge': 10000,
                    'max_rows_per_file': 1000000,
                    'memory_warning_mb': 2000,
                    'memory_limit_mb': 4000
                }
                self.logger.info("[MEMORY] 中等内存模式: 适度优化")
            else:  # 8GB以下
                config = {
                    'large_file_threshold_mb': 50,
                    'chunk_size_large': 10000,
                    'chunk_size_huge': 5000,
                    'max_rows_per_file': 500000,
                    'memory_warning_mb': 1000,
                    'memory_limit_mb': 2000
                }
                self.logger.info("[MEMORY] 低内存模式: 严格限制")

            config['available_memory_gb'] = available_gb
            return config

        except ImportError:
            # 如果没有psutil，使用保守配置
            self.logger.warning("[MEMORY] 无法检测系统内存，使用保守配置")
            return {
                'large_file_threshold_mb': 50,
                'chunk_size_large': 10000,
                'chunk_size_huge': 5000,
                'max_rows_per_file': 500000,
                'memory_warning_mb': 1000,
                'memory_limit_mb': 2000,
                'available_memory_gb': 4.0  # 假设4GB
            }
    
    @monitor_performance("process_all_devices")
    def process_all_devices(self) -> Dict[str, Any]:
        """处理所有设备的数据"""
        start_time = datetime.now()
        
        try:
            # 1. 加载配置文件
            device_groups = self._load_device_groups()
            self.logger.info(f"[DEVICE_GROUP] 加载到 {len(device_groups)} 个设备组")
            
            # 2. 按设备分组处理
            all_results = {}
            
            for device_name, device_config in device_groups.items():
                self.logger.info(f"[DEVICE_GROUP] 开始处理设备: {device_name}")
                
                try:
                    result = self._process_single_device(device_name, device_config)
                    all_results[device_name] = result
                    self.stats['devices_processed'] += 1
                    
                    self.logger.info(f"[DEVICE_GROUP] 设备处理完成: {device_name}, "
                                   f"记录数: {result.get('record_count', 0)}")
                    
                except Exception as e:
                    self.logger.error(f"[DEVICE_GROUP] 设备处理失败: {device_name}, 错误: {e}")
                    all_results[device_name] = {'success': False, 'error': str(e)}
            
            # 3. 汇总结果
            total_records = sum(r.get('record_count', 0) for r in all_results.values())
            processing_time = (datetime.now() - start_time).total_seconds()
            
            self.stats['processing_time'] = processing_time
            
            return {
                'success': True,
                'total_devices': len(device_groups),
                'processed_devices': self.stats['devices_processed'],
                'total_records': total_records,
                'processing_time': processing_time,
                'device_results': all_results,
                'stats': self.stats
            }
            
        except Exception as e:
            self.logger.error(f"[DEVICE_GROUP] 设备分组处理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'stats': self.stats
            }
    
    def _load_device_groups(self) -> Dict[str, Dict]:
        """加载设备分组配置"""
        try:
            config_path = Path('config/data_mapping.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 扁平化设备配置
            device_groups = {}
            
            for station_name, station_config in config.items():
                for device_name, device_config in station_config.items():
                    # 添加站点信息
                    device_config['station_name'] = station_name
                    device_groups[device_name] = device_config
            
            return device_groups
            
        except Exception as e:
            self.logger.error(f"[DEVICE_GROUP] 配置文件加载失败: {e}")
            raise
    
    @monitor_performance("process_single_device")
    def _process_single_device(self, device_name: str, device_config: Dict) -> Dict[str, Any]:
        """处理单个设备的所有参数文件"""
        start_time = datetime.now()
        
        try:
            # 1. 提取设备信息
            station_name = device_config.get('station_name', '')
            device_type = device_config.get('type', ['unknown'])[0]
            
            # 2. 收集所有参数文件
            param_files = self._collect_param_files(device_config)
            self.logger.info(f"[DEVICE] {device_name} 找到 {len(param_files)} 个参数文件")
            
            if not param_files:
                return {'success': False, 'error': '没有找到参数文件', 'record_count': 0}
            
            # 3. 按时间合并所有参数数据
            merged_data = self._merge_device_data_by_time(device_name, param_files)
            
            # 4. 转换为目标表格式
            target_table = self._determine_target_table(device_type)
            formatted_records = self._format_records_for_table(
                merged_data, device_name, station_name, target_table
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return {
                'success': True,
                'device_name': device_name,
                'station_name': station_name,
                'device_type': device_type,
                'target_table': target_table,
                'param_count': len(param_files),
                'record_count': len(formatted_records),
                'time_points': len(merged_data),
                'processing_time': processing_time,
                'records': formatted_records
            }
            
        except Exception as e:
            self.logger.error(f"[DEVICE] 设备处理失败: {device_name}, 错误: {e}")
            return {
                'success': False,
                'device_name': device_name,
                'error': str(e),
                'record_count': 0
            }
    
    def _collect_param_files(self, device_config: Dict) -> Dict[str, str]:
        """收集设备的所有参数文件"""
        param_files = {}
        
        for param_name, file_list in device_config.items():
            if param_name in ['type', 'pump_type', 'station_name']:
                continue
                
            if isinstance(file_list, list) and file_list:
                for file_path in file_list:
                    if isinstance(file_path, str) and file_path.endswith('.csv'):
                        # 检查文件是否存在
                        if Path(file_path).exists():
                            param_files[param_name] = file_path
                        else:
                            self.logger.warning(f"[DEVICE] 文件不存在: {file_path}")
        
        return param_files
    
    @monitor_performance("merge_device_data_by_time")
    def _merge_device_data_by_time(self, device_name: str, param_files: Dict[str, str]) -> Dict[str, Dict]:
        """内存优化的设备数据合并 - 逐文件处理，及时释放内存"""
        time_grouped_data = defaultdict(dict)

        # 逐个处理文件，避免同时加载多个大文件
        for param_name, file_path in param_files.items():
            try:
                self._process_single_param_file(param_name, file_path, time_grouped_data)
            except Exception as e:
                self.logger.error(f"[MERGE] 参数文件处理失败: {param_name} -> {file_path}, 错误: {e}")
                # 确保异常情况下也释放内存
                import gc
                gc.collect()

        self.stats['time_points_processed'] += len(time_grouped_data)
        self.logger.info(f"[MERGE] 设备 {device_name} 时间合并完成: {len(time_grouped_data)} 个时间点")

        return dict(time_grouped_data)

    def _process_single_param_file(self, param_name: str, file_path: str, time_grouped_data: defaultdict):
        """处理单个参数文件"""
        self.logger.debug(f"[MERGE] 处理参数文件: {param_name} -> {file_path}")

        # 读取并验证CSV文件
        df = self._read_csv_with_encoding(file_path)
        if df.empty:
            self.logger.warning(f"[MERGE] 文件为空: {file_path}")
            return

        # 验证和清理数据
        df_valid = self._validate_and_clean_dataframe(df, file_path)
        if df_valid is None:
            return

        # 时间对齐处理
        df_processed = self._align_time_and_extract_values(df_valid)
        del df_valid  # 释放中间DataFrame

        # 更新时间分组数据
        self._update_time_grouped_data(df_processed, param_name, time_grouped_data)
        del df_processed  # 释放处理后的DataFrame

        self.stats['files_processed'] += 1
        self.logger.debug(f"[MERGE] 参数文件处理完成: {param_name}, 内存已释放")

    def _validate_and_clean_dataframe(self, df: pd.DataFrame, file_path: str) -> pd.DataFrame:
        """验证和清理DataFrame"""
        # 确保必要的列存在
        if 'DataTime' not in df.columns:
            self.logger.warning(f"[MERGE] 文件缺少DataTime列: {file_path}")
            del df  # 立即释放内存
            return None

        # 过滤掉空的时间值
        df_valid = df[df['DataTime'].notna() & (df['DataTime'] != '')]
        del df  # 立即释放原始DataFrame内存

        if df_valid.empty:
            self.logger.warning(f"[MERGE] 文件无有效时间数据: {file_path}")
            del df_valid
            return None

        return df_valid

    def _align_time_and_extract_values(self, df_valid: pd.DataFrame) -> pd.DataFrame:
        """时间对齐和数值提取"""
        # 使用时间对齐缓存进行时间标准化
        time_strings = df_valid['DataTime'].astype(str).tolist()

        # 批量时间对齐（使用缓存）
        aligned_times = self.time_cache.align_time_batch(time_strings)
        df_valid['normalized_time'] = aligned_times

        # 统计缓存性能
        self._update_cache_statistics()

        # 向量化处理：提取数值和质量
        df_valid['data_value'] = pd.to_numeric(df_valid.get('DataValue', 0.0), errors='coerce').fillna(0.0)
        df_valid['data_quality'] = pd.to_numeric(df_valid.get('DataQuality', 192), errors='coerce').fillna(192).astype(int)

        # 只保留需要的列，删除其他列
        df_final = df_valid[['normalized_time', 'data_value', 'data_quality']].copy()
        return df_final

    def _update_cache_statistics(self):
        """更新缓存统计信息"""
        cache_stats = self.time_cache.get_stats()
        self.stats['time_cache_hits'] += cache_stats.get('cache_hits', 0)
        self.stats['time_cache_misses'] += cache_stats.get('cache_misses', 0)
        self.stats['time_fast_path_hits'] += cache_stats.get('fast_path_hits', 0)

    def _update_time_grouped_data(self, df_final: pd.DataFrame, param_name: str, time_grouped_data: defaultdict):
        """更新时间分组数据"""
        # 完全向量化：批量更新字典
        time_values = df_final['normalized_time'].values
        data_values = df_final['data_value'].values
        quality_values = df_final['data_quality'].values

        # 批量更新time_grouped_data
        for i in range(len(time_values)):
            normalized_time = time_values[i]
            time_grouped_data[normalized_time][param_name] = {
                'value': float(data_values[i]),
                'quality': int(quality_values[i])
            }

        # 立即释放当前文件的内存
        del time_values, data_values, quality_values
    
    def _read_csv_with_encoding(self, file_path: str) -> pd.DataFrame:
        """内存优化的CSV读取 - 支持分块处理大文件"""
        file_path_obj = Path(file_path)
        file_size_mb = file_path_obj.stat().st_size / (1024 * 1024)

        # 🚨 大文件内存保护 - 使用动态阈值
        threshold_mb = self.memory_config['large_file_threshold_mb']
        if file_size_mb > threshold_mb:
            self.logger.warning(f"[MEMORY] 检测到大文件: {file_path} ({file_size_mb:.1f}MB > {threshold_mb}MB), 启用内存优化模式")
            self.stats['large_files_processed'] += 1
            return self._read_large_csv_chunked(file_path_obj, file_size_mb)

        # 小文件正常处理
        for encoding in self.supported_encodings:
            try:
                # 采样读取
                if self.load_percentage >= 1.0:
                    df = pd.read_csv(
                        file_path_obj,
                        encoding=encoding,
                        low_memory=True,  # 启用低内存模式
                        na_values=['', 'NULL', 'null', 'NaN', 'nan']
                    )
                else:
                    # 头部采样
                    total_lines = sum(1 for _ in open(file_path_obj, 'r', encoding=encoding)) - 1
                    sample_size = max(1, int(total_lines * self.load_percentage))
                    df = pd.read_csv(
                        file_path_obj,
                        encoding=encoding,
                        nrows=sample_size,
                        low_memory=True,
                        na_values=['', 'NULL', 'null', 'NaN', 'nan']
                    )

                return df

            except Exception as e:
                continue

        raise Exception(f"无法读取文件: {file_path}")

    def _read_large_csv_chunked(self, file_path: Path, file_size_mb: float) -> pd.DataFrame:
        """分块读取大CSV文件，减少内存占用"""
        try:
            # 🧠 根据内存配置和文件大小动态调整分块大小
            if file_size_mb > 500:  # 超大文件
                chunk_size = self.memory_config['chunk_size_huge']
            else:  # 大文件
                chunk_size = self.memory_config['chunk_size_large']

            self.logger.info(f"[MEMORY] 分块读取大文件: {file_path.name}, 块大小: {chunk_size:,}行")

            chunks = []
            total_rows = 0

            for encoding in self.supported_encodings:
                try:
                    # 分块读取
                    chunk_reader = pd.read_csv(
                        file_path,
                        encoding=encoding,
                        chunksize=chunk_size,
                        low_memory=True,
                        na_values=['', 'NULL', 'null', 'NaN', 'nan']
                    )

                    for i, chunk in enumerate(chunk_reader):
                        # 只保留必要的列
                        if 'DataTime' in chunk.columns and 'DataValue' in chunk.columns:
                            chunk = chunk[['DataTime', 'DataValue', 'DataQuality']].copy()

                            # 立即清理无效数据
                            chunk = chunk.dropna(subset=['DataTime'])
                            chunk = chunk[chunk['DataTime'] != '']

                            if not chunk.empty:
                                chunks.append(chunk)
                                total_rows += len(chunk)

                                # 🧠 内存保护：使用配置的最大行数限制
                                max_rows = self.memory_config['max_rows_per_file']
                                if total_rows > max_rows:
                                    self.logger.warning(f"[MEMORY] 达到内存保护限制，停止读取: {total_rows:,}行 (限制: {max_rows:,})")
                                    break

                        # 每处理10个块报告一次进度
                        if (i + 1) % 10 == 0:
                            self.logger.debug(f"[MEMORY] 已处理 {i+1} 个数据块，累计 {total_rows:,} 行")

                    # 合并所有块
                    if chunks:
                        result_df = pd.concat(chunks, ignore_index=True)
                        self.logger.info(f"[MEMORY] 大文件读取完成: {len(result_df):,}行, 内存使用优化")
                        return result_df

                except Exception as e:
                    self.logger.debug(f"[MEMORY] 编码 {encoding} 读取失败: {e}")
                    continue

            raise Exception(f"无法读取大文件: {file_path}")

        except Exception as e:
            self.logger.error(f"[MEMORY] 分块读取失败: {e}")
            raise
    
    def _normalize_time(self, time_str: str) -> str:
        """标准化时间格式"""
        try:
            # 尝试解析时间
            dt = pd.to_datetime(time_str)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return time_str
    
    def _determine_target_table(self, device_type: str) -> str:
        """确定目标表"""
        if device_type.lower() in ['pump']:
            return 'pump_data'
        elif device_type.lower() in ['main_pipeline', 'pipeline']:
            return 'main_pipe_data'
        else:
            return 'raw_data_by_device'
    
    def _format_records_for_table(self, merged_data: Dict[str, Dict], 
                                 device_name: str, station_name: str, 
                                 target_table: str) -> List[Dict[str, Any]]:
        """格式化记录为目标表格式"""
        records = []
        
        for data_time, params in merged_data.items():
            if target_table == 'pump_data':
                record = self._format_pump_record(data_time, params, device_name, station_name)
            elif target_table == 'main_pipe_data':
                record = self._format_pipe_record(data_time, params, device_name, station_name)
            else:
                record = self._format_raw_record(data_time, params, device_name, station_name)
            
            if record:
                records.append(record)
        
        self.stats['records_merged'] += len(records)
        return records
    
    def _format_pump_record(self, data_time: str, params: Dict, 
                           device_name: str, station_name: str) -> Dict[str, Any]:
        """格式化泵记录"""
        # 映射泵站名称到ID
        station_mapping = {
            '二期供水泵房': 1, '二期取水泵房': 2,
            '一期供水泵房': 3, '一期取水泵房': 4,
            '三期供水泵房': 5, '三期取水泵房': 6
        }
        station_id = station_mapping.get(station_name, 999)
        
        # 创建泵记录
        record = {
            'device_id': None,  # 需要后续查询
            'pump_name': device_name,
            'station_id': station_id,
            'data_time': data_time,
            # 电气参数
            'frequency': params.get('frequency', {}).get('value'),
            'power': params.get('power', {}).get('value'),
            'kwh': params.get('kwh', {}).get('value'),
            'power_factor': params.get('power_factor', {}).get('value'),
            'voltage_a': params.get('voltage_a', {}).get('value'),
            'voltage_b': params.get('voltage_b', {}).get('value'),
            'voltage_c': params.get('voltage_c', {}).get('value'),
            'current_a': params.get('current_a', {}).get('value'),
            'current_b': params.get('current_b', {}).get('value'),
            'current_c': params.get('current_c', {}).get('value'),
            # 水力参数
            'outlet_pressure': params.get('outlet_pressure', {}).get('value'),
            'outlet_flow': params.get('outlet_flow', {}).get('value'),
            'head': params.get('head', {}).get('value'),
            'inlet_pressure': params.get('inlet_pressure', {}).get('value'),
            'outlet_temperature': params.get('outlet_temperature', {}).get('value'),
            # 状态字段 - 根据电流和频率动态判断
            'pump_status': self._determine_pump_status(params),
            'is_normal': 1,
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
        
        return record
    
    def _format_pipe_record(self, data_time: str, params: Dict, 
                           device_name: str, station_name: str) -> Dict[str, Any]:
        """格式化管道记录"""
        # 映射泵站名称到ID
        station_mapping = {
            '二期供水泵房': 1, '二期取水泵房': 2,
            '一期供水泵房': 3, '一期取水泵房': 4,
            '三期供水泵房': 5, '三期取水泵房': 6
        }
        station_id = station_mapping.get(station_name, 999)
        
        # 创建管道记录
        record = {
            'device_id': None,  # 需要后续查询
            'main_pipe_name': device_name,
            'station_id': station_id,
            'data_time': data_time,
            # 水力参数
            'pressure': params.get('pressure', {}).get('value'),
            'flow_rate': params.get('flow_rate', {}).get('value'),
            'cumulative_flow': params.get('cumulative_flow', {}).get('value'),
            # 预留字段
            'reserved_decimal_1': None,
            'reserved_decimal_2': None,
            'reserved_decimal_3': None,
            'reserved_decimal_4': None,
            'reserved_decimal_5': None,
            'reserved_decimal_6': None,
            'reserved_decimal_7': None,
            # 状态字段
            'normal': 1,
            'created_at': datetime.now(),
            'updated_at': None
        }
        
        return record
    
    def _format_raw_record(self, data_time: str, params: Dict, 
                          device_name: str, station_name: str) -> Dict[str, Any]:
        """格式化原始记录"""
        # 为每个参数创建一条记录
        records = []
        for param_name, param_data in params.items():
            record = {
                'station_id': station_name,
                'device_name': device_name,
                'param_name': param_name,
                'tag_name': f"{device_name}_{param_name}",
                'data_time': data_time,
                'data_quality': param_data.get('quality', 192),
                'data_value': param_data.get('value', 0.0),
                'file_source': '',
                'batch_id': '',
                'created_at': datetime.now()
            }
            records.append(record)
        
        return records

    def _determine_pump_status(self, params: Dict) -> str:
        """根据电流和频率判断水泵运行状态

        判断逻辑：
        1. 如果有频率参数且频率 > 5Hz，认为运行中
        2. 如果没有频率参数，检查电流：
           - 三相电流任一相 > 5A，认为运行中
           - 否则认为停止
        3. 如果频率和电流都没有或都为0，认为停止

        Args:
            params: 参数字典，包含各种传感器数据

        Returns:
            str: 'running' 或 'stopped'
        """
        try:
            # 获取频率值（变频泵）
            frequency = params.get('frequency', {}).get('value')
            if frequency is not None:
                frequency = float(frequency)
                if frequency > 5.0:  # 频率大于5Hz认为运行
                    return 'running'
                elif frequency <= 1.0:  # 频率小于等于1Hz认为停止
                    return 'stopped'

            # 获取三相电流值
            current_a = params.get('current_a', {}).get('value')
            current_b = params.get('current_b', {}).get('value')
            current_c = params.get('current_c', {}).get('value')

            # 检查是否有有效的电流值
            currents = []
            for current in [current_a, current_b, current_c]:
                if current is not None:
                    try:
                        current_val = float(current)
                        if current_val > 0:  # 只考虑正值
                            currents.append(current_val)
                    except (ValueError, TypeError):
                        continue

            if currents:
                max_current = max(currents)
                if max_current > 5.0:  # 任一相电流大于5A认为运行
                    return 'running'
                elif max_current < 1.0:  # 所有相电流小于1A认为停止
                    return 'stopped'
                else:
                    return 'running'  # 1-5A之间认为运行（可能是轻载）

            # 如果既没有频率也没有电流，检查功率
            power = params.get('power', {}).get('value')
            if power is not None:
                try:
                    power_val = float(power)
                    if power_val > 1.0:  # 功率大于1kW认为运行
                        return 'running'
                    else:
                        return 'stopped'
                except (ValueError, TypeError):
                    pass

            # 默认情况：如果无法判断，认为停止
            return 'stopped'

        except Exception as e:
            self.logger.warning(f"判断水泵状态失败: {e}, 默认为停止状态")
            return 'stopped'

    def get_time_cache_stats(self) -> Dict[str, Any]:
        """获取时间缓存统计信息"""
        if self.time_cache:
            cache_stats = self.time_cache.get_stats()
            return {
                'cache_enabled': True,
                'cache_size': len(self.time_cache._time_cache),
                'max_cache_size': self.time_cache.max_cache_size,
                'total_requests': cache_stats.get('total_requests', 0),
                'cache_hits': cache_stats.get('cache_hits', 0),
                'cache_misses': cache_stats.get('cache_misses', 0),
                'fast_path_hits': cache_stats.get('fast_path_hits', 0),
                'hit_rate': cache_stats.get('hit_rate', 0.0),
                'fast_path_rate': cache_stats.get('fast_path_rate', 0.0),
                'memory_usage_mb': cache_stats.get('memory_usage_mb', 0.0)
            }
        else:
            return {
                'cache_enabled': False,
                'error': '时间缓存未初始化'
            }
